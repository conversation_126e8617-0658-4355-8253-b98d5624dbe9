﻿using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.Sells;
using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO;

public class SellDTO
{
    public Guid Id { get; set; }
    public int InvoiceNo { get; set; } = 0;
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public DateOnly Date { get; set; }
    public Guid? TreasuryId { get; set; }
    public decimal Paid { get; set; } = decimal.Zero;
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public Guid? StoreId { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
    public decimal Total { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
    public decimal DiscountValue { get; set; } = decimal.Zero;
    public bool IsDiscountValue { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
    public decimal FinalTotal { get; set; }
    public string? UserId { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public ClientCMDTO Client { get; set; }
    public List<SellItemDTO>? SellItemDTOs { get; set; } = new List<SellItemDTO>();



}
