﻿using Microsoft.EntityFrameworkCore;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول جميع العملاء
    /// </summary>
    [Index("Name", IsUnique = true)]
    public class Client : BaseEntity
    {
        public string Name { get; set; }
        public int? Phone { get; set; }
        public string? Address { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
        public decimal Balance { get; set; }
        public bool IsSupplier { get; set; }
        public bool IsCustomer { get; set; }
        public virtual ICollection<Receipt> Receipts { get; set; } = new List<Receipt>();
        public virtual ICollection<Purchase> Purchases { get; set; } = new List<Purchase>();
        public virtual ICollection<Sell> Sells { get; set; } = new List<Sell>();
    }
}
