﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.ModelsDTO.Finacnial;
using Financial = PosGTech.Models.Financial;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FinancialsController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("getAllFinancials")]
        public async Task<IActionResult> GetAllFinancials()
        {
            var Financials = maper.Map<IEnumerable<Financial>, IEnumerable<FinancialDTO>>(await unitOfWork.Financial.GetAll());
            return Ok(Financials);
        }

    }
}
