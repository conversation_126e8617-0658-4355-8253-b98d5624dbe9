using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.ShopSettings;
using PosGTech.Web.Services.Interfaces;
using System.Text.Json;

namespace PosGTech.Web.Pages.Pages.Stores
{
    public partial class UpsertStoreSettings
    {
        [CascadingParameter] MudDialogInstance MudDialog { get; set; }
        [Parameter] public Guid id { get; set; }
        
        [Inject] IGRepository<ShopSettingsDTO> _ShopSettings { get; set; }


        private ShopSettingsDTO shopSettings = new();
        private IBrowserFile? logoFile;
        private bool isLoading = false;
        private string? logoPreviewUrl;

        protected override async Task OnInitializedAsync()
        {
            if (id != Guid.Empty)
            {
                await LoadShopSettings();
            }
        }

        private async Task LoadShopSettings()
        {
            try
            {
                var res = await _ShopSettings.GetByIdAsync("ShopSettings/getShopSettingsById", id);
                if (res.Item2 == null && res.Item1 != null)
                {
                    shopSettings = res.Item1;
                }
                else
                {
                    _snackbar.Add("خطأ في تحميل البيانات", Severity.Error);
                    MudDialog.Cancel();
                }
            }
            catch (Exception ex)
            {
                _snackbar.Add($"خطأ في تحميل البيانات: {ex.Message}", Severity.Error);
                MudDialog.Cancel();
            }
        }

        private void OnLogoFileChanged(IBrowserFile file)
        {
            try
            {
                // التحقق من نوع الملف
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
                var fileExtension = Path.GetExtension(file.Name).ToLowerInvariant();

                if (!allowedExtensions.Contains(fileExtension))
                {
                    _snackbar.Add("نوع الملف غير مدعوم. يرجى استخدام صور بصيغة JPG, PNG, GIF أو BMP", Severity.Error);
                    return;
                }

                // التحقق من حجم الملف (5 ميجابايت كحد أقصى)
                if (file.Size > 5 * 1024 * 1024)
                {
                    _snackbar.Add("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت", Severity.Error);
                    return;
                }

                logoFile = file;

                // إنشاء معاينة للصورة (بدون await)
                _ = CreateImagePreview(file);

                _snackbar.Add($"تم اختيار الملف: {file.Name}", Severity.Success);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                _snackbar.Add($"خطأ في اختيار الملف: {ex.Message}", Severity.Error);
            }
        }

        private async Task CreateImagePreview(IBrowserFile file)
        {
            try
            {
                var buffer = new byte[file.Size];
                await file.OpenReadStream().ReadAsync(buffer);
                logoPreviewUrl = $"data:{file.ContentType};base64,{Convert.ToBase64String(buffer)}";
                StateHasChanged();
            }
            catch (Exception)
            {
                logoPreviewUrl = null;
            }
        }

        private void RemoveLogoFile()
        {
            logoFile = null;
            logoPreviewUrl = null;
            _snackbar.Add("تم إزالة الملف المختار", Severity.Info);
            StateHasChanged();
        }

        private async Task Upsert()
        {
            if (isLoading) return;

            try
            {
                isLoading = true;
                StateHasChanged();

                ResponseVM response;

                if (logoFile != null)
                {
                    // رفع مع ملف
                    if (id == Guid.Empty)
                    {
                        response = await _ShopSettings.InsertWithFile("ShopSettings/insertShopSettingsWithFile", shopSettings, logoFile, "shopSettings");
                    }
                    else
                    {
                        response = await _ShopSettings.UpdateWithFile("ShopSettings/updateShopSettingsWithFile", shopSettings, id, logoFile, "shopSettings");
                    }
                }
                else
                {
                    // بدون ملف
                    if (id == Guid.Empty)
                    {
                        response = await _ShopSettings.Insert("ShopSettings/insertShopSettings", shopSettings);
                    }
                    else
                    {
                        response = await _ShopSettings.Update("ShopSettings/updateShopSettings", shopSettings, id);
                    }
                }

                if (response.State)
                {
                    _snackbar.Add(response.Message, Severity.Success);

                    // تحديث البيانات بعد الحفظ الناجح لإظهار الشعار الجديد
                    if (logoFile != null)
                    {
                        // إعادة تحميل البيانات لإظهار الشعار المحفوظ
                        await LoadShopSettings();
                        logoFile = null; // إعادة تعيين ملف الشعار
                        logoPreviewUrl = null; // إعادة تعيين معاينة الشعار
                        StateHasChanged();
                    }

                    MudDialog.Close(DialogResult.Ok(true));
                }
                else
                {
                    _snackbar.Add(response.Message, Severity.Error);
                }
            }
            catch (Exception ex)
            {
                _snackbar.Add($"خطأ في حفظ البيانات: {ex.Message}", Severity.Error);
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }



        private void Cancel() => MudDialog.Cancel();

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }



        /// <summary>
        /// تحويل مسار الشعار النسبي إلى URL كامل للـ API
        /// </summary>
        /// <param name="logoPath">مسار الشعار النسبي</param>
        /// <returns>URL كامل للصورة</returns>
        private string GetFullLogoUrl(string? logoPath)
        {
            if (string.IsNullOrEmpty(logoPath))
            {
                return string.Empty;
            }

            // إذا كان المسار يبدأ بـ http أو https، فهو مسار كامل
            if (logoPath.StartsWith("http://") || logoPath.StartsWith("https://"))
            {
                return logoPath;
            }

            // إذا كان المسار يبدأ بـ data:، فهو base64
            if (logoPath.StartsWith("data:"))
            {
                return logoPath;
            }

            // URL الأساسي للـ API (يجب أن يتطابق مع إعدادات WebPages.cs)
            var apiBaseUrl = "https://localhost:7282";

            // تحويل المسار النسبي إلى URL كامل للـ API
            // مثال: /logo/image.png -> https://localhost:7282/logo/image.png
            var fullPath = logoPath.StartsWith("/") ? logoPath : "/" + logoPath;
            return apiBaseUrl + fullPath;
        }
    }
}
