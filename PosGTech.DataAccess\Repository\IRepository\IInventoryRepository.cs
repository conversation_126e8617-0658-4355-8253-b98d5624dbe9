﻿using AutoMapper;
using PosGTech.Models;
using PosGTech.ModelsDTO.Inventories;

namespace PosGTech.DataAccess.Repository.IRepository;

public interface IInventoryRepository : IRepository<Inventory>
{
    Task AddInventory(Inventory inventory);
    Task DeleteInventory(Inventory inventory);
    Task UpdateInventory(Inventory inventory, Inventory model);
    Task<InventoryDTO> GetInventoryById(IMapper mapper, Guid id);

}
