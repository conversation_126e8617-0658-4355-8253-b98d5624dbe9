﻿<MudCard Elevation="0" Class="receipt-card mb-4">
    <MudCardHeader>
        <MudText Typo="Typo.h6" Class="receipt-header">فاتورة المشتريات</MudText>
    </MudCardHeader>
    <MudCardContent>
        <MudGrid Spacing="3">
            <MudItem xs="12" sm="6" md="3">
                <MudAutocomplete T="UserCMDTO"
                                 Label="المستخدم"
                                 @bind-Value="_crruntUser"
                                 ResetValueOnEmptyText="true"
                                 CoerceText="true"
                                 CoerceValue="true"
                                 SearchFunc="@SearchUser"
                                 ToStringFunc="@(e=> e==null?null : $"{e.Name}")"
                                 Variant="Variant.Outlined"
                                 Adornment="Adornment.Start"
                                 AdornmentIcon="@Icons.Material.Filled.AccountCircle"
                                 Class="input-group" />
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudAutocomplete T="UserTreasuryCMDTO"
                                 Label="الخزينة"
                                 @bind-Value="Receipt.UserTreasury"
                                 Required="true"
                                 ResetValueOnEmptyText="true"
                                 CoerceText="true"
                                 CoerceValue="true"
                                 SearchFunc="@SearchUserTreasury"
                                 ToStringFunc="@(e=> e==null?null : $"{e.Treasury.Name}")"
                                 For="@(() => Receipt.UserTreasury)"
                                 Variant="Variant.Outlined"
                                 Adornment="Adornment.Start"
                                 AdornmentIcon="@Icons.Material.Filled.AccountBalance"
                                 Class="input-group" />
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudNumericField HideSpinButtons="true"
                                 T="int"
                                 @bind-Value="@Receipt.Purchase.InvoiceNo"
                                 Immediate="true"
                                 Label="رقم الفاتورة"
                                 OnKeyDown="OnKeyDown"
                                 Variant="Variant.Outlined"
                                 Adornment="Adornment.Start"
                                 AdornmentIcon="@Icons.Material.Filled.Receipt"
                                 IconSize="Size.Medium"
                                 Class="input-group" />
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudTextField Value="Receipt.Purchase.Client.Name"
                              Disabled="true"
                              Label="اسم المورد"
                              Required="true"
                              Variant="Variant.Outlined"
                              Adornment="Adornment.Start"
                              AdornmentIcon="@Icons.Material.Filled.Person"
                              Class="input-group" />
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudTextField Value="@((Receipt.Purchase.FinalTotal - Receipt.Purchase.Paid))"
                              Disabled="true"
                              Label="القيمة المتبقية من الفاتورة"
                              Variant="Variant.Outlined"
                              Adornment="Adornment.Start"
                              AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                              Class="input-group" />
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudNumericField HideSpinButtons="true"
                                 T="decimal"
                                 Label="قيمة الإيصال"
                                 Min="0"
                                 Value="Receipt.Value"
                                 ValueChanged="ValueChange"
                                 For="@(() => Receipt.Value)"
                                 Variant="Variant.Outlined"
                                 Adornment="Adornment.Start"
                                 AdornmentIcon="@Icons.Material.Filled.AttachMoney"
                                 IconSize="Size.Medium"
                                 Class="input-group" />
            </MudItem>
        </MudGrid>
    </MudCardContent>
</MudCard>
<style>
    /* ألوان النظام */
    :root {
        --primary-color: #1976d2;
        --secondary-color: #f50057;
        --background-color: #f5f5f5;
        --card-color: #ffffff;
        --text-color: #333333;
        --border-color: #e0e0e0;
        --hover-color: #f0f7ff;
    }

    /* تخصيص العناصر المشتركة */
    .receipt-card {
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }

        .receipt-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
        }

    .receipt-form-container {
        background-color: var(--background-color);
        padding: 16px;
        border-radius: 12px;
    }

    .input-group {
        margin-bottom: 12px;
    }

    .receipt-header {
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 16px;
        margin-bottom: 16px;
        font-weight: bold;
    }

    /* تعزيز تجربة المستخدم */
    .mud-input:focus-within {
        border-color: var(--primary-color);
    }

    .form-section {
        margin-bottom: 24px;
    }

    .mud-select-text, .mud-input {
        font-size: 0.95rem;
    }

    /* تعديل حجم الأيقونات */
    .mud-icon-root {
        font-size: 1.2rem;
    }

    /* أنماط الأزرار */
    .action-button {
        border-radius: 6px;
        padding: 8px 16px;
        transition: all 0.2s ease;
    }

        .action-button:hover {
            transform: translateY(-2px);
        }
</style>


@* UpsertReceiptPurchases.razor *@
@* <MudGrid Spacing="3">
    <MudItem xs="12" sm="6" md="3">
        <MudAutocomplete T="UserCMDTO" 
                         Label="المستخدم" 
                         @bind-Value="_crruntUser"
                         ResetValueOnEmptyText="true"
                         CoerceText="true" 
                         CoerceValue="true"
                         SearchFunc="@SearchUser" 
                         ToStringFunc="@(e=> e==null?null : $"{e.Name}")"
                         Variant="Variant.Outlined"
                         Adornment="Adornment.End"
                         AdornmentIcon="@Icons.Material.Filled.AccountCircle"
                         />
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudAutocomplete T="UserTreasuryCMDTO" 
                         Label="الخزينة" 
                         @bind-Value="Receipt.UserTreasury" 
                         Required="true"
                         ResetValueOnEmptyText="true"
                         CoerceText="true" 
                         CoerceValue="true"
                         SearchFunc="@SearchUserTreasury" 
                         ToStringFunc="@(e=> e==null?null : $"{e.Treasury.Name}")" 
                         For="@(() => Receipt.UserTreasury)"
                         Variant="Variant.Outlined"
                         Adornment="Adornment.End"
                         AdornmentIcon="@Icons.Material.Filled.AccountBalance"
                          />
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudNumericField HideSpinButtons="true" 
                         T="int" 
                         @bind-Value="@Receipt.Purchase.InvoiceNo" 
                         Immediate="true" 
                         Label="رقم الفاتورة" 
                         OnKeyDown="OnKeyDown"
                         Variant="Variant.Outlined"
                         Adornment="Adornment.End"
                         AdornmentIcon="@Icons.Material.Filled.Receipt"
                          />
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudTextField Value="Receipt.Purchase.Client.Name" 
                      Disabled="true" 
                      Label="اسم المورد" 
                      Required="true"
                      Variant="Variant.Outlined"
                      Adornment="Adornment.End"
                      AdornmentIcon="@Icons.Material.Filled.Person"
                     />
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudTextField Value="@((Receipt.Purchase.FinalTotal - Receipt.Purchase.Paid))" 
                      Disabled="true" 
                      Label="القيمة المتبقية من الفاتورة"
                      Variant="Variant.Outlined"
                      Adornment="Adornment.End"
                      AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                      />
    </MudItem>

    <MudItem xs="12" sm="6" md="3">
        <MudNumericField HideSpinButtons="true" 
                         T="decimal" 
                         Label="قيمة الإيصال" 
                         Min="0" 
                         Value="Receipt.Value" 
                         ValueChanged="ValueChange" 
                         For="@(() => Receipt.Value)"
                         Variant="Variant.Outlined"
                         Adornment="Adornment.End"
                         AdornmentIcon="@Icons.Material.Filled.AttachMoney"
                         />
    </MudItem>
</MudGrid>
 *@