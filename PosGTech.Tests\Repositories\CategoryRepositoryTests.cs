using Microsoft.EntityFrameworkCore;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository;
using PosGTech.Models;

namespace PosGTech.Tests.Repositories
{
    public class CategoryRepositoryTests : IDisposable
    {
        private readonly ApplicationDbContext _context = new ApplicationDbContext(new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase("TestDatabase")
                .Options);


        [Fact]
        public async Task GetAll_ReturnsAllCategories()
        {
            // Arrange
            var expectedCategories = new List<Category>
            {
                new Category { Id = Guid.NewGuid(), Name = "Category1" },
                new Category { Id = Guid.NewGuid(), Name = "Category2" }
            };

            await _context.Categories.AddRangeAsync(expectedCategories);
            await _context.SaveChangesAsync();

            var repository = new CategoryRepository(_context);

            // Act
            var result = await repository.GetAll();

            // Assert
            Assert.Equal(expectedCategories.Count, result.Count());
        }

        [Fact]
        public async Task Add_SavesCategory()
        {
            // Arrange
            var category = new Category { Id = Guid.NewGuid(), Name = "NewCategory" };
            var repository = new CategoryRepository(_context);

            // Act
            repository.Add(category);

            // Assert
            var savedCategory = await _context.Categories.FindAsync(category.Id);
            Assert.NotNull(savedCategory);
            Assert.Equal(category.Name, savedCategory.Name);
        }

        [Fact]
        public async Task Update_UpdatesCategory()
        {
            // Arrange
            var category = new Category { Id = Guid.NewGuid(), Name = "OriginalCategory" };
            await _context.Categories.AddAsync(category);
            await _context.SaveChangesAsync();

            var repository = new CategoryRepository(_context);

            // Act
            category.Name = "UpdatedCategory";
            repository.Update(category);

            // Assert
            var updatedCategory = await _context.Categories.FindAsync(category.Id);
            Assert.NotNull(updatedCategory);
            Assert.Equal("UpdatedCategory", updatedCategory.Name);
        }

        [Fact]
        public async Task Delete_RemovesCategory()
        {
            // Arrange
            var category = new Category { Id = Guid.NewGuid(), Name = "CategoryToDelete" };
            await _context.Categories.AddAsync(category);
            await _context.SaveChangesAsync();

            var repository = new CategoryRepository(_context);

            // Act
            repository.Remove(category);
            await _context.SaveChangesAsync();
            // Assert
            var deletedCategory = await _context.Categories.FindAsync(category.Id);
            Assert.Null(deletedCategory);
        }

        [Fact]
        public async Task GetById_ReturnsCategoryById()
        {
            // Arrange
            var category = new Category { Id = Guid.NewGuid(), Name = "TestCategory" };
            await _context.Categories.AddAsync(category);
            await _context.SaveChangesAsync();

            var repository = new CategoryRepository(_context);

            // Act
            var result = await repository.GetByIdAsync(category.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(category.Id, result.Id);
            Assert.Equal(category.Name, result.Name);
        }

        // Clean up after each test
        public void Dispose()
        {
            _context.Database.EnsureDeleted();
            _context.Dispose();
        }
    }
}