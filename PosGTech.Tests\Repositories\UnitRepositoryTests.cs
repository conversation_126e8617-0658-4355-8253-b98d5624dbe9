using Microsoft.EntityFrameworkCore;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository;
using PosGTech.Models;

namespace PosGTech.Tests.Repositories
{
    public class UnitRepositoryTests : IDisposable
    {
        private readonly ApplicationDbContext _context = new ApplicationDbContext(new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase("TestDatabase")
                .Options);

        [Fact]
        public async Task GetAll_ReturnsAllUnits()
        {
            // Arrange
            var expectedUnits = new List<Unit>
            {
                new Unit { Id = Guid.NewGuid(), Name = "Unit1" },
                new Unit { Id = Guid.NewGuid(), Name = "Unit2" }
            };
            await _context.Units.AddRangeAsync(expectedUnits);
            await _context.SaveChangesAsync();
            var repository = new UnitRepository(_context);

            // Act
            var result = await repository.GetAll();

            // Assert
            Assert.Equal(expectedUnits.Count, result.Count());
        }

        [Fact]
        public async Task Add_SavesUnit()
        {
            // Arrange
            var unit = new Unit { Id = Guid.NewGuid(), Name = "NewUnit" };
            var repository = new UnitRepository(_context);

            // Act
            repository.Add(unit);

            // Assert
            var savedUnit = await _context.Units.FindAsync(unit.Id);
            Assert.NotNull(savedUnit);
            Assert.Equal(unit.Name, savedUnit.Name);
        }

        [Fact]
        public async Task Update_UpdatesUnit()
        {
            // Arrange
            var unit = new Unit { Id = Guid.NewGuid(), Name = "OriginalUnit" };
            await _context.Units.AddAsync(unit);
            await _context.SaveChangesAsync();
            var repository = new UnitRepository(_context);

            // Act
            unit.Name = "UpdatedUnit";
            repository.Update(unit);

            // Assert
            var updatedUnit = await _context.Units.FindAsync(unit.Id);
            Assert.NotNull(updatedUnit);
            Assert.Equal("UpdatedUnit", updatedUnit.Name);
        }

        [Fact]
        public async Task Delete_RemovesUnit()
        {
            // Arrange
            var unit = new Unit { Id = Guid.NewGuid(), Name = "UnitToDelete" };
            await _context.Units.AddAsync(unit);
            await _context.SaveChangesAsync();
            var repository = new UnitRepository(_context);

            // Act
            repository.Remove(unit);
            await _context.SaveChangesAsync();

            // Assert
            var deletedUnit = await _context.Units.FindAsync(unit.Id);
            Assert.Null(deletedUnit);
        }

        [Fact]
        public async Task GetById_ReturnsUnitById()
        {
            // Arrange
            var unit = new Unit { Id = Guid.NewGuid(), Name = "TestUnit" };
            await _context.Units.AddAsync(unit);
            await _context.SaveChangesAsync();
            var repository = new UnitRepository(_context);

            // Act
            var result = await repository.GetByIdAsync(unit.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(unit.Id, result.Id);
            Assert.Equal(unit.Name, result.Name);
        }

        public void Dispose()
        {
            _context.Database.EnsureDeleted();
            _context.Dispose();
        }
    }
}
