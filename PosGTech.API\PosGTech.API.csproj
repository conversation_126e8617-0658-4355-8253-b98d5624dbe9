﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <InvariantGlobalization>false</InvariantGlobalization>
    <Configurations>Debug;Release;DebShit</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Auth\**" />
    <Compile Remove="Product\**" />
    <Content Remove="Auth\**" />
    <Content Remove="Product\**" />
    <EmbeddedResource Remove="Auth\**" />
    <EmbeddedResource Remove="Product\**" />
    <None Remove="Auth\**" />
    <None Remove="Product\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.2" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.1" />
	  <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.8.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="ReportViewerCore.NETCore" Version="15.1.25" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PosGTech.DataAccess\PosGTech.DataAccess.csproj" />
    <ProjectReference Include="..\PosGTech.ModelsDTO\PosGTech.ModelsDTO.csproj" />
    <ProjectReference Include="..\PosGTech.Models\PosGTech.Models.csproj" />


  </ItemGroup>

</Project>
