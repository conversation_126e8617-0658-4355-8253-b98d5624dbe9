@page "/upsertStoreSettings/{id:guid}"
@using PosGTech.ModelsDTO.ShopSettings
@using Microsoft.AspNetCore.Components.Forms

<MudDialog>
    <DialogContent>
        <EditForm id="_formShopSettings" Model="@shopSettings" OnValidSubmit="@(async () => await Upsert())">
            <DataAnnotationsValidator />
            <MudPaper Class="d-flex" Elevation="0">
                <MudGrid Class="pa-4">
                    <MudItem xs="12">
                        <MudText Typo="Typo.h6" Class="mb-4">إعدادات المتجر</MudText>
                    </MudItem>

                    <!-- اسم المتجر -->
                    <MudItem xs="12" md="6">
                        <MudTextField T="string"
                                      @bind-Value="shopSettings.StoreName"
                                      Label="اسم المتجر"
                                      For="@(() => shopSettings.StoreName)"
                                      Required="true"
                                      Variant="Variant.Outlined" />
                    </MudItem>

                    <!-- اسم الشركة -->
                    <MudItem xs="12" md="6">
                        <MudTextField T="string"
                                      @bind-Value="shopSettings.CompanyName"
                                      Label="اسم الشركة"
                                      For="@(() => shopSettings.CompanyName)"
                                      Variant="Variant.Outlined" />
                    </MudItem>

                    <!-- رقم الهاتف -->
                    <MudItem xs="12" md="6">
                        <MudTextField T="string"
                                      @bind-Value="shopSettings.CompanyPhone"
                                      Label="رقم الهاتف"
                                      For="@(() => shopSettings.CompanyPhone)"
                                      Variant="Variant.Outlined"
                                      InputType="InputType.Telephone" />
                    </MudItem>

                    <!-- العنوان -->
                    <MudItem xs="12" md="6">
                        <MudTextField T="string"
                                      @bind-Value="shopSettings.StoreAddress"
                                      Label="عنوان المتجر"
                                      For="@(() => shopSettings.StoreAddress)"
                                      Variant="Variant.Outlined"
                                      Lines="2" />
                    </MudItem>

                    <!-- رفع الصورة -->
                    <MudItem xs="12">
                        <MudCard Elevation="2" Class="pa-4">
                            <MudCardContent>
                                <MudText Typo="Typo.h6" Class="mb-4" Color="Color.Primary">
                                    <MudIcon Icon="@Icons.Material.Filled.Image" Class="me-2" />
                                    شعار المتجر
                                </MudText>

                                <MudGrid>
                                    <!-- عرض الصورة الحالية -->
                                    @if (!string.IsNullOrEmpty(shopSettings.LogoPath))
                                    {
                                        <MudItem xs="12" md="6">
                                            <MudText Typo="Typo.subtitle2" Class="mb-2">الشعار الحالي:</MudText>
                                            <MudPaper Class="pa-2 text-center" Elevation="1">
                                                <MudImage Src="@GetFullLogoUrl(shopSettings.LogoPath)"
                                                          Alt="شعار المتجر"
                                                          Width="150"
                                                          Height="150"
                                                          ObjectFit="ObjectFit.Contain"
                                                          Class="border rounded" />
                                            </MudPaper>
                                        </MudItem>
                                    }

                                    <!-- رفع صورة جديدة -->
                                    <MudItem xs="12" md="@(!string.IsNullOrEmpty(shopSettings.LogoPath) ? 6 : 12)">
                                        <MudText Typo="Typo.subtitle2" Class="mb-3">
                                            @(string.IsNullOrEmpty(shopSettings.LogoPath) ? "اختر شعار المتجر:" : "تغيير الشعار:")
                                        </MudText>

                                        <div class="text-center">
                                            <MudFileUpload T="IBrowserFile"
                                                           Accept=".jpg,.jpeg,.png,.gif,.bmp"
                                                           FilesChanged="OnLogoFileChanged"
                                                           MaximumFileCount="1">
                                                <ActivatorContent>
                                                    <MudFab Color="@(logoFile == null ? Color.Primary : Color.Success)"
                                                            StartIcon="@(logoFile == null ? Icons.Material.Filled.Image : Icons.Material.Filled.CheckCircle)"
                                                            Label="@(logoFile == null ? "اختر شعار المتجر" : "تغيير الشعار")"
                                                            Size="Size.Large"
                                                            Class="ma-2 elevation-4" />
                                                </ActivatorContent>
                                            </MudFileUpload>
                                        </div>

                                        <!-- منطقة عرض معلومات الملف -->
                                        @if (logoFile == null)
                                        {
                                            <MudAlert Severity="Severity.Info" Class="mt-3" Dense="true">
                                                <MudText Typo="Typo.body2">
                                                    <MudIcon Icon="@Icons.Material.Filled.Info" Class="me-2" />
                                                    انقر على الزر أعلاه لاختيار شعار المتجر
                                                </MudText>
                                                <MudText Typo="Typo.caption" Class="mt-1">
                                                    <strong>الصيغ المدعومة:</strong> JPG, PNG, GIF, BMP<br/>
                                                    <strong>الحد الأقصى للحجم:</strong> 5 ميجابايت
                                                </MudText>
                                            </MudAlert>
                                        }

                                        @if (logoFile != null)
                                        {
                                            <MudCard Class="mt-3" Elevation="2">
                                                <MudCardContent>
                                                    <MudStack Row="true" AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween">
                                                        <MudStack Row="true" AlignItems="AlignItems.Center" Spacing="3">
                                                            @if (!string.IsNullOrEmpty(logoPreviewUrl))
                                                            {
                                                                <MudAvatar Size="Size.Large" Class="border" Style="width: 80px; height: 80px;">
                                                                    <MudImage Src="@logoPreviewUrl" Alt="معاينة الشعار" Style="width: 100%; height: 100%; object-fit: cover;" />
                                                                </MudAvatar>
                                                            }
                                                            else
                                                            {
                                                                <MudAvatar Size="Size.Large" Class="border" Style="width: 80px; height: 80px;">
                                                                    <MudIcon Icon="@Icons.Material.Filled.Image" Size="Size.Large" />
                                                                </MudAvatar>
                                                            }
                                                            <MudStack>
                                                                <MudText Typo="Typo.h6" Color="Color.Success">
                                                                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="me-2" />
                                                                    تم اختيار الملف
                                                                </MudText>
                                                                <MudText Typo="Typo.body2">
                                                                    <strong>اسم الملف:</strong> @logoFile.Name
                                                                </MudText>
                                                                <MudText Typo="Typo.caption">
                                                                    <strong>الحجم:</strong> @FormatFileSize(logoFile.Size)
                                                                </MudText>
                                                            </MudStack>
                                                        </MudStack>
                                                        <MudButton Variant="Variant.Filled"
                                                                   Color="Color.Error"
                                                                   StartIcon="@Icons.Material.Filled.Delete"
                                                                   Size="Size.Small"
                                                                   OnClick="RemoveLogoFile">
                                                            إزالة الملف
                                                        </MudButton>
                                                    </MudStack>
                                                </MudCardContent>
                                            </MudCard>
                                        }

                                        <MudAlert Severity="Severity.Info" Class="mt-3" Dense="true">
                                            <MudText Typo="Typo.caption">
                                                <strong>الصيغ المدعومة:</strong> JPG, PNG, GIF, BMP<br/>
                                                <strong>الحد الأقصى للحجم:</strong> 5 ميجابايت
                                            </MudText>
                                        </MudAlert>
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </EditForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel" Color="Color.Secondary">إلغاء</MudButton>
        <MudButton ButtonType="ButtonType.Submit"
                   form="_formShopSettings"
                   Variant="Variant.Filled"
                   Color="Color.Primary"
                   Disabled="@isLoading">
            @if (isLoading)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                <MudText Class="ms-2">جاري الحفظ...</MudText>
            }
            else
            {
                <text>@(id == Guid.Empty ? "إضافة" : "تحديث")</text>
            }
        </MudButton>
    </DialogActions>
</MudDialog>
