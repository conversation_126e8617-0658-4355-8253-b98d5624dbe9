﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.JSInterop;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Inventories;
using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.Stores;
using PosGTech.Web.Pages.Pages.Items;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Inventories;

public partial class UpsertInventory
{
    [Inject]
    IDialogService DialogService { get; set; }
    [Parameter]
    public Guid id { get; set; }
    InventoryDTO inventory = new();
    InventoryItemDTO selectedItemInventory = new InventoryItemDTO();
    List<ItemDTO> items = new List<ItemDTO>();
    List<StoreCMDTO> stores = new List<StoreCMDTO>();
    [Inject]
    IGRepository<InventoryDTO> _inventory { get; set; }
    [Inject]
    IGRepository<ItemDTO> _item { get; set; }
    [Inject]
    IGRepository<StoreCMDTO> _store { get; set; }
    [Inject]
    IJSRuntime _iJSRuntime { get; set; }
    MudMessageBox mbox { get; set; }
    DateTime? _dateInventory = DateTime.Now, _dateExp = null;
    string _message;
    EditForm Form { get; set; }
    MudAutocomplete<ItemCMDTO> ItemForAdd { get; set; }
    public async void keydownForm(KeyboardEventArgs args)
    {
        if (args.Key == "F2") Upsert();
    }
    public async Task keydown(KeyboardEventArgs args)
    {
        if (args.Key == "Enter") await _iJSRuntime.InvokeVoidAsync("moveFocus");
    }
    /// <summary>
    /// إختيار الصنف عن طريق الباركود
    /// </summary>
    /// <param name="e"></param>
    /// <returns></returns>
    public async Task SelectItem(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            if (ItemForAdd.Text == "" || ItemForAdd.Text is null)
                return;
            if (items.Any(x => x.Name.Contains(ItemForAdd.Text, StringComparison.InvariantCultureIgnoreCase) || x.ItemNums?.Any(x => x.Barcode == ItemForAdd.Text) == true))
            {
                await ChangeItem(items.Where(x => x.Name.Contains(ItemForAdd.Text, StringComparison.InvariantCultureIgnoreCase) || x.ItemNums?.Any(x => x.Barcode == ItemForAdd.Text) == true).Select(x => new ItemCMDTO() { Id = x.Id, Name = x.Name }).First());

            }
            else await ChangeItem(null);
            StateHasChanged();
        }
    }
    async Task GetItems()
    {

        var resItem = await _item.GetAll("Items/getAllItemsForPurchase");
        if (resItem.response == null) items = resItem.list.ToList();
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            _navigation.NavigateTo("/listInventories");
        }
        StateHasChanged();
    }
    async Task GetInventory()
    {
        var res = await _inventory.GetByIdAsync("Inventories/getInventoryById", id);
        if (res.response == null)
        {
            inventory = res.model;
            _dateInventory = inventory.Date.ToDateTime(time: TimeOnly.MinValue);
        }
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            _navigation.NavigateTo("/listInventories");
        }
    }
    protected override async Task OnInitializedAsync()
    {
        var resStore = await _store.GetAll("Stores/getAllStoresCMB");
        if (resStore.response == null) stores = resStore.list.ToList();
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            _navigation.NavigateTo("/listInventories");
        }
        await GetItems();
        if (id != Guid.Empty) await GetInventory();
    }
    async void Upsert()
    {
        if (!Form.EditContext.Validate()) return;
        if (inventory.InventoryItemDTOs.Count == 0)
        { _snackbar.Add($"يرجى اضافة اصناف", Severity.Error); return; }
        if (_dateInventory == null)
        { _snackbar.Add($"يرجى اختيار تاريخ", Severity.Error); return; }
        inventory.Date = DateOnly.FromDateTime(_dateInventory.Value);
        ResponseVM response;
        if (id == Guid.Empty)
        {
            response = await _inventory.Insert("Inventories/insertInventory", inventory);
        }
        else
        {
            response = await _inventory.Update("Inventories/updateInventory", inventory, id);
        }

        if (response.State)
        {
            _snackbar.Add(response.Message, Severity.Success);
            _navigation.NavigateTo("/listInventories");
        }
        else
        {
            _snackbar.Add(response.Message, Severity.Error);
        }
    }
    void Back() => _navigation.NavigateTo("/listInventories");
    private async Task<IEnumerable<ItemCMDTO>> SearchItem(string value, CancellationToken token)
    {
        if (string.IsNullOrEmpty(value))
            return items.Select(x => new ItemCMDTO() { Id = x.Id, Name = x.Name });
        return items.Where(x => x.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase) || x.ItemNums?.Any(x => x.Barcode == value) == true).Select(x => new ItemCMDTO() { Id = x.Id, Name = x.Name });
    }
    private async void AddItemInventory()
    {
        if (selectedItemInventory.Item == null)
        { _snackbar.Add($"يرجى اختيار صنف", Severity.Error); return; }
        if (_dateExp is not null)
        {
            if (_dateExp < DateTime.Now)
            {
                _message = $"الصلاحية {_dateExp.Value.ToShortDateString()} منتهية هل تريد الإستمرار؟";
                bool? result = await mbox.ShowAsync();
                if (result != true) return;
            }
            selectedItemInventory.Exp = DateOnly.FromDateTime(_dateExp.Value);
        }

        if (inventory.InventoryItemDTOs.Any(x => x.ItemUnit.Id == selectedItemInventory.ItemUnit.Id && x.Item.Id == selectedItemInventory.Item.Id
                                                 && x.Exp.GetValueOrDefault().ToShortDateString() == selectedItemInventory.Exp.GetValueOrDefault().ToShortDateString()
                                                    && x.Price == selectedItemInventory.Price))
        {
            _message = $" هذا الصنف {selectedItemInventory.Item.Name} موجود هل تريد اضافة الكمية؟";
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                inventory.InventoryItemDTOs.First(x => x.ItemUnit.Id == selectedItemInventory.ItemUnit.Id && x.Item.Id == selectedItemInventory.Item.Id
                                                 && x.Exp.GetValueOrDefault().ToShortDateString() == selectedItemInventory.Exp.GetValueOrDefault().ToShortDateString()
                                                    && x.Price == selectedItemInventory.Price)
                    .Quantity += selectedItemInventory.Quantity;
            }
            else return;
        }
        else
        {
            selectedItemInventory.InventoryId = id;
            inventory.InventoryItemDTOs.Add(selectedItemInventory);
        }
        items.First(x => x.Id == selectedItemInventory.Item.Id).Quantity += ItemExtensions.GetQuantityUnitDTO(selectedItemInventory.ItemUnit, selectedItemInventory.Quantity);
        selectedItemInventory = new();
        inventory.Total = inventory.InventoryItemDTOs.Sum(x => x.Price * x.Quantity);
        ItemForAdd.Text = null;
        await ItemForAdd.FocusAsync();
        StateHasChanged();
    }
    private void DeleteItem(InventoryItemDTO item)
    {
        inventory.InventoryItemDTOs.Remove(item);
        items.First(x => x.Id == item.Item.Id).Quantity -= ItemExtensions.GetQuantityUnitDTO(item.ItemUnit, item.Quantity);
        inventory.Total = inventory.InventoryItemDTOs.Sum(x => x.Price * x.Quantity);
        StateHasChanged();
    }
    private void ChangeQte(decimal quantity)
    {
        if (quantity <= 0)
        {
            selectedItemInventory.Quantity = decimal.One; return;
        }
        selectedItemInventory.Quantity = quantity;
    }

    private async Task ChangeItem(ItemCMDTO? Item)
    {
        selectedItemInventory.Item = Item;
        selectedItemInventory.ItemUnit = null;
        if (selectedItemInventory.Item != null)
        {
            selectedItemInventory.Price = items.First(x => x.Id == selectedItemInventory.Item.Id).CostPrice;
            selectedItemInventory.ItemUnit = items.First(x => x.Id == selectedItemInventory.Item.Id).ItemUnits.First(x => x.IsBasicUnit);
            _dateExp = items.First(x => x.Id == selectedItemInventory.Item.Id).IsHaveExp ? DateTime.Now : null;

            await keydown(new KeyboardEventArgs() { Key = "Enter" });
        }
    }
    private void ChangeItemUnit(ItemUnitDTO? unit)
    {
        selectedItemInventory.ItemUnit = unit;
        selectedItemInventory.Price = selectedItemInventory.ItemUnit != null ? ItemExtensions.GetPriceUnitDTO(unit, items.First(x => x.Id == selectedItemInventory.Item.Id).CostPrice) : 0;
    }
    async void AddNewItem()
    {
        var parameters = new DialogParameters<UpsertItem>();
        parameters.Add(x => x.id, Guid.Empty);
        var options = new DialogOptions() { CloseButton = false, MaxWidth = MaxWidth.Medium, FullWidth = true };
        var result = await DialogService.Show<UpsertItem>("إضافة صنف", parameters, options).Result;
        if ((string?)result.Data != null)
        {
            await GetItems();
            selectedItemInventory.Item = items.Where(x => x.Name == (string)result.Data).Select(x => new ItemCMDTO() { Id = x.Id, Name = x.Name }).First();
            StateHasChanged();
            await ItemForAdd.FocusAsync();
        }
    }

}