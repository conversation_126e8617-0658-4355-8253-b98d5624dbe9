﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models;

public class ItemUnit : BaseEntity
{
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public Guid UnitId { get; set; }
    public Unit? Unit { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public Guid ItemId { get; set; }
    public Item? Item { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
    [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
    public decimal Quantity { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
    [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
    public decimal SalePrice { get; set; }
    /// <summary>
    /// هل هو اكبر من الوحدة الاساسية او لا
    /// اكبر عملة X
    /// اصغر عملة /
    /// </summary>
    public bool IsBigger { get; set; }
    /// <summary>
    ///هذه الوحدة الأساسية او فرعية
    /// اساسية true
    /// فرعية false/
    /// </summary>
    public bool IsBasicUnit { get; set; }
    public ICollection<ReturnSellItem>? ReturnSellItems { get; set; }
    public ICollection<PurchaseItem>? PurchaseItems { get; set; }
    public ICollection<ConsumedItem>? ConsumedItems { get; set; }
    public ICollection<InventoryItem>? InventoryItems { get; set; }
    public ICollection<ReturnPurchaseItem>? ReturnPurchaseItems { get; set; }
    public ICollection<TransferItem>? TransferItems { get; set; }
    public ICollection<SellItem>? SellItems { get; set; }
}

