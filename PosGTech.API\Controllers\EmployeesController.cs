﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Employees;
using PosGTech.ModelsDTO.Finacnial;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmployeesController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("getAllEmployeesCM")]
        public async Task<IActionResult> GetAllEmployeesCM()
        {
            var employees = maper.Map<IEnumerable<Employee>, IEnumerable<EmployeeCMDTO>>(await unitOfWork.Employee.GetAll());
            return Ok(employees);
        }
        [HttpGet("getAllEmployees")]
        public async Task<IActionResult> GetAllEmployees()
        {
            var employees = maper.Map<IEnumerable<Employee>, IEnumerable<EmployeeDTO>>(await unitOfWork.Employee.GetAll());
            return Ok(employees);
        }


        [HttpGet("getEmployeeById/{id:Guid}")]
        public async Task<IActionResult> GetEmployeeById([FromRoute] Guid id)
        {
            var employee = maper.Map<Employee, EmployeeDTO>(await unitOfWork.Employee.GetByIdAsync(id));
            return Ok(employee);
        }



        [HttpPost("insertEmployee")]
        public async Task<IActionResult> InsertEmployee([FromBody] EmployeeDTO model)
        {
            var newemployee = maper.Map<EmployeeDTO, Employee>(model);
            if (newemployee.Balance != 0) await unitOfWork.Receipt.AddReceipt(new() { Employee = newemployee, Date = DateTime.Now, FinancialId = FinancialId.OpeningBalanceForEmployee, IsExchange = null, Value = newemployee.Balance });
            else unitOfWork.Employee.Add(newemployee);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpPut("updateEmployee/{id:Guid}")]
        public async Task<IActionResult> UpdateEmployee([FromRoute] Guid id, EmployeeDTO model)
        {
            var oldemployee = await unitOfWork.Employee.GetByIdAsync(id);
            if (oldemployee == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });

            unitOfWork.Employee.UpdateEmployee(oldemployee, model);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpDelete("deleteEmployee/{id:Guid}")]
        public async Task<IActionResult> DeleteEmployee([FromRoute] Guid id)
        {
            var oldemployee = await unitOfWork.Employee.GetByIdAsync(id);
            if (oldemployee == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            unitOfWork.Employee.Remove(oldemployee);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }
    }
}
