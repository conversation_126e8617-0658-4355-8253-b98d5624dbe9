﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Treasury;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Treasuriess
{
    public partial class UpsertTreasuries
    {
        [CascadingParameter] MudDialogInstance MudDialog { get; set; }
        [Parameter]
        public Guid id { get; set; }
        TreasuryDTO Treasury
            = new();
        [Inject]
        IGRepository<TreasuryDTO> _TreasuryServer { get; set; }
        protected override async Task OnInitializedAsync()
        {
            if (id != Guid.Empty)
            {
                var res = await _TreasuryServer.GetByIdAsync("Treasuries/getTreasuryById", id);
                if (res.response == null)
                {
                    Treasury = res.model;
                }
                else
                {
                    _snackbar.Add("خطأ في الاتصال", Severity.Error);
                    MudDialog.Cancel();
                }
            }
        }
        async void Upsert()
        {
            ResponseVM response;
            if (id == Guid.Empty)
                response = await _TreasuryServer.Insert("Treasuries/insertTreasury", Treasury);
            else
                response = await _TreasuryServer.Update("Treasuries/updateTreasury", Treasury, id);

            if (response.State)
            {
                _snackbar.Add(response.Message, Severity.Success);
                MudDialog.Close(Treasury.Name);
            }
            else
            {
                _snackbar.Add(response.Message, Severity.Error);
            }
        }
        void Cancel() => MudDialog.Cancel();

        private bool isClosing = false;
        private bool isAnimating = false;


    }
}