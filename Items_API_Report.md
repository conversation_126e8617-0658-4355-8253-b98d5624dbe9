# تقرير شامل عن الأصناف (Items) في PosGTech.Web

## نظرة عامة

نظام الأصناف في PosGTech.Web يوفر واجهة مستخدم متقدمة وسهلة الاستخدام لإدارة جميع المنتجات والسلع في نظام نقطة البيع. يستخدم التطبيق تقنية Blazor WebAssembly مع مكتبة MudBlazor لتوفير تجربة مستخدم حديثة ومتجاوبة. يشمل النظام إدارة شاملة للأصناف مع دعم الوحدات المتعددة، الباركود، التصنيفات، والتكامل الكامل مع API الخلفي.

## البنية التقنية

### 1. النماذج الأساسية (Models)

#### Item Model
```csharp
public class Item : BaseEntity
{
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    [StringLength(100, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
    public string Name { get; set; }
    
    public bool IsHaveExp { get; set; }
    
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public Guid CategoryId { get; set; }
    public Category? Category { get; set; }
    
    [Precision(10, 3, ErrorMessage = "خطا في القيمة")]
    [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
    public decimal CostPrice { get; set; }
    
    // العلاقات
    public ICollection<ItemUnit>? ItemUnits { get; set; }
    public ICollection<ItemNum>? ItemNums { get; set; }
    public ICollection<StoreItem>? StoreItems { get; set; }
}
```

**الخصائص الرئيسية:**
- **Id**: المعرف الفريد للصنف
- **Name**: اسم الصنف (مطلوب، حد أقصى 100 حرف)
- **IsHaveExp**: هل الصنف له تاريخ انتهاء
- **CategoryId**: معرف التصنيف (مطلوب)
- **CostPrice**: سعر التكلفة (دقة 10,3)

#### ItemUnit Model
```csharp
public class ItemUnit : BaseEntity
{
    [Required] public Guid UnitId { get; set; }
    public Unit? Unit { get; set; }
    
    [Required] public Guid ItemId { get; set; }
    public Item? Item { get; set; }
    
    [Precision(10, 3)] public decimal Quantity { get; set; }
    [Precision(10, 3)] public decimal SalePrice { get; set; }
    
    public bool IsBigger { get; set; }  // أكبر من الوحدة الأساسية
    public bool IsBasicUnit { get; set; }  // الوحدة الأساسية
}
```

#### ItemNum Model (الباركود)
```csharp
public class ItemNum : BaseEntity
{
    [Required] public Guid ItemId { get; set; }
    public Item? Item { get; set; }
    
    [Required] public string Barcode { get; set; }
}
```

### 2. نماذج البيانات المنقولة (DTOs)

#### ItemDTO
```csharp
public class ItemDTO
{
    public Guid Id { get; set; }
    
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    [StringLength(100, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
    public string Name { get; set; }
    
    public decimal Quantity { get; set; } = decimal.Zero;
    public bool IsHaveExp { get; set; }
    
    [Required] public Guid CategoryId { get; set; }
    
    [Precision(10, 3)] public decimal CostPrice { get; set; } = decimal.Zero;
    
    public ICollection<ItemNumDTO>? ItemNums { get; set; }
    public ICollection<ItemUnitDTO>? ItemUnits { get; set; }
}
```

#### ItemCMDTO (للعمليات المبسطة)
نموذج مبسط للأصناف يُستخدم في العمليات التي لا تحتاج لجميع التفاصيل.

#### ItemUnitDTO
```csharp
public class ItemUnitDTO
{
    public Guid Id { get; set; }
    public UnitDTO? Unit { get; set; }
    public ItemDTO? Item { get; set; }
    
    [Precision(10, 3)] public decimal Quantity { get; set; }
    public decimal SalePrice { get; set; }
    
    public bool IsBigger { get; set; }
    public bool IsBasicUnit { get; set; }
    public bool IsAbleToDelete { get; set; } = true;
}
```

#### ItemNumDTO
```csharp
public class ItemNumDTO
{
    public Guid Id { get; set; }
    public ItemDTO? Item { get; set; }
    
    [Required] public string Barcode { get; set; }
}
```

## واجهة برمجة التطبيقات (API Endpoints)

### ItemsController

#### 1. جلب جميع الأصناف
```http
GET /api/Items/getAllItems
Authorization: Bearer {token}
```
**الوصف**: جلب جميع الأصناف مع الوحدات والأرقام التسلسلية
**الاستجابة**: `IEnumerable<ItemDTO>`

#### 2. جلب الأصناف للمشتريات
```http
GET /api/Items/getAllItemsForPurchase
Authorization: Bearer {token}
```
**الوصف**: جلب الأصناف مرتبة حسب آخر تحديث للمشتريات
**الاستجابة**: `IEnumerable<ItemDTO>`

#### 3. جلب الأصناف المبسطة
```http
GET /api/Items/getAllItemsCM
Authorization: Bearer {token}
```
**الوصف**: جلب الأصناف بصيغة مبسطة للعمليات السريعة
**الاستجابة**: `IEnumerable<ItemCMDTO>`

#### 4. جلب صنف بالمعرف
```http
GET /api/Items/getItemById/{id}
Authorization: Bearer {token}
```
**المعاملات**: 
- `id` (Guid): معرف الصنف
**الاستجابة**: `ItemDTO`

#### 5. إضافة صنف جديد
```http
POST /api/Items/insertItem
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "اسم الصنف",
  "isHaveExp": true,
  "categoryId": "guid-here",
  "costPrice": 100.50,
  "itemNums": [
    {
      "barcode": "1234567890"
    }
  ],
  "itemUnits": [
    {
      "unitId": "guid-here",
      "quantity": 1,
      "salePrice": 120.00,
      "isBigger": false,
      "isBasicUnit": true
    }
  ]
}
```

#### 6. تحديث صنف
```http
PUT /api/Items/updateItem/{id}
Authorization: Bearer {token}
Content-Type: application/json
```

#### 7. حذف صنف
```http
DELETE /api/Items/deleteItem/{id}
Authorization: Bearer {token}
```

### ItemUnitController

#### جلب وحدات صنف
```http
GET /api/ItemUnit/GetItemUnit/{id}
```
**الوصف**: جلب جميع وحدات صنف معين
**المعاملات**: `id` (Guid): معرف الصنف

## العلاقات مع النماذج الأخرى

### 1. علاقة مع التصنيفات (Categories)
- كل صنف ينتمي لتصنيف واحد
- التصنيف الواحد يحتوي على عدة أصناف
- علاقة One-to-Many

### 2. علاقة مع الوحدات (Units)
- الصنف الواحد يمكن أن يكون له عدة وحدات قياس
- كل وحدة لها سعر بيع منفصل
- وحدة أساسية واحدة لكل صنف
- علاقة Many-to-Many عبر ItemUnit

### 3. علاقة مع المخازن (StoreItems)
- الصنف يُخزن في عدة مخازن
- كل مخزن يحتفظ بكمية منفصلة لكل صنف
- علاقة Many-to-Many عبر StoreItem

### 4. علاقة مع تواريخ الانتهاء (StoreItemExp)
- للأصناف التي لها تاريخ انتهاء
- تتبع الكميات حسب تاريخ الانتهاء
- علاقة One-to-Many

### 5. علاقة مع المبيعات (SellItems)
- كل عنصر مبيعات يرتبط بصنف ووحدة
- تسجيل الكمية والسعر لكل عملية بيع
- علاقة One-to-Many

### 6. علاقة مع المشتريات (PurchaseItems)
- كل عنصر مشتريات يرتبط بصنف ووحدة
- تسجيل الكمية والسعر لكل عملية شراء
- علاقة One-to-Many

### 7. علاقة مع الجرد (InventoryItems)
- تسجيل كميات الجرد لكل صنف
- مقارنة الكميات الفعلية مع المسجلة
- علاقة One-to-Many

### 8. علاقة مع التحويلات (TransferItems)
- نقل الأصناف بين المخازن
- تتبع حركة الأصناف
- علاقة One-to-Many

### 9. علاقة مع المستهلكات (ConsumedItems)
- الأصناف المستهلكة في العمليات الداخلية
- تقليل المخزون بدون مبيعات
- علاقة One-to-Many

### 10. علاقة مع المرتجعات
- **ReturnSellItems**: مرتجعات المبيعات
- **ReturnPurchaseItems**: مرتجعات المشتريات
- علاقة One-to-Many لكل نوع

## المستودعات (Repositories)

### IItemRepository
```csharp
public interface IItemRepository : IRepository<Item>
{
    void UpdateItem(Item oldeitem, ItemDTO newItem);
    Task UpdateCost(IEnumerable<ItemForChangeQte> items);
    Task<ItemDTO?> GetItemDTOById(Guid id);
}
```

### ItemRepository
**الوظائف الرئيسية:**

#### 1. GetItemDTOById
```csharp
public async Task<ItemDTO?> GetItemDTOById(Guid id)
```
- جلب صنف بصيغة DTO مع جميع التفاصيل
- يشمل الوحدات والأرقام التسلسلية
- استعلام محسن للأداء

#### 2. UpdateItem
```csharp
public void UpdateItem(Item oldeitem, ItemDTO newItem)
```
- تحديث بيانات الصنف
- إدارة الوحدات والأرقام التسلسلية
- معالجة التغييرات المعقدة

#### 3. UpdateCost
```csharp
public Task UpdateCost(IEnumerable<ItemForChangeQte> items)
```
- تحديث تكلفة الأصناف
- حساب متوسط التكلفة
- تحديث أسعار البيع تلقائياً

## الوظائف المساعدة (Helper Functions)

### ItemExtensions
```csharp
public static class ItemExtensions
{
    public static decimal GetPriceUnitDTO(ItemUnitDTO unit, decimal price)
    public static decimal GetPriceUnit(ItemUnit unit, decimal price)
    public static decimal GetQuantityUnitDTO(ItemUnitDTO unit, decimal quantity)
    public static decimal GetQuantityUnit(ItemUnit unit, decimal quantity)
}
```

**الوظائف:**
- **GetPriceUnit**: حساب السعر حسب الوحدة
- **GetQuantityUnit**: تحويل الكمية حسب الوحدة
- دعم الوحدات الأكبر والأصغر من الوحدة الأساسية

## التحقق من صحة البيانات (Validation)

### 1. Validation Attributes
- **Required**: الحقول المطلوبة
- **StringLength**: طول النص (100 حرف للاسم)
- **Range**: نطاق القيم (0 إلى 10,000,000)
- **Precision**: دقة الأرقام العشرية (10,3)

### 2. Custom Validation
- **PosGTech.CustomValidationAttribute.Precision**: تحديد دقة الأرقام
- رسائل خطأ باللغة العربية
- التحقق من القيم المنطقية

### 3. Business Rules
- كل صنف يجب أن يكون له وحدة أساسية واحدة على الأقل
- الباركود يجب أن يكون فريد
- سعر التكلفة لا يمكن أن يكون سالب
- الكمية لا يمكن أن تكون سالبة

## إدارة المخزون

### 1. تتبع الكميات
- **StoreItem**: الكمية الإجمالية في كل مخزن
- **StoreItemExp**: الكميات حسب تاريخ الانتهاء
- تحديث تلقائي مع كل عملية

### 2. حركة المخزون
- **إضافة**: عند المشتريات والتحويلات الواردة
- **تقليل**: عند المبيعات والتحويلات الصادرة والاستهلاك
- **تسوية**: عند الجرد

### 3. تواريخ الانتهاء
- تتبع منفصل للأصناف ذات تاريخ الانتهاء
- نظام FIFO (First In, First Out)
- تنبيهات للأصناف منتهية الصلاحية

## الأمان والتخويل

### 1. المصادقة
- جميع endpoints تتطلب JWT Token
- التحقق من صحة المستخدم

### 2. التخويل
- صلاحيات مختلفة للعمليات
- حماية العمليات الحساسة
- تسجيل العمليات

### 3. حماية البيانات
- التحقق من صحة المدخلات
- حماية من SQL Injection
- تشفير البيانات الحساسة

## الأداء والتحسين

### 1. استعلامات محسنة
- استخدام Include للعلاقات
- تحميل البيانات المطلوبة فقط
- فهرسة الجداول

### 2. Caching
- تخزين مؤقت للأصناف المستخدمة بكثرة
- تحديث Cache عند التغيير
- تحسين أوقات الاستجابة

### 3. Pagination
- تقسيم النتائج للقوائم الكبيرة
- تحسين استخدام الذاكرة
- تحسين تجربة المستخدم

## معالجة الأخطاء

### 1. أنواع الأخطاء
- **Validation Errors**: أخطاء التحقق من البيانات
- **Business Logic Errors**: أخطاء منطق الأعمال
- **Database Errors**: أخطاء قاعدة البيانات
- **Authorization Errors**: أخطاء التخويل

### 2. رسائل الخطأ
- رسائل واضحة باللغة العربية
- تفاصيل كافية لحل المشكلة
- عدم كشف معلومات حساسة

### 3. Logging
- تسجيل جميع العمليات
- تتبع الأخطاء والاستثناءات
- مراقبة الأداء

## التكامل مع النظم الأخرى

### 1. نظام المحاسبة
- تحديث تكلفة البضاعة المباعة
- حساب الأرباح والخسائر
- تقارير مالية

### 2. نظام التقارير
- تقارير المخزون
- تقارير المبيعات حسب الصنف
- تحليل الأداء

### 3. نظام الباركود
- قراءة الباركود
- طباعة الملصقات
- إدارة الأرقام التسلسلية

## أمثلة على الاستخدام

### 1. إضافة صنف جديد
```json
{
  "name": "لابتوب ديل",
  "isHaveExp": false,
  "categoryId": "550e8400-e29b-41d4-a716-446655440000",
  "costPrice": 15000.00,
  "itemNums": [
    {
      "barcode": "1234567890123"
    }
  ],
  "itemUnits": [
    {
      "unitId": "550e8400-e29b-41d4-a716-446655440001",
      "quantity": 1,
      "salePrice": 18000.00,
      "isBigger": false,
      "isBasicUnit": true
    }
  ]
}
```

### 2. تحديث سعر صنف
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440002",
  "name": "لابتوب ديل",
  "costPrice": 14500.00,
  "itemUnits": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440003",
      "salePrice": 17500.00
    }
  ]
}
```

### 3. البحث عن صنف بالباركود
```http
GET /api/Items/getItemByBarcode/1234567890123
```

## الخلاصة

نظام الأصناف في PosGTech API يوفر:

### المزايا الرئيسية:
- **إدارة شاملة**: جميع جوانب إدارة الأصناف
- **مرونة عالية**: دعم الوحدات المتعددة والباركود
- **تكامل كامل**: مع جميع أجزاء النظام
- **أداء محسن**: استعلامات سريعة وفعالة
- **أمان عالي**: حماية البيانات والعمليات

### الوظائف المتقدمة:
- إدارة تواريخ الانتهاء
- نظام الوحدات المتعدد
- تتبع حركة المخزون
- التكامل مع المبيعات والمشتريات
- تقارير تفصيلية

### مجالات الاستخدام:
- محلات التجزئة
- المطاعم والمقاهي
- الصيدليات
- المستودعات
- أي نشاط تجاري يتطلب إدارة مخزون

هذا التقرير يوفر فهماً شاملاً لنظام الأصناف ويمكن استخدامه كمرجع للمطورين والمستخدمين النهائيين.
