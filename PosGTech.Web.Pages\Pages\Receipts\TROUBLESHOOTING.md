# استكشاف أخطاء طباعة الإيصالات

## الأخطاء الشائعة وحلولها

### 1. خط<PERSON> "Could not find 'printReceiptFromBlazor'"

**السبب**: لم يتم تحميل ملف JavaScript بشكل صحيح

**الحلول**:
1. **تحقق من تحميل الملف**:
   - افتح Developer Tools (F12)
   - اذهب إلى تبويب Console
   - ابحث عن رسالة "Receipt Print JS loaded successfully"
   - إذا لم تظهر، فالملف لم يتم تحميله

2. **تحقق من مسار الملف**:
   - تأكد من وجود الملف في: `wwwroot/js/receipt-print.js`
   - تحقق من أن المسار في `index.html` صحيح

3. **إعادة تحميل الصفحة**:
   - اضغط Ctrl+F5 لإعادة تحميل كامل
   - امسح cache المتصفح

4. **تحقق من الشبكة**:
   - في Developer Tools، اذهب إلى تبويب Network
   - ابحث عن `receipt-print.js`
   - تأكد من أنه يتم تحميله بنجاح (Status 200)

### 2. خطأ في تحميل مكتبة Print.js

**السبب**: مشكلة في الاتصال بـ CDN أو حجب المحتوى

**الحلول**:
1. **تحقق من الاتصال بالإنترنت**
2. **تحقق من إعدادات Firewall/Antivirus**
3. **استخدام الطريقة البديلة**: النظام يستخدم تلقائياً `window.print()` كبديل

### 3. مشكلة النوافذ المنبثقة

**السبب**: المتصفح يحجب النوافذ المنبثقة

**الحلول**:
1. **السماح للنوافذ المنبثقة**:
   - في Chrome: اضغط على أيقونة الحجب في شريط العناوين
   - اختر "Always allow pop-ups from this site"

2. **تحقق من إعدادات المتصفح**:
   - اذهب إلى Settings > Privacy and Security > Site Settings
   - ابحث عن Pop-ups and redirects
   - أضف الموقع إلى القائمة المسموحة

### 4. مشكلة عرض الخطوط العربية

**السبب**: عدم تحميل خطوط Google Fonts

**الحلول**:
1. **تحقق من الاتصال بالإنترنت**
2. **تحقق من تحميل الخطوط**:
   - في Developer Tools > Network
   - ابحث عن طلبات fonts.googleapis.com
   - تأكد من نجاح التحميل

3. **استخدام خطوط محلية كبديل**:
   ```css
   font-family: 'Noto Sans Arabic', 'Cairo', 'Tahoma', Arial, sans-serif;
   ```

### 5. مشكلة عرض الشعار

**السبب**: مسار الشعار غير صحيح أو الملف غير موجود

**الحلول**:
1. **تحقق من رفع الشعار**:
   - اذهب إلى إعدادات المتجر
   - تأكد من رفع الشعار بنجاح

2. **تحقق من مسار الشعار**:
   - في Developer Tools > Console
   - ابحث عن أخطاء تحميل الصور

3. **تحقق من صيغة الصورة**:
   - الصيغ المدعومة: JPG, PNG, GIF, BMP

## خطوات التشخيص

### 1. فحص أساسي
```javascript
// في Console المتصفح
console.log('Print functions check:', {
    printReceiptFromBlazor: typeof printReceiptFromBlazor,
    previewReceiptFromBlazor: typeof previewReceiptFromBlazor,
    printJS: typeof printJS
});
```

### 2. اختبار الطباعة البديلة
```javascript
// اختبار الطباعة بدون Print.js
window.open('data:text/html,<html dir="rtl"><body><h1>اختبار الطباعة</h1></body></html>', '_blank');
```

### 3. فحص إعدادات المتجر
- تأكد من وجود إعدادات افتراضية في قاعدة البيانات
- تحقق من صحة مسارات الشعارات

## الطرق البديلة

### 1. الطباعة البديلة
إذا فشلت مكتبة Print.js، يستخدم النظام تلقائياً:
- `window.print()` للطباعة المباشرة
- نوافذ منبثقة مع HTML مخصص

### 2. المعاينة البديلة
- فتح نافذة جديدة مع HTML كامل
- أزرار طباعة وإغلاق مدمجة

## نصائح للأداء الأفضل

### 1. تحسين التحميل
- تأكد من ترتيب تحميل ملفات JavaScript
- استخدم CDN سريع لمكتبة Print.js

### 2. تحسين الطباعة
- استخدم صور مضغوطة للشعارات
- قلل من استخدام الألوان في الطباعة الحرارية

### 3. تحسين التوافق
- اختبر على متصفحات مختلفة
- تأكد من دعم JavaScript ES6+

## الدعم الفني

إذا استمرت المشاكل:
1. تحقق من سجلات الأخطاء في المتصفح
2. تأكد من تحديث المتصفح
3. جرب متصفح مختلف للاختبار
4. تحقق من إعدادات الشبكة والأمان

## معلومات إضافية

- **المتصفحات المدعومة**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **أنظمة التشغيل**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **الطابعات المدعومة**: جميع الطابعات التي تدعم HTML/CSS printing
