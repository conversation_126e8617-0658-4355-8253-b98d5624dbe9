﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Units;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UnitsController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("getAllUnits")]
        public async Task<IActionResult> GetAllUnits()
        {
            var Units = maper.Map<IEnumerable<Unit>, IEnumerable<UnitDTO>>(await unitOfWork.Unit.GetAll());
            return Ok(Units);
        }


        [HttpGet("getUnitById/{id:Guid}")]
        public async Task<IActionResult> GetUnitById([FromRoute] Guid id)
        {
            var Unit = maper.Map<Unit, UnitDTO>(await unitOfWork.Unit.GetByIdAsync(id));
            return Ok(Unit);
        }

        [HttpPost("insertUnit")]
        public async Task<IActionResult> InsertUnit([FromBody] UnitDTO model)
        {
            var newUnit = maper.Map<UnitDTO, Unit>(model);
            unitOfWork.Unit.Add(newUnit);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpPut("updateUnit/{id:Guid}")]
        public async Task<IActionResult> UpdateUnit([FromRoute] Guid id, UnitDTO model)
        {

            var oldUnit = await unitOfWork.Unit.GetByIdAsync(id);
            if (oldUnit == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            unitOfWork.Unit.UpdateUnit(oldUnit, model);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);

        }



        [HttpDelete("deleteUnit/{id:Guid}")]
        public async Task<IActionResult> DeleteUnit([FromRoute] Guid id)
        {
            var oldUnit = await unitOfWork.Unit.GetByIdAsync(id);
            if (oldUnit == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            unitOfWork.Unit.Remove(oldUnit);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }

    }
}
