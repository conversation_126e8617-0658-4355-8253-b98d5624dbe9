using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PosGTech.Web.Services.Interfaces
{
    /// <summary>
    /// Interface for error logging service
    /// </summary>
    public interface IErrorLoggingService
    {
        /// <summary>
        /// Logs an error with detailed information
        /// </summary>
        /// <param name="errorInfo">Error information to log</param>
        /// <returns>Task representing the async operation</returns>
        Task LogErrorAsync(ErrorInfo errorInfo);

        /// <summary>
        /// Logs an exception with context
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="context">Context where the error occurred</param>
        /// <param name="additionalData">Additional data to include in log</param>
        /// <returns>Task representing the async operation</returns>
        Task LogExceptionAsync(Exception exception, string context = "", Dictionary<string, object>? additionalData = null);

        /// <summary>
        /// Gets recent error logs for debugging
        /// </summary>
        /// <param name="count">Number of recent errors to retrieve</param>
        /// <returns>List of recent error information</returns>
        Task<List<ErrorInfo>> GetRecentErrorsAsync(int count = 10);

        /// <summary>
        /// Clears old error logs
        /// </summary>
        /// <param name="olderThanDays">Clear logs older than specified days</param>
        /// <returns>Task representing the async operation</returns>
        Task ClearOldLogsAsync(int olderThanDays = 7);
    }

    /// <summary>
    /// Error information for logging
    /// </summary>
    public class ErrorInfo
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string ExceptionType { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string StackTrace { get; set; } = string.Empty;
        public string Context { get; set; } = string.Empty;
        public ErrorSeverity Severity { get; set; }
        public string UserAgent { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public Dictionary<string, object> AdditionalData { get; set; } = new();
        public string InnerException { get; set; } = string.Empty;
    }
}
