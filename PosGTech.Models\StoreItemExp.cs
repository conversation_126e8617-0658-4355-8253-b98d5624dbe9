﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    public class StoreItemExp : BaseEntity
    {
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid StoreItemId { get; set; }
        public StoreItem? StoreItem { get; set; }
        public DateOnly? Exp { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal Quantity { get; set; }
    }
}
