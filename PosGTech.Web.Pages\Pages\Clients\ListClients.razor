﻿@page "/ListClients"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<AuthorizeView Policy="@PermissionConstants.ClientsView" Context="auth">
    <Authorized>
        <MudPaper Class="d-flex flex-column justify-space-evenly desktop rounded-0" Elevation="0">

    <MudTable Items="@clients" Height="calc(100vh - 170px)" Loading="loading" Breakpoint="Breakpoint.Sm" Filter="new Func<ClientDTO,bool>(FilterFunc1)" @bind-SelectedItem="selectedItem" Virtualize="true" Striped="true" Dense="true" FixedHeader="true" Elevation="4">

        <ToolBarContent>
            <MudText Align="@Align.Justify" Typo="Typo.h6" Style="margin-top:24px; margin-left:48px;">العملاء</MudText>
            <MudTextField @bind-Value="Search" Placeholder="بحث" Adornment="Adornment.End" AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium" Class="mt-0"></MudTextField>
            <MudSpacer />
            <AuthorizeView Policy="@PermissionConstants.ClientsAdd" Context="auth0">
                <Authorized>
                    <MudButton Variant="Variant.Filled" EndIcon="@Icons.Material.Filled.Add" OnClick="()=>Upsert(Guid.Empty)" Color="Color.Primary">جديد</MudButton>
                </Authorized>
            </AuthorizeView>
        </ToolBarContent>

        <HeaderContent>
            <MudTh>ت</MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<ClientDTO, object>(x=>x.Name)">الاسم</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<ClientDTO, object>(x=>x.Phone)">رقم الهاتف</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<ClientDTO, object>(x=>x.Balance)">الرصيد الحالي</MudTableSortLabel></MudTh>
            <MudTh></MudTh>
        </HeaderContent>
        <RowTemplate>
            <MudTd DataLabel="ت">@(clients.ToList().IndexOf(context) + 1)</MudTd>
            <MudTd DataLabel="الاسم">@context.Name</MudTd>
                <MudTd DataLabel="رقم الهاتف">@context.Phone</MudTd>
                <MudTd DataLabel="الرصيد الحالي">@context.Balance</MudTd>
                <MudTd>
                    <AuthorizeView Policy="@PermissionConstants.ClientsEdit" Context="auth1">
                        <Authorized>
                            <MudIconButton Size="Size.Small" Color="Color.Info" Icon="@Icons.Material.Filled.Edit" OnClick="()=>Upsert(context.Id)" />
                        </Authorized>
                    </AuthorizeView>
                    <AuthorizeView Policy="@PermissionConstants.ClientsDelete" Context="auth2">
                        <Authorized>
                            <MudIconButton Size="Size.Small" Color="Color.Error" Icon="@Icons.Material.Filled.Delete" OnClick="()=>Delete(context)" />
                        </Authorized>
                    </AuthorizeView>
                </MudTd>
            </RowTemplate>

            <PagerContent>
                <MudTablePager style="height:24px;" PageSizeOptions="new int[] {int.MaxValue }"
                               RowsPerPageString="عدد الصفوف :"
                               InfoFormat="{last_item} -- {all_items}"
                               HorizontalAlignment="HorizontalAlignment.Center"
                               HideRowsPerPage="true"
                               HidePageNumber="true"
                               HidePagination="true" />
            </PagerContent>
        </MudTable>

  
</MudPaper>
            
    <MudMessageBox @ref="mbox" Title="تنبيه" CancelText="إلغاء">
        <MessageContent>
        هل تريد حذف <b>@NameClientForDelete</b> ؟
    </MessageContent>
    <YesButton>
        <MudButton Variant="Variant.Filled" Color="Color.Error" StartIcon="@Icons.Material.Filled.DeleteForever">حذف!</MudButton>
    </YesButton>
</MudMessageBox>


<style>

    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }
</style>
    </Authorized>
    <NotAuthorized>
        <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Class="ma-4">
            ليس لديك صلاحية الوصول لعرض العملاء
        </MudAlert>
    </NotAuthorized>
</AuthorizeView>
