﻿using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.CustomValidationAttribute;
using PosGTech.ModelsDTO.Employees;
using PosGTech.ModelsDTO.Expenses;
using PosGTech.ModelsDTO.Finacnial;
using PosGTech.ModelsDTO.Purchases;
using PosGTech.ModelsDTO.Treasury;
using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Receipts;

public class ReceiptDTO
{
    public Guid Id { get; set; }
    public int ReceiptNo { get; set; } = 0;
    public string? Statement { get; set; }
    public DateTime? Date { get; set; } = DateTime.Now;
    /// <summary>
    /// يكون صحيح في حال الصرف وخطأ في حال القبض
    /// </summary>
    public bool? IsExchange { get; set; }
    /// <summary>
    /// القيمة
    /// </summary>
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public decimal Value { get; set; }
    [RequiredIf(["موظف", "صرف مرتب", "سحب", "فاتورة مشتريات", "فاتورة مبيعات", "إيداع", "مصروفات", "تحويل", "عميل"], ErrorMessage = "هذا الحقل مطلوب")]
    public UserTreasuryCMDTO? UserTreasury { get; set; }
    [RequiredIf(["رصيد افتتاحي للموظف", "موظف", "صرف مرتب"], ErrorMessage = "هذا الحقل مطلوب")]
    public EmployeeCMDTO? Employee { get; set; }
    [RequiredIf(["رصيد افتتاحي للعميل", "عميل"], ErrorMessage = "هذا الحقل مطلوب")]
    public ClientCMDTO? Client { get; set; }
    [RequiredIf(["مصروفات"], ErrorMessage = "هذا الحقل مطلوب")]
    public ExpenseDTO? Expense { get; set; }
    [RequiredIf(["فاتورة مشتريات"], ErrorMessage = "هذا الحقل مطلوب")]
    public PurchaseReceiptDTO? Purchase { get; set; }
    [RequiredIf(["فاتورة مبيعات"], ErrorMessage = "هذا الحقل مطلوب")]
    public SellReceiptDTO? Sell { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public FinancialDTO? Financial { get; set; }
    [RequiredIf(["تحويل"], ErrorMessage = "هذا الحقل مطلوب")]
    public UserTreasuryCMDTO? ToTreasury { get; set; }
}
