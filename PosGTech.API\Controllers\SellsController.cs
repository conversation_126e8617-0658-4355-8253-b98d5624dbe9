﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO;
using PosGTech.ModelsDTO.Sells;
using PosGTech.ModelsDTO.Clients;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    //[Authorize]
    public class SellsController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("getSellById/{id:Guid}")]
        public async Task<IActionResult> GetSellById([FromRoute] Guid id)
        {
            var sell = maper.Map<Sell, SellDTO>(await unitOfWork.Sell.GetFirstOrDefault(x => x.Id == id, $"Client,SellItems.StoreItemExp.StoreItem.Item,SellItems.ItemUnit.Unit,Receipts"));
            return Ok(sell);
        }
        [HttpGet("getSellByNum/{billNum:int}")]
        public async Task<IActionResult> GetSellByNum([FromRoute] int billNum)
        {
            var sells = maper.Map<Sell, SellReceiptDTO>(await unitOfWork.Sell.GetFirstOrDefault(x => x.InvoiceNo == billNum, $"Client,SellItems.StoreItemExp.StoreItem.Item,SellItems.ItemUnit.Unit,Receipts"));
            return Ok(sells);
        }
        [HttpGet("getAllSellsNum")]
        public async Task<IActionResult> GetAllSellsNum()
        {
            var sells = maper.Map<IEnumerable<Sell>, IEnumerable<SellsNumDTO>>(await unitOfWork.Sell.GetAll(order: x => x.InvoiceNo));
            return Ok(sells);
        }

        [HttpPost("insertSell")]
        public async Task<IActionResult> InsertSell([FromBody] SellDTO model)
        {
            var userId = User.Claims.FirstOrDefault(x => x.Type == "id").Value;
            var newSell = maper.Map<SellDTO, Sell>(model);
            var id = await unitOfWork.Sell.AddSell(newSell, userId, model.Paid, model.TreasuryId);
            if (unitOfWork.StoreItemExp.CheckQuantityItemsBelowZero()) return BadRequest(new ResponseVM() { State = false, Message = "الكمية غير موجودة" });
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) { res.Message = id.ToString(); return Ok(res); }
            else return BadRequest(res);

        }
        [HttpPut("updateSell/{id:Guid}")]
        public async Task<IActionResult> UpdateSell([FromRoute] Guid id, SellDTO model)
        {
            var oldsell = await unitOfWork.Sell.GetFirstOrDefault(x => x.Id == id, "Client,SellItems.StoreItemExp.StoreItem.Item,SellItems.ItemUnit.Unit");
            if (oldsell == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            if (model.Paid != 0 && oldsell.ClientId != model.Client.Id) return BadRequest(new ResponseVM() { Message = "لا يمكن تغيير العميل" });
            await unitOfWork.Sell.UpdateSell(oldsell, maper.Map<SellDTO, Sell>(model));
            if (unitOfWork.StoreItemExp.CheckQuantityItemsBelowZero()) return BadRequest(new ResponseVM() { State = false, Message = "الكمية غير موجودة" });
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpDelete("deleteSell/{id:Guid}")]
        public async Task<IActionResult> DeleteSell([FromRoute] Guid id)
        {
            var oldsell = await unitOfWork.Sell.GetFirstOrDefault(x => x.Id == id, "Client,SellItems.StoreItemExp.StoreItem.Item,SellItems.ItemUnit.Unit,Receipts");
            if (oldsell == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            if (oldsell.SellItems.Any(x => x.ReturnQuantity > 0)) return BadRequest(new ResponseVM() { Message = "لا يمكن الحذف يوجد اصناف مرجعة" });
            if (oldsell.Receipts.Any()) return BadRequest(new ResponseVM() { Message = "لا يمكن الحذف يوجد ايصالات لهذه الفاتورة" });
            await unitOfWork.Sell.DeleteSell(oldsell);
            if (unitOfWork.StoreItemExp.CheckQuantityItemsBelowZero()) return BadRequest(new ResponseVM() { State = false, Message = "الكمية غير موجودة" });
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }

        [HttpGet("getSalesReport")]
        public async Task<IActionResult> GetSalesReport([FromQuery] DateOnly fromDate, [FromQuery] DateOnly toDate, [FromQuery] Guid? storeId = null)
        {
            try
            {
                var sells = await unitOfWork.Sell.GetAll(
                    filter: x => x.Date >= fromDate && x.Date <= toDate && (storeId == null || x.StoreId == storeId),
                    includeProperties: "Client,Store,SellItems.ItemUnit.Item,Receipts"
                );

                var salesDetails = sells.Select(sell => new SalesReportDetailDTO
                {
                    Id = sell.Id,
                    InvoiceNo = sell.InvoiceNo,
                    Date = sell.Date,
                    Client = maper.Map<Client, ClientCMDTO>(sell.Client),
                    StoreName = sell.Store?.Name ?? "غير محدد",
                    Total = sell.Total,
                    DiscountValue = sell.DiscountValue,
                    FinalTotal = sell.FinalTotal,
                    Paid = sell.Receipts?.Sum(r => r.Value) ?? 0,
                    Remaining = sell.FinalTotal - (sell.Receipts?.Sum(r => r.Value) ?? 0),
                    ItemCount = sell.SellItems?.Count ?? 0
                }).ToList();

                var dailySummary = salesDetails
                    .GroupBy(s => s.Date)
                    .Select(g => new DailySummaryDTO
                    {
                        Date = g.Key,
                        InvoiceCount = g.Count(),
                        TotalSales = g.Sum(s => s.Total),
                        TotalDiscount = g.Sum(s => s.DiscountValue),
                        FinalTotal = g.Sum(s => s.FinalTotal),
                        ItemCount = g.Sum(s => s.ItemCount),
                        AverageInvoiceValue = g.Count() > 0 ? g.Average(s => s.FinalTotal) : 0
                    })
                    .OrderBy(d => d.Date)
                    .ToList();

                var statistics = new SalesStatisticsDTO
                {
                    TotalRevenue = salesDetails.Sum(s => s.FinalTotal),
                    TotalInvoices = salesDetails.Count,
                    TotalDiscount = salesDetails.Sum(s => s.DiscountValue),
                    AverageInvoiceValue = salesDetails.Count > 0 ? salesDetails.Average(s => s.FinalTotal) : 0,
                    TotalItems = salesDetails.Sum(s => s.ItemCount),
                    TotalPaid = salesDetails.Sum(s => s.Paid),
                    TotalRemaining = salesDetails.Sum(s => s.Remaining)
                };

                var report = new SalesReportDTO
                {
                    Statistics = statistics,
                    SalesDetails = salesDetails,
                    DailySummary = dailySummary
                };

                return Ok(report);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM { State = false, Message = $"خطأ في جلب التقرير: {ex.Message}" });
            }
        }
    }
}
