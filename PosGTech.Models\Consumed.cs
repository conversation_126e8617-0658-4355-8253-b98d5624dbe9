﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول فواتير المواد المستهلكة
    /// </summary>
    public class Consumed
 : BaseEntity
    {

        public int InvoiceNo { get; set; } = 0;
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public DateOnly Date { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
        public decimal TotalPrice { get; set; }
        public Guid StoreId { get; set; }
        public Store? Store { get; set; }
        public ICollection<ConsumedItem>? ConsumedItems { get; set; }
    }
}
