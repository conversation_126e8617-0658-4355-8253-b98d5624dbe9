﻿using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.StoreItemExp;
using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Sells
{
    public class SellItemDTO
    {
        public Guid Id { get; set; }
        public Guid SellId { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal Quantity { get; set; } = 1;
        public decimal CostPrice { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal SalePrice { get; set; }
        public decimal SalePriceAfterDiscount { get; set; } = decimal.Zero;

        public StoreItemExpDTO? StoreItemExp { get; set; }
        public ItemUnitDTO? ItemUnit { get; set; }
        public decimal ReturnQuantity { get; set; } = 0;

    }
}
