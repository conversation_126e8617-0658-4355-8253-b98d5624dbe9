﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models.ViewModels;
using PosGTech.Models;
using PosGTech.ModelsDTO.Treasury;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TreasuriesController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("getAllTreasury")]
        public async Task<IActionResult> GetAllTreasury()
        {
            var Treasurys = maper.Map<IEnumerable<Treasury>, IEnumerable<TreasuryCMDTO>>(await unitOfWork.Treasury.GetAll());
            return Ok(Treasurys);
        }
     
        [HttpGet("getTreasuryById/{id:Guid}")]
        public async Task<IActionResult> GetTreasuryById([FromRoute] Guid id)
        {
            var Treasury = maper.Map<Treasury, TreasuryDTO>(await unitOfWork.Treasury.GetFirstOrDefault(x => x.Id == id,tracked: false));
            return Ok(Treasury);
        }



        [HttpPost("insertTreasury")]
        public async Task<IActionResult> InsertTreasury([FromBody] TreasuryDTO model)
        {
            if (!ModelState.IsValid)
                return Ok(new ResponseVM() { State = false, Message = $"الرجاء تعبئة البيانات بشكل صحيح" });

            unitOfWork.Treasury.Add(maper.Map<Treasury>(model));

            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);

        }




        [HttpPut("updateTreasury/{id:Guid}")]
        public async Task<IActionResult> UpdateTreasury([FromRoute] Guid id, [FromBody] TreasuryDTO model)
        {
            if (!ModelState.IsValid)
                return BadRequest(new ResponseVM() { State = false, Message = "الرجاء تعبئة البيانات بشكل صحيح" });

            var oldTreasury = await unitOfWork.Treasury.GetFirstOrDefault(x => x.Id == id);

            if (oldTreasury == null) return BadRequest(new ResponseVM() { Message = "  الخزينة  المراد التعديل عليه غير موجودة " });
            unitOfWork.Treasury.UpdateTreasury(ref oldTreasury, maper.Map<Treasury>(model));

            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);


        }


        [HttpDelete("deleteTreasury/{id:Guid}")]
        public async Task<IActionResult> DeleteTreasury([FromRoute] Guid id)
        {
            var oldTreasury = await unitOfWork.Treasury.GetByIdAsync(id);
            if (oldTreasury == null) return BadRequest(new ResponseVM() { Message = " الخزينة المراد حذفه  غير موجود" });
            unitOfWork.Treasury.Remove(oldTreasury);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }
    }
}
