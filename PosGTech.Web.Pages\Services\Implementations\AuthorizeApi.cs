﻿using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components.Authorization;
using PosGTech.ModelsDTO.Authentication;
using PosGTech.Web.Services.Contracts;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Security.Claims;
using System.Text.Json;

namespace PosGTech.Web.Services.Implementations
{
    public class AuthorizeApi : IAuthorizeApi
    {
        private readonly HttpClient _httpClient;

        public event AuthenticationStateChangedHandler? AuthenticationStateChanged;
        private readonly ILocalStorageService _localStorage;

        public AuthorizeApi(HttpClient httpClient, ILocalStorageService localStorage)
        {
            _httpClient = httpClient;
            _localStorage = localStorage;
        }
        protected void NotifyAuthenticationStateChanged(Task<AuthenticationState> task)
        {
            if (task == null)
            {
                throw new ArgumentNullException(nameof(task));
            }

            AuthenticationStateChanged?.Invoke(task);
        }
        public async Task<AuthUser> Login(LoginUser loginParameters)
        {
            var response = await _httpClient.PostAsJsonAsync(_httpClient.BaseAddress + "Users/login", loginParameters);
            return await SaveTokenFromResponse(response);
        }
        public async Task<AuthUser> RefreshToken(string token)
        {
            var response = await _httpClient.GetAsync(_httpClient.BaseAddress + "getRefreshTokenUser/" + token);
            return await SaveTokenFromResponse(response);
        }
        async Task<AuthUser> SaveTokenFromResponse(HttpResponseMessage response)
        {
            if (response.StatusCode == System.Net.HttpStatusCode.OK)
            {
                var result = await response.Content.ReadFromJsonAsync<AuthUser>();
                await _localStorage.SetItemAsStringAsync("authToken", result.Token);
                await _localStorage.SetItemAsStringAsync("ExpiresOn", result.ExpiresOn.ToString());
                await _localStorage.SetItemAsStringAsync("RefreshToken", result.RefreshToken);
                await _localStorage.SetItemAsStringAsync("RefreshTokenExpiration", result.RefreshTokenExpiration.ToString());
                var authenticatedUser = new ClaimsPrincipal(new ClaimsIdentity(ParseClaimsFromJwt(result.Token), "jwt"));
                var authState = Task.FromResult(new AuthenticationState(authenticatedUser));
                NotifyAuthenticationStateChanged(authState);
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", result.Token);
                response.EnsureSuccessStatusCode();
                return result;
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
            {
                string m = await response.Content.ReadAsStringAsync();
                return new AuthUser() { isAuthenticated = false, Message = m };
            }
            else
            {
                return new AuthUser() { isAuthenticated = false, Message = "خطأ في الاتصال" };
            }
        }
        public async Task Logout()
        {
            await _localStorage.RemoveItemAsync("authToken");
            await _localStorage.RemoveItemAsync("ExpiresOn");
            await _localStorage.RemoveItemAsync("RefreshToken");
            await _localStorage.RemoveItemAsync("RefreshTokenExpiration");
        }
        private IEnumerable<Claim> ParseClaimsFromJwt(string jwt)
        {
            var claims = new List<Claim>();
            var payload = jwt.Split('.')[1];
            var jsonBytes = ParseBase64WithoutPadding(payload);
            var keyValuePairs = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonBytes);

            // معالجة الأدوار (roles)
            keyValuePairs.TryGetValue(ClaimTypes.Role, out object roles);
            if (roles != null)
            {
                if (roles.ToString().Trim().StartsWith("["))
                {
                    var parsedRoles = JsonSerializer.Deserialize<string[]>(roles.ToString());
                    foreach (var parsedRole in parsedRoles)
                    {
                        claims.Add(new Claim(ClaimTypes.Role, parsedRole));
                    }
                }
                else
                {
                    claims.Add(new Claim(ClaimTypes.Role, roles.ToString()));
                }
                keyValuePairs.Remove(ClaimTypes.Role);
            }

            // معالجة الصلاحيات (permissions)
            keyValuePairs.TryGetValue("permission", out object permissions);
            if (permissions != null)
            {
                if (permissions.ToString().Trim().StartsWith("["))
                {
                    var parsedPermissions = JsonSerializer.Deserialize<string[]>(permissions.ToString());
                    foreach (var permission in parsedPermissions)
                    {
                        claims.Add(new Claim("permission", permission));
                    }
                }
                else
                {
                    claims.Add(new Claim("permission", permissions.ToString()));
                }
                keyValuePairs.Remove("permission");
            }

            // معالجة باقي Claims
            claims.AddRange(keyValuePairs.Select(kvp => new Claim(kvp.Key, kvp.Value.ToString())));

            return claims;
        } //ParseClaimsFromJwt




        private static byte[] ParseBase64WithoutPadding(string base64)
        {
            switch (base64.Length % 4)
            {
                case 2: base64 += "=="; break;
                case 3: base64 += "="; break;
            }
            return Convert.FromBase64String(base64);
        } //ParseBase64WithoutPadding

        public async Task<ClaimsIdentity> GetClaims()
        {
            if (await _localStorage.GetItemAsStringAsync("authToken") != null)
            {
                if (DateTime.Parse(await _localStorage.GetItemAsStringAsync("ExpiresOn")) < DateTime.UtcNow)
                {
                    if (DateTime.Parse(await _localStorage.GetItemAsStringAsync("RefreshTokenExpiration")) < DateTime.UtcNow)
                    {
                        await Logout();
                        return new ClaimsIdentity();
                    }
                    await Logout();
                    var auth = await RefreshToken(await _localStorage.GetItemAsStringAsync("RefreshToken"));
                    if (auth.isAuthenticated) return new ClaimsIdentity(ParseClaimsFromJwt(auth.Token).ToList().Select(c => new Claim(c.Type, c.Value)).ToArray(), "Server authentication");
                    else return new ClaimsIdentity();
                }
                return new ClaimsIdentity(ParseClaimsFromJwt(await _localStorage.GetItemAsStringAsync("authToken")).ToList().Select(c => new Claim(c.Type, c.Value)).ToArray(), "Server authentication");
            }
            return new ClaimsIdentity();
        }
    }
    public class ServiceResponse<T>
    {
        public T? Data { get; set; }
        public bool Success { get; set; } = true;
        public string Message { get; set; } = string.Empty;
    }

}
