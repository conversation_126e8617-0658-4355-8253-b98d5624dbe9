# اختبار تكامل بيانات المتجر في الإيصالات

## خطوات الاختبار

### 1. إعداد بيانات المتجر
```
انتقل إلى: صفحة إعدادات المتجر
املأ البيانات التالية:
- اسم المتجر: "متجر التقنية المتقدمة"
- اسم الشركة: "شركة التقنية للبرمجيات"
- رقم الهاتف: "0501234567"
- عنوان المتجر: "الرياض، حي النخيل، شارع الملك فهد"
- ارفع شعار للمتجر (اختياري)
```

### 2. إنشاء إيصال جديد
```
انتقل إلى: صفحة الإيصالات
أنشئ إيصال جديد:
- نوع العملية: "عميل"
- القيمة: 1000 ريال
- البيان: "دفعة من العميل أحمد محمد"
```

### 3. طباعة الإيصال
```
اضغط على زر "طباعة"
تحقق من ظهور البيانات التالية:
✓ شعار المتجر في الزاوية اليمنى العلوية
✓ اسم المتجر في الوسط بخط كبير
✓ اسم الشركة أسفل اسم المتجر
✓ رقم الهاتف مع تسمية "هاتف:"
✓ عنوان المتجر
```

## النتائج المتوقعة

### في حالة وجود بيانات كاملة
```
┌─────────────────────────────────────────────────────────┐
│  [شعار]         متجر التقنية المتقدمة        التاريخ: 2024/01/15  │
│                شركة التقنية للبرمجيات                    │
│                هاتف: 0501234567                        │
│           الرياض، حي النخيل، شارع الملك فهد              │
│                                                         │
│                   إيصال قبض نقدية                       │
│                     رقم 1001                           │
└─────────────────────────────────────────────────────────┘
```

### في حالة عدم وجود شعار
```
┌─────────────────────────────────────────────────────────┐
│  [متجر التقنية]    متجر التقنية المتقدمة    التاريخ: 2024/01/15  │
│                شركة التقنية للبرمجيات                    │
│                هاتف: 0501234567                        │
│           الرياض، حي النخيل، شارع الملك فهد              │
└─────────────────────────────────────────────────────────┘
```

### في حالة عدم وجود إعدادات
```
┌─────────────────────────────────────────────────────────┐
│  [اسم المتجر]         اسم المتجر           التاريخ: 2024/01/15  │
│                اسم الشركة المطورة                       │
│                هاتف: 0123456789                        │
│                   عنوان المتجر                         │
└─────────────────────────────────────────────────────────┘
```

## حالات الاختبار المختلفة

### اختبار 1: بيانات كاملة مع شعار
- ✅ جميع الحقول مملوءة
- ✅ شعار مرفوع
- **النتيجة المتوقعة**: عرض كامل لجميع البيانات

### اختبار 2: بيانات كاملة بدون شعار
- ✅ جميع الحقول مملوءة
- ❌ لا يوجد شعار
- **النتيجة المتوقعة**: عرض اسم المتجر المختصر بدلاً من الشعار

### اختبار 3: بيانات جزئية
- ✅ اسم المتجر فقط
- ❌ باقي الحقول فارغة
- **النتيجة المتوقعة**: عرض اسم المتجر فقط

### اختبار 4: لا توجد إعدادات
- ❌ لا توجد إعدادات محفوظة
- **النتيجة المتوقعة**: عرض القيم الافتراضية

## رسائل التحذير المتوقعة

### عند عدم وجود إعدادات
```
"تم استخدام إعدادات افتراضية. يرجى إضافة إعدادات المتجر من قائمة الإعدادات"
```

### عند فشل تحميل الإعدادات
```
"خطأ في تحميل إعدادات المتجر: [تفاصيل الخطأ]"
```

### عند عدم وجود اسم متجر
```
"تحذير: اسم المتجر غير محدد في الإعدادات"
```

## التحقق من الجودة

### 1. التصميم
- ✓ الخطوط واضحة ومقروءة
- ✓ الألوان مناسبة للطباعة
- ✓ التخطيط متوازن ومنظم
- ✓ دعم اللغة العربية واتجاه RTL

### 2. الوظائف
- ✓ تحميل البيانات بنجاح
- ✓ عرض البيانات في المواقع الصحيحة
- ✓ معالجة الحالات الاستثنائية
- ✓ رسائل خطأ واضحة

### 3. الأداء
- ✓ تحميل سريع للبيانات
- ✓ عدم تأثير على سرعة الطباعة
- ✓ استهلاك ذاكرة معقول

## استكشاف الأخطاء

### مشكلة: لا يظهر الشعار
**الحلول**:
1. تحقق من صحة مسار الشعار
2. تأكد من وجود الملف في المجلد المحدد
3. تحقق من صيغة الصورة (JPG, PNG, GIF, BMP)

### مشكلة: لا تظهر بيانات المتجر
**الحلول**:
1. تحقق من حفظ الإعدادات بنجاح
2. تأكد من وجود اتصال بقاعدة البيانات
3. راجع رسائل الخطأ في وحدة التحكم

### مشكلة: تخطيط غير صحيح
**الحلول**:
1. تحقق من دعم المتصفح للطباعة
2. تأكد من إعدادات الطباعة (A4, Portrait)
3. راجع CSS للطباعة

## الخلاصة
التكامل بين UpsertReceipt.razor و UpsertStoreSettings.razor يوفر:
- عرض احترافي لبيانات المتجر
- مرونة في التخصيص
- معالجة شاملة للأخطاء
- تجربة مستخدم محسنة
