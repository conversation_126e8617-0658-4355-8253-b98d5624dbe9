﻿@page "/listPurchases"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<AuthorizeView Policy="@PermissionConstants.PurchasesView" Context="auth">
    <Authorized>
        <MudContainer MaxWidth="MaxWidth.False" Class="pa-0">
    <MudPaper Class="d-flex flex-column desktop rounded-lg" Elevation="0">
        <MudTable Items="@purchases"
                  Height="calc(100vh - 170px)"
                  Loading="loading"
                  Breakpoint="Breakpoint.Sm"
                  Filter="new Func<PurchaseDTO,bool>(FilterFunc1)"
                  @bind-SelectedItem="selectedItem"
                  Virtualize="true"
                  Striped="true"
                  Dense="true"
                  FixedHeader="true"
                  Elevation="2"
                  Class="rounded-lg">
            <ToolBarContent>
                <div class="d-flex align-center gap-4 pa-4 flex-grow-1">
                    <MudIcon Icon="@Icons.Material.Filled.Receipt"
                             Color="Color.Primary"
                             Size="Size.Large" />
                    <MudText Typo="Typo.h5" Color="Color.Primary">فواتير الشراء</MudText>
                    <MudSpacer />
                    <MudTextField @bind-Value="Search"
                                  Placeholder="بحث"
                                  Adornment="Adornment.End"
                                  AdornmentIcon="@Icons.Material.Filled.Search"
                                  Class="ml-auto flex-grow-0"
                                  Style="min-width: 300px;"
                                  Immediate="true"
                                  Variant="Variant.Outlined"
                                  Dense="true" />
                    <MudButton Variant="Variant.Filled"
                               EndIcon="@Icons.Material.Filled.Add"
                               OnClick="()=>Upsert(Guid.Empty)"
                               Color="Color.Primary"
                               Size="Size.Large"
                               Class="ml-4">
                        فاتورة جديدة
                    </MudButton>
                </div>
            </ToolBarContent>

            <HeaderContent>
                <MudTh Style="width: 80px">#</MudTh>
                <MudTh>
                    <MudTableSortLabel SortBy="new Func<PurchaseDTO, object>(x=>x.InvoiceNo)">
                        <div class="d-flex gap-2">
                            <MudIcon Icon="@Icons.Material.Filled.Numbers" Size="Size.Small" />
                            رقم الفاتورة
                        </div>
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortBy="new Func<PurchaseDTO, object>(x=>x.Client.Name)">
                        <div class="d-flex gap-2">
                            <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" />
                            المورد
                        </div>
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortBy="new Func<PurchaseDTO, object>(x=>x.Date)">
                        <div class="d-flex gap-2">
                            <MudIcon Icon="@Icons.Material.Filled.DateRange" Size="Size.Small" />
                            التاريخ
                        </div>
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortBy="new Func<PurchaseDTO, object>(x=>x.FinalTotal)">
                        <div class="d-flex gap-2">
                            <MudIcon Icon="@Icons.Material.Filled.MonetizationOn" Size="Size.Small" />
                            الاجمالي
                        </div>
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortBy="new Func<PurchaseDTO, object>(x=>x.Paid)">
                        <div class="d-flex gap-2">
                            <MudIcon Icon="@Icons.Material.Filled.Payments" Size="Size.Small" />
                            المدفوع
                        </div>
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortBy="new Func<PurchaseDTO, object>(x=>x.FinalTotal-x.Paid)">
                        <div class="d-flex gap-2">
                            <MudIcon Icon="@Icons.Material.Filled.AccountBalance" Size="Size.Small" />
                            المتبقي
                        </div>
                    </MudTableSortLabel>
                </MudTh>
                <MudTh Style="width: 100px">الإجراءات</MudTh>
            </HeaderContent>

            <RowTemplate>
                <MudTd DataLabel="#">@(purchases.ToList().IndexOf(context) + 1)</MudTd>
                <MudTd DataLabel="رقم الفاتورة">@context.InvoiceNo</MudTd>
                <MudTd DataLabel="المورد">@context.Client.Name</MudTd>
                <MudTd DataLabel="التاريخ">@context.Date.ToShortDateString()</MudTd>
                <MudTd DataLabel="الاجمالي">@context.FinalTotal.ToString("N2")</MudTd>
                <MudTd DataLabel="المدفوع">@context.Paid.ToString("N2")</MudTd>
                <MudTd DataLabel="المتبقي">@((context.FinalTotal - context.Paid).ToString("N2"))</MudTd>
                <MudTd>
                    <div class="d-flex gap-2">
                        <MudIconButton Size="Size.Small"
                                       Color="Color.Info"
                                       Icon="@Icons.Material.Filled.Edit"
                                       OnClick="()=>Upsert(context.Id)"
                                       Class="pa-1" />
                        <MudIconButton Size="Size.Small"
                                       Color="Color.Error"
                                       Icon="@Icons.Material.Filled.Delete"
                                       OnClick="()=>Delete(context)"
                                       Class="pa-1" />
                        <MudIconButton Size="Size.Small"
                                       Color="Color.Primary"
                                       Icon="@Icons.Material.Filled.Print"
                                       OnClick="()=>PrintReport(context.Id)"
                                       Class="pa-1" />

                    </div>
                </MudTd>
            </RowTemplate>

            <PagerContent>
                <MudTablePager RowsPerPageString="عدد الصفوف :"
                               InfoFormat="{last_item} -- {all_items}"
                               HorizontalAlignment="HorizontalAlignment.Center"
                               Class="pa-4" />
            </PagerContent>
        </MudTable>
    </MudPaper>
</MudContainer>

<MudMessageBox @ref="mbox"
               Title="تأكيد الحذف"
               CancelText="إلغاء"
               Class="rounded-lg">
    <MessageContent>
        <div class="d-flex flex-column gap-4">
            <MudIcon Icon="@Icons.Material.Filled.Warning"
                     Color="Color.Warning"
                     Size="Size.Large"
                     Class="mx-auto" />
            <MudText Align="Align.Center">
                هل أنت متأكد من حذف فاتورة رقم <b>@NamePurchaseForDelete</b> ؟
            </MudText>
        </div>
    </MessageContent>
    <YesButton>
        <MudButton Variant="Variant.Filled"
                   Color="Color.Error"
                   StartIcon="@Icons.Material.Filled.DeleteForever"
                   Size="Size.Large">
            تأكيد الحذف
        </MudButton>
    </YesButton>
</MudMessageBox>

<style>
    .desktop {
        height: calc(100vh - 64px);
        width: 100%;
        overflow: hidden;
    }

    .mud-table {
        border-radius: 8px;
    }

    .mud-table-container {
        overflow: hidden;
    }

    .mud-table-head {
        background-color: var(--mud-palette-background-grey);
    }

    .mud-table-cell {
        font-size: 0.875rem;
    }

    .mud-button {
        text-transform: none;
    }

    .mud-icon-button {
        transition: transform 0.2s ease;
    }

        .mud-icon-button:hover {
            transform: scale(1.1);
        }
</style>
    </Authorized>
    <NotAuthorized>
        <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Class="ma-4">
            ليس لديك صلاحية الوصول لعرض المشتريات
        </MudAlert>
    </NotAuthorized>
</AuthorizeView>
