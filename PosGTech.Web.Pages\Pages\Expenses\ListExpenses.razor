﻿@page "/ListExpenses"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]
<AuthorizeView Policy="@PermissionConstants.ExpensesView" Context="auth">
    <Authorized>

        <MudPaper Class="d-flex flex-column justify-space-evenly desktop rounded-0" Elevation="0">


            <MudTable Items="@Expenses" Height="calc(100vh - 170px)" Loading="loading" Breakpoint="Breakpoint.Sm" Filter="new Func<ExpenseDTO,bool>(FilterFunc1)" @bind-SelectedItem="selectedItem" Virtualize="true" Striped="true" Dense="true" FixedHeader="true" Elevation="4">

                <ToolBarContent>
                    <MudText Align="@Align.Justify" Typo="Typo.h6" Style="margin-top:24px; margin-left:48px;">المصروفات</MudText>
                    <MudTextField @bind-Value="Search" Placeholder="بحث" Adornment="Adornment.End" AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium" Class="mt-0"></MudTextField>
                    <MudSpacer />
                    <AuthorizeView Policy="@PermissionConstants.ExpensesAdd" Context="auth1">
                        <Authorized>
                            <MudButton Variant="Variant.Filled" EndIcon="@Icons.Material.Filled.Add" OnClick="()=>Upsert(Guid.Empty)" Color="Color.Primary">جديد</MudButton>
                        </Authorized>
                    </AuthorizeView>
                </ToolBarContent>

                <HeaderContent>
                    <MudTh Class="rounded-0">ت</MudTh>
                    <MudTh><MudTableSortLabel SortBy="new Func<ExpenseDTO, object>(x=>x.Name)">الاسم</MudTableSortLabel></MudTh>
                    <MudTh Class="rounded-0"></MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="ت">@(Expenses.ToList().IndexOf(context) + 1)</MudTd>
                    <MudTd DataLabel="الاسم">@context.Name</MudTd>
                    <MudTd>
                        <AuthorizeView Policy="@PermissionConstants.ExpensesEdit" Context="auth2">
                            <Authorized>
                                <MudIconButton Size="Size.Small" Color="Color.Info" Icon="@Icons.Material.Filled.Edit" OnClick="()=>Upsert(context.Id)" />
                            </Authorized>
                        </AuthorizeView>
                        <AuthorizeView Policy="@PermissionConstants.ExpensesDelete" Context="auth3">
                            <Authorized>
                                <MudIconButton Size="Size.Small" Color="Color.Error" Icon="@Icons.Material.Filled.Delete" OnClick="()=>Delete(context)" />
                            </Authorized>
                        </AuthorizeView>
                    </MudTd>
                </RowTemplate>


                <PagerContent>
                    <MudTablePager style="height:24px;" PageSizeOptions="new int[] {int.MaxValue }"
                                   RowsPerPageString="عدد الصفوف :"
                                   InfoFormat="{last_item} -- {all_items}"
                                   HorizontalAlignment="HorizontalAlignment.Center"
                                   HideRowsPerPage="true"
                                   HidePageNumber="true"
                                   HidePagination="true" />
                </PagerContent>
            </MudTable>


        </MudPaper>
    </Authorized>

    <NotAuthorized>
        <p>ليس لديك صلاحية الوصول.</p>
    </NotAuthorized>
</AuthorizeView>


    <MudMessageBox @ref="mbox" Title="تنبيه" CancelText="إلغاء">
        <MessageContent>
            هل تريد حذف <b>@NameExpenseForDelete</b> ؟
    </MessageContent>
    <YesButton>
        <MudButton Variant="Variant.Filled" Color="Color.Error" StartIcon="@Icons.Material.Filled.DeleteForever">حذف!</MudButton>
    </YesButton>
</MudMessageBox>



<style>

    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }
</style>