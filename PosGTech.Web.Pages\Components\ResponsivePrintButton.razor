@using Microsoft.AspNetCore.Components
@using MudBlazor
@inject IJSRuntime JSRuntime
@inject ISnackbar Snackbar

<div class="d-flex gap-2">
    <!-- زر الطباعة الحرارية -->
    <MudButton Variant="Variant.Filled"
               Color="Color.Primary"
               StartIcon="Icons.Material.Filled.Print"
               OnClick="@(() => PrintReport("Thermal"))"
               Disabled="@IsLoading"
               Size="Size.Small">
        @if (IsLoading && CurrentPrintType == "Thermal")
        {
            <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
            <MudText Class="ms-2">جاري الطباعة...</MudText>
        }
        else
        {
            <MudText>طباعة حرارية</MudText>
        }
    </MudButton>

    <!-- زر طباعة A4 -->
    <MudButton Variant="Variant.Outlined"
               Color="Color.Secondary"
               StartIcon="Icons.Material.Filled.PictureAsPdf"
               OnClick="@(() => PrintReport("A4"))"
               Disabled="@IsLoading"
               Size="Size.Small">
        @if (IsLoading && CurrentPrintType == "A4")
        {
            <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
            <MudText Class="ms-2">جاري التحميل...</MudText>
        }
        else
        {
            <MudText>تحميل PDF</MudText>
        }
    </MudButton>

    <!-- زر الطباعة السريعة (افتراضي حراري) -->
    @if (ShowQuickPrint)
    {
        <MudButton Variant="Variant.Text"
                   Color="Color.Info"
                   StartIcon="Icons.Material.Filled.FlashOn"
                   OnClick="@(() => PrintReport(DefaultPrintType))"
                   Disabled="@IsLoading"
                   Size="Size.Small">
            طباعة سريعة
        </MudButton>
    }
</div>

@code {
    [Parameter] public Guid InvoiceId { get; set; }
    [Parameter] public string ApiEndpoint { get; set; } = "api/Report/ReportSellResponsive";
    [Parameter] public string DefaultPrintType { get; set; } = "Thermal";
    [Parameter] public bool ShowQuickPrint { get; set; } = true;
    [Parameter] public EventCallback<string> OnPrintStarted { get; set; }
    [Parameter] public EventCallback<string> OnPrintCompleted { get; set; }
    [Parameter] public EventCallback<string> OnPrintError { get; set; }

    private bool IsLoading = false;
    private string CurrentPrintType = "";

    private async Task PrintReport(string printType)
    {
        if (InvoiceId == Guid.Empty)
        {
            Snackbar.Add("معرف الفاتورة غير صحيح", Severity.Error);
            return;
        }

        try
        {
            IsLoading = true;
            CurrentPrintType = printType;
            StateHasChanged();

            // إشعار بدء الطباعة
            await OnPrintStarted.InvokeAsync(printType);

            // بناء رابط التقرير
            var reportUrl = $"{ApiEndpoint}/{InvoiceId}?printType={printType}";

            if (printType == "Thermal")
            {
                // للطباعة الحرارية - فتح في نافذة جديدة للطباعة المباشرة
                await JSRuntime.InvokeVoidAsync("window.open", reportUrl, "_blank");
                Snackbar.Add("تم إرسال التقرير للطباعة الحرارية", Severity.Success);
            }
            else
            {
                // لطباعة A4 - تحميل الملف
                await JSRuntime.InvokeVoidAsync("downloadFile", reportUrl, $"فاتورة_مبيعات_{InvoiceId}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf");
                Snackbar.Add("تم تحميل ملف PDF بنجاح", Severity.Success);
            }

            // إشعار اكتمال الطباعة
            await OnPrintCompleted.InvokeAsync(printType);
        }
        catch (Exception ex)
        {
            var errorMessage = $"خطأ في {(printType == "Thermal" ? "الطباعة الحرارية" : "تحميل PDF")}: {ex.Message}";
            Snackbar.Add(errorMessage, Severity.Error);
            await OnPrintError.InvokeAsync(errorMessage);
        }
        finally
        {
            IsLoading = false;
            CurrentPrintType = "";
            StateHasChanged();
        }
    }
}

<script>
    window.downloadFile = (url, filename) => {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };
</script>

<style>
    .gap-2 {
        gap: 0.5rem;
    }
</style>
