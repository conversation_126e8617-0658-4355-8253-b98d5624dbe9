﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Clients;

public class ClientDTO
{
    public Guid Id { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    [StringLength(100, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
    public string Name { get; set; }
    public int? Phone { get; set; }
    public string? Address { get; set; }
    public decimal Balance { get; set; }
    public bool IsSupplier { get; set; }
    public bool IsCustomer { get; set; }

}
