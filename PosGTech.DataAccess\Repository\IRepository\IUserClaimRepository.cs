using Microsoft.AspNetCore.Identity;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Users;

namespace PosGTech.DataAccess.Repository.IRepository
{
    /// <summary>
    /// Interface لإدارة UserClaim للصلاحيات الإضافية والمحذوفة
    /// </summary>
    public interface IUserClaimRepository
    {
        /// <summary>
        /// إضافة صلاحية إضافية للمستخدم
        /// </summary>
        Task<ResponseVM> AddAdditionalPermission(Guid userId, string permission);

        /// <summary>
        /// إزالة صلاحية إضافية من المستخدم
        /// </summary>
        Task<ResponseVM> RemoveAdditionalPermission(Guid userId, string permission);

        /// <summary>
        /// إضافة صلاحية محذوفة من الدور
        /// </summary>
        Task<ResponseVM> AddRemovedPermission(Guid userId, string permission);

        /// <summary>
        /// إزالة صلاحية محذوفة (استعادة الصلاحية للدور)
        /// </summary>
        Task<ResponseVM> RemoveRemovedPermission(Guid userId, string permission);

        /// <summary>
        /// الحصول على الصلاحيات الإضافية للمستخدم
        /// </summary>
        Task<List<UserClaimDTO>> GetUserAdditionalPermissions(Guid userId);

        /// <summary>
        /// الحصول على الصلاحيات المحذوفة من الدور للمستخدم
        /// </summary>
        Task<List<UserClaimDTO>> GetUserRemovedPermissions(Guid userId);

        /// <summary>
        /// الحصول على جميع Claims المستخدم
        /// </summary>
        Task<List<UserClaimDTO>> GetAllUserClaims(Guid userId);

        /// <summary>
        /// إدارة صلاحيات المستخدم بشكل مجمع
        /// </summary>
        Task<ResponseVM> ManageUserPermissions(UserPermissionManagementDTO model);

        /// <summary>
        /// التحقق من وجود صلاحية معينة للمستخدم
        /// </summary>
        Task<bool> UserHasPermission(Guid userId, string permission);

        /// <summary>
        /// الحصول على تاريخ تغييرات صلاحيات المستخدم
        /// </summary>
        Task<List<UserPermissionHistoryDTO>> GetUserPermissionHistory(Guid userId);

        /// <summary>
        /// الحصول على الصلاحيات الفعلية للمستخدم
        /// </summary>
        Task<UserEffectivePermissionsDTO> GetUserEffectivePermissions(Guid userId);
    }
}
