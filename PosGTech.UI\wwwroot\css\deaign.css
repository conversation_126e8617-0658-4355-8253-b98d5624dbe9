﻿:root {
    --primary-color: #2196f3;
    --primary-dark: #1976d2;
    --error-color: #f44336;
    --surface-color: #ffffff;
    --text-primary: rgba(0, 0, 0, 0.87);
}

@media (prefers-color-scheme: dark) {
    :root {
        --surface-color: #1e1e1e;
        --text-primary: rgba(255, 255, 255, 0.87);
    }
}

.mud-dialog {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    background-color: #ffffff;
}

.header-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mud-dialog-title {
    margin: 0 !important;
    padding: 16px !important;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    color: #495057;
}

.mud-dialog-content {
    overflow: visible;
    padding: 24px;
    color: #495057;
    background-color: #ffffff;
}

.validation-message {
    color: var(--error-color) !important;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.custom-input {
    transition: all 0.3s ease;
}

    .custom-input:focus-within {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
    }

.rounded-pill {
    border-radius: 50px !important;
}

.btn-cancel {
    transition: all 0.3s ease;
    margin-right: 12px;
}

    .btn-cancel:hover {
        background-color: rgba(244, 67, 54, 0.04);
        transform: translateY(-1px);
    }

.btn-save {
    transition: all 0.3s ease;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

    .btn-save:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25);
    }

.mud-card {
    background-color: var(--surface-color);
    color: var(--text-primary);
    padding: 16px;
}

.mud-input {
    border-radius: 8px;
    color: var(--text-primary);
}

.mud-input-outlined-border {
    border-color: rgba(255, 255, 255, 0.12);
}

/* إضافة تنسيقات للنصوص في الوضع الداكن */
.mud-text {
    color: var(--text-primary);
}

.mud-input-label {
    color: var(--text-primary);
}


/*-------------------------------------------------------------------------*/

/* المتغيرات والألوان الأساسية */
:root {
    --primary-gradient: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
    --surface-gradient: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
    --error-gradient: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* تنسيق العنوان الرئيسي */
h3 {
    color: #1565c0;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding: 0.5rem 1rem;
    border-right: 4px solid #1565c0;
    background: rgba(30, 136, 229, 0.05);
    border-radius: var(--border-radius);
}

/* تنسيق الحاوية الرئيسية */
.mud-container {
    margin-top: 1rem;
}

/* تنسيق الورقة الرئيسية */
.mud-paper {
    transition: var(--transition);
    border-radius: var(--border-radius) !important;
}

/* تنسيق رأس البحث */
.primary {
    background: var(--primary-gradient) !important;
    padding: 1.5rem !important;
}
/* تنسيق حقل البحث */
.mud-input.mud-input-filled {
    background: rgba(255, 255, 255, 0.9) !important;
    transition: var(--transition);
}

    .mud-input.mud-input-filled:hover {
        background: rgba(255, 255, 255, 1) !important;
        transform: translateY(-1px);
    }

    /* تنسيقات خاصة بالنص في حقل البحث */
    .mud-input.mud-input-filled .mud-input-slot {
        color: #1e1e1e !important; /* لون النص الأساسي */
    }

    .mud-input.mud-input-filled input::placeholder {
        color: rgba(0, 0, 0, 0.6) !important; /* لون النص الشبح */
    }

    .mud-input.mud-input-filled.mud-input-adorned-end .mud-input-adornment {
        color: #1565c0 !important; /* لون أيقونة البحث */
    }

    /* تحسين تباين النص عند التركيز */
    .mud-input.mud-input-filled:focus-within {
        background: white !important;
    }

        .mud-input.mud-input-filled:focus-within .mud-input-slot {
            color: #1e1e1e !important;
        }
    .mud-input.mud-input-filled:hover {
        background: rgba(255, 255, 255, 1) !important;
        transform: translateY(-1px);
    }

/* تنسيق زر الإضافة */
.mud-button-filled {
    transition: var(--transition);
    font-weight: 500;
}

    .mud-button-filled:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    }

/* تنسيق الجدول */
.mud-table {
    background: white;
    border-radius: 0 0 var(--border-radius) var(--border-radius) !important;
}

.mud-table-container {
    max-height: calc(100vh - 250px);
}

.mud-table-cell {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 12px 16px;
}

.mud-table-head {
    background-color: rgba(0, 0, 0, 0.02);
}

    .mud-table-head .mud-table-cell {
        font-weight: 600;
        color: #1565c0;
    }

/* تنسيق الصفوف */
.mud-table-row {
    transition: var(--transition);
}

    .mud-table-row:hover {
        background-color: rgba(0, 0, 0, 0.02);
    }

/* تنسيق الأزرار في الجدول */
.mud-button-group {
    box-shadow: none !important;
    border-radius: var(--border-radius);
    overflow: hidden;
}

    .mud-button-group .mud-icon-button {
        transition: var(--transition);
        padding: 8px !important;
    }

        .mud-button-group .mud-icon-button:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

/* تنسيق الرقاقة (Chip) */
.mud-chip {
    font-weight: 500;
}

/* تنسيق رسائل عدم وجود بيانات */
.mud-text {
    color: rgba(0, 0, 0, 0.6);
}

/* تنسيق مربع الحوار */
.mud-dialog {
    border-radius: 8px;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
}

.mud-dialog-title {
    background-color: #f8f9fa;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
}

/* تنسيقات الأزرار المستديرة */
.rounded-pill {
    border-radius: 50px !important;
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
}

/* تنسيقات التحميل */
.mud-progress-circular {
    animation: spin 2s linear infinite;
}

@keyframes spin {
    100% {
        transform: rotate(360deg);
    }
}

/* تنسيقات الوضع الداكن */
@media (prefers-color-scheme: dark) {
    .mud-table {
        background: #1a1a1a;
    }

    .mud-table-head {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .mud-table-cell {
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .mud-table-row:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .mud-text {
        color: rgba(255, 255, 255, 0.7);
    }

    /* تنسيق الدايلوج في الوضع الداكن */
    .mud-dialog {
        background-color: #2d2d30;
        border: 1px solid #3e3e42;
    }

    .mud-dialog-title {
        background-color: #3e3e42;
        color: #ffffff;
        border-bottom: 1px solid #4d4d50;
    }

    .mud-dialog-content {
        background-color: #2d2d30;
        color: #ffffff;
    }
}

/* تنسيقات الحركة */
.animate__animated {
    animation-duration: 0.5s;
}