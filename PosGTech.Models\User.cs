﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول المستخدمين
    /// </summary>
    public class User : IdentityUser<Guid>
    {
        public string Name { get; set; }
        public virtual ICollection<UserTreasury> UserTreasuries { get; set; } = new List<UserTreasury>();
        public List<RefreshToken>? RefreshTokens { get; set; }
        public ICollection<UserRole> UserRoles { get; set; }
    }
    [Owned]
    public class RefreshToken
    {
        public string Token { get; set; }
        public DateTime ExpiresOn { get; set; }
        public bool IsExpired => DateTime.UtcNow >= ExpiresOn;
        public DateTime CreatedOn { get; set; }
        public DateTime? RevokedOn { get; set; }
        public bool IsActive => RevokedOn == null && !IsExpired;
    }
    public class UserRole : IdentityUserRole<Guid>
    {
        public virtual User User { get; set; }
        public virtual Role Role { get; set; }
    }
    public class Role : IdentityRole<Guid>
    {
        public ICollection<UserRole> UserRoles { get; set; }
    }
}
