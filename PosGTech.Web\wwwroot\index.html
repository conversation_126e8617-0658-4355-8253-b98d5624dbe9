<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PosGTech.Web</title>
    <base href="/" />
    <link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="css/app.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="PosGTech.Web.styles.css" rel="stylesheet" />
    <link href="manifest.webmanifest" rel="manifest" />
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap" rel="stylesheet" />
    <link href="_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
    <link href="_content/CodeBeam.MudBlazor.Extensions/MudExtensions.min.css" rel="stylesheet" />
    <link href="css/styleFeilds.css" rel="stylesheet" />
 
</head>

<body>
    <div id="app">
        <svg class="loading-progress">
            <circle r="40%" cx="50%" cy="50%" />
            <circle r="40%" cx="50%" cy="50%" />
        </svg>
        <div class="loading-progress-text"></div>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

    <script type="text/javascript">
        // Global error handling setup
        let globalErrorHandler = null;

        function setupGlobalErrorHandling(dotNetHelper) {
            globalErrorHandler = dotNetHelper;

            // Handle unhandled JavaScript errors
            window.addEventListener('error', function(event) {
                console.log('JavaScript Error Caught:', {
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    error: event.error
                });

                if (globalErrorHandler) {
                    console.log('Calling .NET error handler...');
                    globalErrorHandler.invokeMethodAsync('HandleJavaScriptErrorAsync',
                        event.message || 'Unknown error',
                        event.filename || 'Unknown source',
                        event.lineno || 0,
                        event.colno || 0,
                        event.error ? event.error.toString() : 'No error details'
                    );
                } else {
                    console.log('No global error handler available');
                }
            });

            // Handle unhandled promise rejections
            window.addEventListener('unhandledrejection', function(event) {
                console.log('Promise Rejection Caught:', event.reason);

                if (globalErrorHandler) {
                    console.log('Calling .NET promise rejection handler...');
                    globalErrorHandler.invokeMethodAsync('HandleUnhandledPromiseRejectionAsync',
                        event.reason ? event.reason.toString() : 'Unknown promise rejection'
                    );
                } else {
                    console.log('No global error handler available for promise rejection');
                }
            });

            console.log('Global error handling initialized');
        }

        function printPDF(bytes) {
  // Convert bytes to Blob
  const blob = new Blob([bytes], { type: 'application/pdf' });
  const url = URL.createObjectURL(blob);

  // Open in new window and print
  const win = window.open(url, '_blank');
  if (win) {
    // Wait for the window to load and trigger print
    win.addEventListener('load', () => {
      setTimeout(() => {
        win.print();
      }, 1000); // Adjust timeout if needed
    });
  } else {
    alert('Failed to open the PDF. Please check pop-up blockers.');
  }
}
        //function printPDF(pdfBytes) {
        //    // 1. إنشاء Blob من البايتات
        //    const blob = new Blob([pdfBytes], { type: 'application/pdf' });

        //    // 2. إنشاء رابط مؤقت للـ Blob
        //    const url = URL.createObjectURL(blob);

        //    // 3. إنشاء إطار مخفي
        //    const iframe = document.createElement('iframe');
        //    iframe.style.display = 'none'; // إخفاء الإطار

        //    // 4. عند اكتمال التحميل، اطبع ثم نظف
        //    iframe.onload = function () {
        //        iframe.contentWindow.print();
        //        URL.revokeObjectURL(url); // تحرير الذاكرة
        //        document.body.removeChild(iframe); // حذف الإطار
        //    };

        //    // 5. إضافة الإطار إلى الصفحة وتحميل الـ PDF
        //    document.body.appendChild(iframe);
        //    iframe.src = url;
        //}
        //function saveAsFile(filename, bytesBase64) {
        //    if (navigator.msSaveBlob) {
        //        //Download document in Edge browser
        //        var data = window.atob(bytesBase64);
        //        var bytes = new Uint8Array(data.length);
        //        for (var i = 0; i < data.length; i++) {
        //            bytes[i] = data.charCodeAt(i);
        //        }
        //        var blob = new Blob([bytes.buffer], { type: "application/octet-stream" });
        //        navigator.msSaveBlob(blob, filename);
        //    }
        //    else {
        //        var link = document.createElement('a');
        //        link.download = filename;
        //        link.href = "data:application/octet-stream;base64," + bytesBase64;
        //        document.body.appendChild(link); // Needed for Firefox
        //        link.click();
        //        document.body.removeChild(link);
        //    }
        //}

        //function downloadFromByteArray(options) {
        //    var url = typeof (options.byteArray) === 'string' ? "data:" + options.contentType + ";base64," + options.byteArray : URL.createObjectURL(new Blob([options.byteArray], { type: options.contentType }));
        //    downloadFromUrl({ url: url, fileName: options.fileName });
        //    if (typeof (options.byteArray) !== 'string') URL.revokeObjectURL(url);
        //}
        //function downloadFromUrl(options) {
        //    var _a;
        //    var anchorElement = document.createElement('a');
        //    anchorElement.href = options.url;
        //    anchorElement.download = (_a = options.fileName) !== null && _a !== void 0 ? _a : '';
        //    anchorElement.click();
        //    anchorElement.remove();
        //}
        //function saveAsFile(filename, bytesBase64) {
        //    if (navigator.msSaveBlob) {
        //        //Download document in Edge browser var data = window.atob(bytesBase64);
        //        var bytes = new Uint8Array(data.length);
        //        for (var i = 0; i < data.length; i++) {
        //            bytes[i] = data.charCodeAt(i);
        //        }

        //        var blob = new Blob([bytes.buffer], { type: "application/octet-stream" });
        //        navigator.msSaveBlob(blob, filename);
        //    }

        //    else {
        //        var link = document.createElement('a');
        //        link.download = filename;
        //        link.href = "data:application/octet-stream;base64," + bytesBase64;
        //        document.body.appendChild(link);
        //        // Needed for Firefox link.click();
        //        document.body.removeChild(link);
        //    }

        //}
    </script>
    <!-- Print.js Library for Receipt Printing -->
    <script src="https://printjs-4de6.kxcdn.com/print.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://printjs-4de6.kxcdn.com/print.min.css">

    <!-- Google Fonts for Arabic Support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom Receipt Print Functions -->
    <script src="js/receipt-print.js"></script>

    <script src="_framework/blazor.webassembly.js"></script>
    <script>navigator.serviceWorker.register('service-worker.js');</script>
    <script src="_content/MudBlazor/MudBlazor.min.js"></script>
    <script src="_content/CodeBeam.MudBlazor.Extensions/MudExtensions.min.js"></script>
    <script src="js/error-testing.js"></script>
</body>

</html>
