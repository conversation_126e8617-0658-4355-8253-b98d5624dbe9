﻿@using PosGTech.ModelsDTO.Authorization

<MudNavMenu>
    <MudNavLink Href="" Match="NavLinkMatch.All" Icon="@Home">الرئيسية</MudNavLink>
    <MudNavGroup Title="إدارة الفواتير" Icon="@Fwater" IconColor="Color.Primary" Expanded="false">
        <AuthorizeView Policy="@PermissionConstants.SalesView" Context="auth">
            <Authorized>
                <MudNavLink OnClick="ShowSell" Match="NavLinkMatch.All" Icon="@Sells"> المبيعات </MudNavLink>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@PermissionConstants.InventoryView" Context="auth">
            <Authorized>
                <MudNavLink Href="listInventories" Match="NavLinkMatch.All" Icon="@Purchase">جرد</MudNavLink>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@PermissionConstants.PurchasesView" Context="auth">
            <Authorized>
                <MudNavLink OnClick="ShowPurchase" Match="NavLinkMatch.All" Icon="@Purchase">فاتورة المشتريات</MudNavLink>
            </Authorized>
        </AuthorizeView>
    </MudNavGroup>
    <MudNavGroup Title="البيانات الاسياسية" Icon="@BaseData" IconColor="Color.Primary" Expanded="false">
        <AuthorizeView Policy="@PermissionConstants.CategoriesView" Context="auth">
            <Authorized>
                <MudNavLink Href="listCategories" Match="NavLinkMatch.All" Icon="@Category">التصنيفات</MudNavLink>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@PermissionConstants.ItemsView" Context="auth">
            <Authorized>
                <MudNavLink Href="listItems" Match="NavLinkMatch.All" Icon="@items">الاصناف</MudNavLink>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@PermissionConstants.ExpensesView" Context="auth">
            <Authorized>
                <MudNavLink Href="listExpenses" Match="NavLinkMatch.All" Icon="@Expenses">المصروفات</MudNavLink>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@PermissionConstants.UnitsView" Context="auth">
            <Authorized>
                <MudNavLink Href="listUnits" Match="NavLinkMatch.All" Icon="@Expenses">الوحدات</MudNavLink>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@PermissionConstants.StoresView" Context="auth">
            <Authorized>
                <MudNavLink Href="listStores" Match="NavLinkMatch.All" Icon="@Expenses">المخازن</MudNavLink>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@PermissionConstants.StoresView" Context="auth">
            <Authorized>
                <MudNavLink Href="listStoreSettings" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Store">إعدادات المتاجر</MudNavLink>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@PermissionConstants.TreasuriesView" Context="auth">
            <Authorized>
                <MudNavLink Href="ViewTreasuries" Match="NavLinkMatch.All" Icon="@Expenses">الخزائن</MudNavLink>
            </Authorized>
        </AuthorizeView>
    </MudNavGroup>
    <MudNavGroup Title="المالية" Icon="@Finance" IconColor="Color.Primary" Expanded="false">
        <AuthorizeView Policy="@PermissionConstants.ReceiptsView" Context="auth">
            <Authorized>
                <MudNavLink Href="listReceipts" Match="NavLinkMatch.All" Icon="@Receipt">الإيصالات</MudNavLink>
            </Authorized>
        </AuthorizeView>
    </MudNavGroup>

    <MudNavGroup Title="التقارير" Icon="@Icons.Material.Filled.Assessment" IconColor="Color.Primary" Expanded="false">
        <AuthorizeView Policy="@PermissionConstants.ReportsView" Context="auth">
            <Authorized>
                <MudNavLink Href="salesReport" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.BarChart">تقرير المبيعات</MudNavLink>
            </Authorized>
        </AuthorizeView>
    </MudNavGroup>

    <MudNavGroup Title="إدارة العلاقات" Icon="@Connection" IconColor="Color.Primary" Expanded="false">
        <AuthorizeView Policy="@PermissionConstants.EmployeesView" Context="auth">
            <Authorized>
                <MudNavLink Href="ListEmployees" Icon="@Emplyee">ادارة الموظفين</MudNavLink>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@PermissionConstants.ClientsView" Context="auth">
            <Authorized>
                <MudNavLink Href="ListClients" Icon="@client">ادارة العملاء</MudNavLink>
            </Authorized>
        </AuthorizeView>
    </MudNavGroup>

    <MudNavGroup Title="إدارة النظام" Icon="@System" IconColor="Color.Primary" Expanded="false">
        <AuthorizeView Policy="@PermissionConstants.UsersView" Context="auth">
            <Authorized>
                <MudNavLink Href="listUsers" Match="NavLinkMatch.All" Icon="@Users">المستخدمون</MudNavLink>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@PermissionConstants.RolesView" Context="auth">
            <Authorized>
                <MudNavLink Href="listRoles" Match="NavLinkMatch.All" Icon="@Roles">الأدوار والصلاحيات</MudNavLink>
            </Authorized>
        </AuthorizeView>
        <!-- رابط مؤقت للتشخيص -->
        <MudNavLink Href="debug/claims" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.BugReport">تشخيص Claims</MudNavLink>
    </MudNavGroup>


</MudNavMenu>
