﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول فواتير المشتريات
    /// </summary>
    public class Inventory : BaseEntity
    {

        public int InvoiceNo { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public DateOnly Date { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
        public decimal Total { get; set; }
        public string? Note { get; set; }
        public Guid StoreId { get; set; }
        public Store? Store { get; set; }
        public ICollection<InventoryItem>? InventoryItems { get; set; }
    }
}
