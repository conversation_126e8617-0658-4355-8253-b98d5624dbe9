﻿@using Blazored.LocalStorage
@using PosGTech.Web.Services
@using static System.Net.Mime.MediaTypeNames

@inject IDialogService DialogService 
@inject ILocalStorageService _localStorage
@inject IdentityAuthenticationStateProvider authStateProvider

@code {
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (await _localStorage.GetItemAsStringAsync("authToken") != null) authStateProvider.SetToken();
        else  _navigation.NavigateTo("/login");
    }
}
