﻿using AutoMapper;
using PosGTech.Mapper;
using PosGTech.Models;
using PosGTech.ModelsDTO.Items;

namespace PosGTech.Tests.Mapping;

public class ItemMappingTests
{
    private readonly IMapper _mapper;

    public ItemMappingTests()
    {
        var configuration = new MapperConfiguration(cfg =>
        {
            cfg.AddProfile<MappingProfile>();
        });
        _mapper = configuration.CreateMapper();
    }

    [Fact]
    public void Item_To_ItemDTO_Should_Calculate_Quantity_From_StoreItems()
    {
        // Arrange
        var item = new Item
        {
            Id = Guid.NewGuid(),
            Name = "Test Item",
            CategoryId = Guid.NewGuid(),
            CostPrice = 100m,
            IsHaveExp = false,
            StoreItems = new List<StoreItem>
            {
                new StoreItem
                {
                    Id = Guid.NewGuid(),
                    ItemId = Guid.NewGuid(),
                    StoreId = Guid.NewGuid(),
                    StoreItemExps = new List<StoreItemExp>
                    {
                        new StoreItemExp { Id = Guid.NewGuid(), Quantity = 10m },
                        new StoreItemExp { Id = Guid.NewGuid(), Quantity = 15m }
                    }
                },
                new StoreItem
                {
                    Id = Guid.NewGuid(),
                    ItemId = Guid.NewGuid(),
                    StoreId = Guid.NewGuid(),
                    StoreItemExps = new List<StoreItemExp>
                    {
                        new StoreItemExp { Id = Guid.NewGuid(), Quantity = 20m }
                    }
                }
            }
        };

        // Act
        var itemDto = _mapper.Map<ItemDTO>(item);

        // Assert
        Assert.NotNull(itemDto);
        Assert.Equal(item.Name, itemDto.Name);
        Assert.Equal(item.CostPrice, itemDto.CostPrice);
        Assert.Equal(45m, itemDto.Quantity); // 10 + 15 + 20 = 45
    }

    [Fact]
    public void Item_To_ItemDTO_Should_Return_Zero_Quantity_When_No_StoreItems()
    {
        // Arrange
        var item = new Item
        {
            Id = Guid.NewGuid(),
            Name = "Test Item",
            CategoryId = Guid.NewGuid(),
            CostPrice = 100m,
            IsHaveExp = false,
            StoreItems = null
        };

        // Act
        var itemDto = _mapper.Map<ItemDTO>(item);

        // Assert
        Assert.NotNull(itemDto);
        Assert.Equal(0m, itemDto.Quantity);
    }

    [Fact]
    public void Item_To_ItemDTO_Should_Return_Zero_Quantity_When_Empty_StoreItems()
    {
        // Arrange
        var item = new Item
        {
            Id = Guid.NewGuid(),
            Name = "Test Item",
            CategoryId = Guid.NewGuid(),
            CostPrice = 100m,
            IsHaveExp = false,
            StoreItems = new List<StoreItem>()
        };

        // Act
        var itemDto = _mapper.Map<ItemDTO>(item);

        // Assert
        Assert.NotNull(itemDto);
        Assert.Equal(0m, itemDto.Quantity);
    }
}
