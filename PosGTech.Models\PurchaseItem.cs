﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول مواد المشتريات
    /// </summary>
    public class PurchaseItem : BaseEntity
    {
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal Quantity { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal Price { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal PriceAfterDiscount { get; set; }
        public DateOnly? Exp { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid? ItemId { get; set; }
        public Item? Item { get; set; }
        public Guid? PurchaseId { get; set; }
        public Purchase? Purchase { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid ItemUnitId { get; set; }
        public ItemUnit? ItemUnit { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal ReturnQuantity { get; set; } = 0;
    }
}
