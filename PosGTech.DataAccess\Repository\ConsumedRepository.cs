﻿using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;

namespace PosGTech.DataAccess.Repository
{
    public class ConsumedRepository : Repository<Consumed>, IConsumedRepository
    {
        IItemRepository _itemRepository;
        IConsumedItemRepository _consumedItem;
        public ConsumedRepository(ApplicationDbContext db, IItemRepository itemRepository, IConsumedItemRepository consumedItem) : base(db)
        {
            _itemRepository = itemRepository;
            _consumedItem = consumedItem;
        }
        public async Task AddConsumed(Consumed consumed)
        {
            //consumed.ConsumedItems = consumed.ConsumedItems.Select(x => { x.Item = null; return x; }).ToList();
            //await _itemRepository.UpdateReduceQuantityForConsumed(consumed.ConsumedItems);
            //consumed.InvoiceNo = ((await GetLastOrDefault(order: x => x.CreatedAt, tracked: false))?.InvoiceNo).GetValueOrDefault(0) + 1;
            Add(consumed);
        }
        public async Task DeleteConsumed(Consumed consumed)
        {
            //await _itemRepository.UpdateAddQuantityForConsumed(consumed.ConsumedItems);
            Remove(consumed);
        }

        public async Task UpdateConsumed(Consumed consumed, Consumed model)
        {
            //await _itemRepository.UpdateAddQuantityForConsumed(consumed.ConsumedItems);
            //_consumedItem.RemoveRange(consumed.ConsumedItems);
            //consumed.ConsumedItems = model.ConsumedItems.Select(x => { x.Item = null; return x; }).ToList();
            //await _itemRepository.UpdateReduceQuantityForConsumed(consumed.ConsumedItems);
            //_consumedItem.AddRange(consumed.ConsumedItems.ToList());
            //consumed.TotalPrice = model.TotalPrice;
            //consumed.Date = model.Date;
            //consumed.UpdatedAt = DateTime.Now;
            Update(consumed);

        }
    }
}
