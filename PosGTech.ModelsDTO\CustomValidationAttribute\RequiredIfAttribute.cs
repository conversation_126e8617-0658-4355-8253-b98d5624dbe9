﻿using PosGTech.ModelsDTO.Finacnial;
using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.CustomValidationAttribute;

public class RequiredIfAttribute : ValidationAttribute
{
    readonly RequiredAttribute _innerAttribute = new RequiredAttribute();
    private string[] _targetValue { get; }

    public RequiredIfAttribute(string[] targetValue)
    {
        _targetValue = targetValue;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var field = validationContext.ObjectType.GetProperty("Financial");
        if (field != null)
        {
            FinancialDTO? dependentValue = (FinancialDTO?)field.GetValue(validationContext.ObjectInstance, null);
            if ((dependentValue == null && _targetValue == null) || _targetValue.Contains(dependentValue.Name))
            {
                if (!_innerAttribute.IsValid(value))
                {
                    var name = validationContext.DisplayName;
                    var specificErrorMessage = ErrorMessage;
                    if (string.IsNullOrEmpty(specificErrorMessage))
                        specificErrorMessage = $"{name} is required.";

                    return new ValidationResult(specificErrorMessage, new[] { validationContext.MemberName });
                }
            }
            return ValidationResult.Success;
        }
        return new ValidationResult(FormatErrorMessage("Financial"));
    }
}
