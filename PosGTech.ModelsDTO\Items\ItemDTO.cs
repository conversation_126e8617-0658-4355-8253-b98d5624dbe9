﻿using PosGTech.Models;
using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Items;

public class ItemDTO
{
    public Guid Id { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    [StringLength(100, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
    public string Name { get; set; }
    public decimal Quantity { get; set; } = decimal.Zero;
    public bool IsHaveExp { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public Guid CategoryId { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
    [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
    public decimal CostPrice { get; set; } = decimal.Zero;
    public ICollection<ItemNumDTO>? ItemNums { get; set; } = new HashSet<ItemNumDTO>();
    public ICollection<ItemUnitDTO>? ItemUnits { get; set; } = new HashSet<ItemUnitDTO>();

    /// <summary>
    /// سعر التكلفة للوحدة الأساسية مع اسم الوحدة للعرض
    /// </summary>
    public string CostPriceForBasicUnitDisplay
    {
        get
        {
            // البحث عن الوحدة الأساسية
            var basicUnit = ItemUnits?.FirstOrDefault(x => x.IsBasicUnit);
            if (basicUnit == null)
                return CostPrice.ToString("F2");

            // سعر التكلفة محفوظ بالفعل للوحدة الأساسية، لذا نعيده مع اسم الوحدة
            return $"{CostPrice:F2} / {basicUnit.Unit?.Name ?? "وحدة"}";
        }
    }

    /// <summary>
    /// سعر التكلفة للوحدة الأساسية فقط (رقم)
    /// </summary>
    public decimal CostPriceForBasicUnit
    {
        get
        {
            // سعر التكلفة محفوظ بالفعل للوحدة الأساسية في قاعدة البيانات
            return CostPrice;
        }
    }

    /// <summary>
    /// سعر البيع للوحدة الأساسية مع اسم الوحدة للعرض
    /// </summary>
    public string SalePriceForBasicUnitDisplay
    {
        get
        {
            // البحث عن الوحدة الأساسية
            var basicUnit = ItemUnits?.FirstOrDefault(x => x.IsBasicUnit);
            if (basicUnit == null)
                return "0.00";

            // عرض سعر البيع مع اسم الوحدة
            return $"{basicUnit.SalePrice:F2} / {basicUnit.Unit?.Name ?? "وحدة"}";
        }
    }

    /// <summary>
    /// سعر البيع للوحدة الأساسية فقط (رقم)
    /// </summary>
    public decimal SalePriceForBasicUnit
    {
        get
        {
            // البحث عن الوحدة الأساسية وإرجاع سعر البيع
            var basicUnit = ItemUnits?.FirstOrDefault(x => x.IsBasicUnit);
            return basicUnit?.SalePrice ?? 0;
        }
    }
}
public static class ItemExtensions
{
    public static decimal GetPriceUnitDTO(ItemUnitDTO unit, decimal price)
    {
        return PriceUnit(unit.IsBasicUnit, unit.IsBigger, unit.Quantity, price);
    }
    public static decimal GetPriceUnit(ItemUnit unit, decimal price)
    {
        return PriceUnit(unit.IsBasicUnit, unit.IsBigger, unit.Quantity, price);
    }
    static decimal PriceUnit(bool IsBasicUnit, bool IsBigger, decimal Quantity, decimal price)
    {
        return IsBasicUnit ? price : IsBigger ? price * Quantity : price / Quantity;
    }

    /// <summary>
    /// Convert cost price from any unit to basic unit (for purchase processing)
    /// This is the inverse of PriceUnit for cost price calculations
    /// </summary>
    public static decimal GetCostPriceForBasicUnitDTO(ItemUnitDTO unit, decimal costPrice)
    {
        return CostPriceForBasicUnit(unit.IsBasicUnit, unit.IsBigger, unit.Quantity, costPrice);
    }

    /// <summary>
    /// Convert cost price from any unit to basic unit (for purchase processing)
    /// This is the inverse of PriceUnit for cost price calculations
    /// </summary>
    public static decimal GetCostPriceForBasicUnit(ItemUnit unit, decimal costPrice)
    {
        return CostPriceForBasicUnit(unit.IsBasicUnit, unit.IsBigger, unit.Quantity, costPrice);
    }

    static decimal CostPriceForBasicUnit(bool IsBasicUnit, bool IsBigger, decimal Quantity, decimal costPrice)
    {
        // For cost price conversion to basic unit:
        // - If it's already basic unit, return as is
        // - If it's a bigger unit (like box), divide by quantity to get per-unit cost
        // - If it's a smaller unit (like half), divide by quantity to get per-unit cost
        return IsBasicUnit ? costPrice : costPrice / Quantity;
    }
    public static decimal GetQuantityUnitDTO(ItemUnitDTO unit, decimal quantity)
    {
        return QuantityUnit(unit.IsBasicUnit, unit.IsBigger, unit.Quantity, quantity);
    }
    public static decimal GetQuantityUnit(ItemUnit unit, decimal quantity)
    {
        return QuantityUnit(unit.IsBasicUnit, unit.IsBigger, unit.Quantity, quantity);
    }
    static decimal QuantityUnit(bool IsBasicUnit, bool IsBigger, decimal Quantity, decimal quantity)
    {
        return IsBasicUnit ? quantity : IsBigger ? quantity * Quantity : quantity / Quantity;
    }
}
