﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول المواد المستهكلة لكل فاتورة
    /// </summary>
    public class ConsumedItem : BaseEntity
    {
        public Guid? ConsumedId { get; set; }
        public Consumed? Consumed { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal Quantity { get; set; } = 1;
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal Price { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid StoreItemExpId { get; set; }
        public StoreItemExp? StoreItemExp { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid ItemUnitId { get; set; }
        public ItemUnit? ItemUnit { get; set; }
    }
}
