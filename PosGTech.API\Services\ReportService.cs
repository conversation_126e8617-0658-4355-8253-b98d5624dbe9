﻿

using Microsoft.JSInterop;
using Microsoft.Reporting.NETCore;
using System.Data;
using System.Reflection;
using static System.Net.Mime.MediaTypeNames;

namespace PosGTech.API.Services;

public class ReportService
{

    public byte[] CreateReport(DataSet DS, string ReportName, List<string> DataSourceNames, string files , ReportParameter[]? Params = null)
    {

        string reportPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, files, ReportName); //or webHostEnvironment.WebRootPath if your report is in wwwroot folder

        Stream reportDefinition;
        using var fs = new FileStream(reportPath, FileMode.Open);
        reportDefinition = fs;
        LocalReport report = new LocalReport();
        report.EnableExternalImages = true;
        report.LoadReportDefinition(reportDefinition);
        for (int i = 0; i < DataSourceNames.Count; i++)
        {
            report.DataSources.Add(new(DataSourceNames[i], DS.Tables[i]));
        }
        if (Params != null) report.SetParameters(Params);
        report.Refresh();
        fs.Dispose();

        byte[] pdf = report.Render("pdf");

        return pdf;


     



    }
    public void AddDataSources<T>(List<T> list, ref DataSet DS)
    {
        try
        {
            var dt = new DataTable();
            Type objectType = typeof(T);
            // Get all properties of the object type
            var properties = objectType.GetProperties();

            // Create columns in the DataTable based on the properties
            foreach (var property in properties)
            {
                dt.Columns.Add(property.Name, property.PropertyType);
            }

            // Iterate through the list and populate the DataTable
            foreach (var item in list)
            {
                DataRow row = dt.NewRow();

                // Set the values of the row based on the object's properties
                foreach (var property in properties)
                {
                    row[property.Name] = property.GetValue(item);
                }
                dt.Rows.Add(row);
            }
            DS.Tables.Add(dt);
        }
        catch (Exception e)
        {

            throw;
        }

    }
}
