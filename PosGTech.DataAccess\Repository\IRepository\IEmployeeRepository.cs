﻿using PosGTech.Models;
using PosGTech.ModelsDTO.Employees;

namespace PosGTech.DataAccess.Repository.IRepository
{
    public interface IEmployeeRepository : IRepository<Employee>
    {
        void UpdateEmployee(Employee oldEmployee, EmployeeDTO newEmployee);
        Task UpdateEmployeeBalanceInc(Guid employeeId, decimal newBalance);
        Task UpdateEmployeeBalanceDec(Guid employeeId, decimal newBalance);

    }
}
