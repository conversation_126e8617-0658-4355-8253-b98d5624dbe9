using Microsoft.EntityFrameworkCore;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository;
using PosGTech.Models;

namespace PosGTech.Tests.Repositories;

public class ItemRepositoryTests : IDisposable
{
    private readonly ApplicationDbContext _context;

    public ItemRepositoryTests()
    {
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: $"TestDatabase_{Guid.NewGuid()}")
            .Options;

        _context = new ApplicationDbContext(options);
    }

    [Fact]
    public async Task Add_SavesItem()
    {
        // Arrange
        var item = new Item
        {
            Id = Guid.NewGuid(),
            Name = "TestItem",
            CategoryId = Guid.NewGuid(),
            //Barcode = "123456789",
            //Description = "Test Description"
        };
        var repository = new ItemRepository(_context, new ItemNumRepository(_context), new ItemUnitRepository(_context));

        // Act
        repository.Add(item);

        // Assert
        var savedItem = await _context.Items.FindAsync(item.Id);
        Assert.NotNull(savedItem);
        Assert.Equal(item.Name, savedItem.Name);
        //Assert.Equal(item.Barcode, savedItem.Barcode);
    }

    [Fact]
    public async Task GetById_ReturnsCorrectItem()
    {
        // Arrange
        var itemId = Guid.NewGuid();
        var item = new Item
        {
            Id = itemId,
            Name = "TestItem",
            CategoryId = Guid.NewGuid(),
            //Barcode = "123456789"
        };
        await _context.Items.AddAsync(item);
        await _context.SaveChangesAsync();
        var repository = new ItemRepository(_context, new ItemNumRepository(_context), new ItemUnitRepository(_context));

        // Act
        var result = await repository.GetByIdAsync(itemId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(item.Name, result.Name);
        //Assert.Equal(item.Barcode, result.Barcode);
    }

    [Fact]
    public async Task GetAll_ReturnsAllItems()
    {
        // Arrange
        var items = new List<Item>
        {
            new Item { Id = Guid.NewGuid(), Name = "Item1", CategoryId = Guid.NewGuid() },
            new Item { Id = Guid.NewGuid(), Name = "Item2", CategoryId = Guid.NewGuid() }
        };
        await _context.Items.AddRangeAsync(items);
        await _context.SaveChangesAsync();
        var repository = new ItemRepository(_context, new ItemNumRepository(_context), new ItemUnitRepository(_context));

        // Act
        var result = await repository.GetAll();

        // Assert
        Assert.Equal(items.Count, result.Count());
    }

    [Fact]
    public async Task Update_UpdatesItem()
    {
        // Arrange
        var itemId = Guid.NewGuid();
        var item = new Item
        {
            Id = itemId,
            Name = "OriginalName",
            CategoryId = Guid.NewGuid()
        };
        await _context.Items.AddAsync(item);
        await _context.SaveChangesAsync();

        var repository = new ItemRepository(_context, new ItemNumRepository(_context), new ItemUnitRepository(_context));
        item.Name = "UpdatedName";

        // Act
        repository.Update(item);

        // Assert
        var updatedItem = await _context.Items.FindAsync(itemId);
        Assert.Equal("UpdatedName", updatedItem.Name);
    }

    [Fact]
    public async Task Delete_RemovesItem()
    {
        // Arrange
        var itemId = Guid.NewGuid();
        var item = new Item
        {
            Id = itemId,
            Name = "ItemToDelete",
            CategoryId = Guid.NewGuid()
        };
        await _context.Items.AddAsync(item);
        await _context.SaveChangesAsync();
        var repository = new ItemRepository(_context, new ItemNumRepository(_context), new ItemUnitRepository(_context));

        // Act
        repository.Remove(item);
        await _context.SaveChangesAsync();
        // Assert
        var deletedItem = await _context.Items.FindAsync(itemId);
        Assert.Null(deletedItem);
    }

    public void Dispose()
    {
        _context.Database.EnsureDeleted();
        _context.Dispose();
    }
}
