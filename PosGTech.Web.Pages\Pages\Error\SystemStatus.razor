@page "/system-status"
@using PosGTech.Web.Services.Interfaces
@using MudBlazor
@using Microsoft.AspNetCore.Components
@inject IErrorLoggingService ErrorLoggingService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>حالة النظام - PosGTech</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudPaper Elevation="3" Class="pa-6">
        <div dir="rtl">
            <!-- Header -->
            <div class="text-center mb-6">
                <MudText Typo="Typo.h4" Color="Color.Primary" Class="mb-2">
                    <MudIcon Icon="@GetSystemStatusIcon()" Color="@GetSystemStatusColor()" Class="me-2" />
                    حالة النظام
                </MudText>
                <MudText Typo="Typo.body1" Color="Color.Secondary">
                    آخر تحديث: @DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                </MudText>
            </div>

            <!-- System Status Card -->
            <MudCard Elevation="2" Class="mb-4">
                <MudCardContent Class="text-center pa-6">
                    <MudIcon Icon="@GetSystemStatusIcon()" 
                            Color="@GetSystemStatusColor()" 
                            Size="Size.Large" 
                            Style="font-size: 4rem;" 
                            Class="mb-4" />
                    
                    <MudText Typo="Typo.h5" Color="@GetSystemStatusColor()" Class="mb-2">
                        @GetSystemStatusText()
                    </MudText>
                    
                    <MudText Typo="Typo.body1" Color="Color.Secondary">
                        @GetSystemStatusDescription()
                    </MudText>
                </MudCardContent>
            </MudCard>

            <!-- Quick Stats -->
            <MudGrid Class="mb-4">
                <MudItem xs="12" sm="6" md="3">
                    <MudCard Elevation="1">
                        <MudCardContent Class="text-center">
                            <MudIcon Icon="Icons.Material.Filled.Error" 
                                    Color="Color.Error" 
                                    Size="Size.Large" 
                                    Class="mb-2" />
                            <MudText Typo="Typo.h6" Color="Color.Error">@ErrorCount</MudText>
                            <MudText Typo="Typo.body2">أخطاء اليوم</MudText>
                        </MudCardContent>
                    </MudCard>
                </MudItem>
                
                <MudItem xs="12" sm="6" md="3">
                    <MudCard Elevation="1">
                        <MudCardContent Class="text-center">
                            <MudIcon Icon="Icons.Material.Filled.CheckCircle" 
                                    Color="Color.Success" 
                                    Size="Size.Large" 
                                    Class="mb-2" />
                            <MudText Typo="Typo.h6" Color="Color.Success">@UptimeHours</MudText>
                            <MudText Typo="Typo.body2">ساعات التشغيل</MudText>
                        </MudCardContent>
                    </MudCard>
                </MudItem>
                
                <MudItem xs="12" sm="6" md="3">
                    <MudCard Elevation="1">
                        <MudCardContent Class="text-center">
                            <MudIcon Icon="Icons.Material.Filled.Speed" 
                                    Color="Color.Info" 
                                    Size="Size.Large" 
                                    Class="mb-2" />
                            <MudText Typo="Typo.h6" Color="Color.Info">@PerformanceScore</MudText>
                            <MudText Typo="Typo.body2">نقاط الأداء</MudText>
                        </MudCardContent>
                    </MudCard>
                </MudItem>
                
                <MudItem xs="12" sm="6" md="3">
                    <MudCard Elevation="1">
                        <MudCardContent Class="text-center">
                            <MudIcon Icon="Icons.Material.Filled.Security" 
                                    Color="Color.Primary" 
                                    Size="Size.Large" 
                                    Class="mb-2" />
                            <MudText Typo="Typo.h6" Color="Color.Primary">آمن</MudText>
                            <MudText Typo="Typo.body2">حالة الأمان</MudText>
                        </MudCardContent>
                    </MudCard>
                </MudItem>
            </MudGrid>

            <!-- Last Error Info -->
            @if (LastError != null)
            {
                <MudCard Elevation="2" Class="mb-4">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="Icons.Material.Filled.Warning" Color="Color.Warning" Class="me-2" />
                                آخر خطأ مسجل
                            </MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <MudGrid>
                            <MudItem xs="12" md="6">
                                <MudText Typo="Typo.body2" Class="mb-1">
                                    <strong>النوع:</strong> @LastError.ExceptionType
                                </MudText>
                                <MudText Typo="Typo.body2" Class="mb-1">
                                    <strong>الوقت:</strong> @LastError.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")
                                </MudText>
                            </MudItem>
                            <MudItem xs="12" md="6">
                                <MudText Typo="Typo.body2" Class="mb-1">
                                    <strong>السياق:</strong> @LastError.Context
                                </MudText>
                                <MudText Typo="Typo.body2">
                                    <strong>الرسالة:</strong> @(LastError.Message.Length > 100 ? LastError.Message.Substring(0, 100) + "..." : LastError.Message)
                                </MudText>
                            </MudItem>
                        </MudGrid>
                    </MudCardContent>
                </MudCard>
            }

            <!-- Action Buttons -->
            <div class="d-flex justify-center flex-wrap gap-3">
                <MudButton Variant="Variant.Filled" 
                          Color="Color.Primary" 
                          StartIcon="Icons.Material.Filled.BugReport"
                          OnClick="NavigateToErrorDiagnostics">
                    تشخيص الأخطاء
                </MudButton>
                
                <MudButton Variant="Variant.Outlined" 
                          Color="Color.Info" 
                          StartIcon="Icons.Material.Filled.Timeline"
                          OnClick="NavigateToSystemActivity">
                    نشاط النظام
                </MudButton>
                
                <MudButton Variant="Variant.Text" 
                          Color="Color.Secondary" 
                          StartIcon="Icons.Material.Filled.Refresh"
                          OnClick="RefreshStatus">
                    تحديث الحالة
                </MudButton>
            </div>
        </div>
    </MudPaper>
</MudContainer>

@code {
    private int ErrorCount = 0;
    private int UptimeHours = 0;
    private int PerformanceScore = 0;
    private ErrorInfo? LastError;
    private SystemHealthStatus SystemHealth = SystemHealthStatus.Good;

    public enum SystemHealthStatus
    {
        Excellent,
        Good,
        Warning,
        Critical
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadSystemStatus();
    }

    private async Task LoadSystemStatus()
    {
        try
        {
            // الحصول على آخر الأخطاء
            var recentErrors = await ErrorLoggingService.GetRecentErrorsAsync(10);
            
            // حساب أخطاء اليوم
            var today = DateTime.Today;
            ErrorCount = recentErrors.Count(e => e.Timestamp.Date == today);
            
            // آخر خطأ
            LastError = recentErrors.FirstOrDefault();
            
            // محاكاة بيانات أخرى
            UptimeHours = (int)(DateTime.Now - DateTime.Today).TotalHours;
            PerformanceScore = CalculatePerformanceScore();
            
            // تحديد حالة النظام
            SystemHealth = DetermineSystemHealth();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"فشل في تحميل حالة النظام: {ex.Message}", Severity.Error);
        }
    }

    private int CalculatePerformanceScore()
    {
        // محاكاة حساب نقاط الأداء بناءً على عدد الأخطاء
        if (ErrorCount == 0) return 100;
        if (ErrorCount <= 2) return 85;
        if (ErrorCount <= 5) return 70;
        if (ErrorCount <= 10) return 50;
        return 30;
    }

    private SystemHealthStatus DetermineSystemHealth()
    {
        if (ErrorCount == 0) return SystemHealthStatus.Excellent;
        if (ErrorCount <= 2) return SystemHealthStatus.Good;
        if (ErrorCount <= 5) return SystemHealthStatus.Warning;
        return SystemHealthStatus.Critical;
    }

    private string GetSystemStatusIcon()
    {
        return SystemHealth switch
        {
            SystemHealthStatus.Excellent => Icons.Material.Filled.CheckCircle,
            SystemHealthStatus.Good => Icons.Material.Filled.CheckCircleOutline,
            SystemHealthStatus.Warning => Icons.Material.Filled.Warning,
            SystemHealthStatus.Critical => Icons.Material.Filled.Error,
            _ => Icons.Material.Filled.Help
        };
    }

    private Color GetSystemStatusColor()
    {
        return SystemHealth switch
        {
            SystemHealthStatus.Excellent => Color.Success,
            SystemHealthStatus.Good => Color.Primary,
            SystemHealthStatus.Warning => Color.Warning,
            SystemHealthStatus.Critical => Color.Error,
            _ => Color.Default
        };
    }

    private string GetSystemStatusText()
    {
        return SystemHealth switch
        {
            SystemHealthStatus.Excellent => "ممتاز",
            SystemHealthStatus.Good => "جيد",
            SystemHealthStatus.Warning => "تحذير",
            SystemHealthStatus.Critical => "حرج",
            _ => "غير محدد"
        };
    }

    private string GetSystemStatusDescription()
    {
        return SystemHealth switch
        {
            SystemHealthStatus.Excellent => "النظام يعمل بشكل مثالي بدون أي أخطاء",
            SystemHealthStatus.Good => "النظام يعمل بشكل جيد مع أخطاء قليلة",
            SystemHealthStatus.Warning => "النظام يعمل ولكن يحتاج إلى مراجعة",
            SystemHealthStatus.Critical => "النظام يواجه مشاكل تحتاج إلى تدخل فوري",
            _ => "حالة النظام غير محددة"
        };
    }

    private void NavigateToErrorDiagnostics()
    {
        Navigation.NavigateTo("/error-diagnostics");
    }

    private void NavigateToSystemActivity()
    {
        Navigation.NavigateTo("/system-activity");
    }

    private async Task RefreshStatus()
    {
        await LoadSystemStatus();
        StateHasChanged();
        Snackbar.Add("تم تحديث حالة النظام", Severity.Success);
    }
}
