# تقرير شامل عن نظام PosGTech.API
## الأصناف والمشتريات والمبيعات

---

## نظرة عامة على النظام

**PosGTech.API** هو نظام إدارة نقاط البيع متكامل مبني بتقنية .NET 8.0، يوفر حلولاً شاملة لإدارة:
- الأصناف والمخزون
- المشتريات والموردين  
- المبيعات والعملاء
- التقارير والإحصائيات
- الإدارة المالية والخزائن

---

## 1. نظام إدارة الأصناف (Items Management)

### 1.1 البنية الأساسية للأصناف

#### نموذج الصنف الرئيسي (Item Model)
```csharp
public class Item : BaseEntity
{
    [Required] public string Name { get; set; }           // اسم الصنف
    public bool IsHaveExp { get; set; }                   // هل له تاريخ انتهاء
    [Required] public Guid CategoryId { get; set; }       // معرف التصنيف
    public Category? Category { get; set; }               // التصنيف
    public decimal CostPrice { get; set; }                // سعر التكلفة
    
    // العلاقات
    public ICollection<ItemUnit>? ItemUnits { get; set; }     // وحدات القياس
    public ICollection<ItemNum>? ItemNums { get; set; }       // الأرقام التسلسلية/الباركود
    public ICollection<StoreItem>? StoreItems { get; set; }   // المخزون في المخازن
}
```

#### وحدات القياس (ItemUnit)
```csharp
public class ItemUnit : BaseEntity
{
    public Guid UnitId { get; set; }           // معرف الوحدة
    public Guid ItemId { get; set; }           // معرف الصنف
    public decimal Quantity { get; set; }      // الكمية (معامل التحويل)
    public decimal SalePrice { get; set; }     // سعر البيع
    public bool IsBigger { get; set; }         // أكبر من الوحدة الأساسية
    public bool IsBasicUnit { get; set; }      // الوحدة الأساسية
}
```

### 1.2 واجهات برمجة التطبيقات للأصناف

#### العمليات الأساسية
- **GET** `/api/Items/getAllItems` - جلب جميع الأصناف
- **GET** `/api/Items/getAllItemsForPurchase` - أصناف للمشتريات
- **GET** `/api/Items/getItemById/{id}` - جلب صنف بالمعرف
- **POST** `/api/Items/insertItem` - إضافة صنف جديد
- **PUT** `/api/Items/updateItem/{id}` - تحديث صنف
- **DELETE** `/api/Items/deleteItem/{id}` - حذف صنف

#### مثال على إضافة صنف جديد
```json
{
  "name": "لابتوب ديل",
  "isHaveExp": false,
  "categoryId": "550e8400-e29b-41d4-a716-446655440000",
  "costPrice": 15000.00,
  "itemNums": [
    { "barcode": "1234567890123" }
  ],
  "itemUnits": [
    {
      "unitId": "550e8400-e29b-41d4-a716-446655440001",
      "quantity": 1,
      "salePrice": 18000.00,
      "isBigger": false,
      "isBasicUnit": true
    }
  ]
}
```

### 1.3 إدارة المخزون

#### نموذج المخزون (StoreItem)
- **StoreItem**: ربط الأصناف بالمخازن
- **StoreItemExp**: تتبع تواريخ الانتهاء والكميات
- **Inventory**: عمليات الجرد والتسوية

---

## 2. نظام المشتريات (Purchases Management)

### 2.1 البنية الأساسية للمشتريات

#### نموذج المشتريات (Purchase Model)
```csharp
public class Purchase : BaseEntity
{
    public int InvoiceNo { get; set; }              // رقم الفاتورة
    public DateOnly Date { get; set; }              // تاريخ الفاتورة
    public decimal Total { get; set; }              // الإجمالي
    public decimal DiscountValue { get; set; }      // قيمة الخصم
    public bool IsDiscountValue { get; set; }       // نوع الخصم (قيمة/نسبة)
    public decimal FinalTotal { get; set; }         // الإجمالي النهائي
    public string? Note { get; set; }               // ملاحظات
    public Guid? ClientId { get; set; }             // معرف المورد
    public Guid StoreId { get; set; }               // معرف المخزن
    
    // العلاقات
    public Client? Client { get; set; }                           // المورد
    public Store? Store { get; set; }                             // المخزن
    public ICollection<PurchaseItem>? PurchaseItems { get; set; } // أصناف الفاتورة
    public ICollection<Receipt>? Receipts { get; set; }           // الإيصالات
}
```

#### عناصر المشتريات (PurchaseItem)
```csharp
public class PurchaseItem : BaseEntity
{
    public decimal Quantity { get; set; }              // الكمية
    public decimal Price { get; set; }                 // السعر
    public decimal PriceAfterDiscount { get; set; }    // السعر بعد الخصم
    public DateOnly? Exp { get; set; }                 // تاريخ الانتهاء
    public Guid? ItemId { get; set; }                  // معرف الصنف
    public Guid ItemUnitId { get; set; }               // معرف وحدة القياس
    public decimal ReturnQuantity { get; set; }        // الكمية المرتجعة
}
```

### 2.2 واجهات برمجة التطبيقات للمشتريات

#### العمليات الأساسية
- **GET** `/api/Purchases/getAllPurchases` - جلب جميع المشتريات
- **GET** `/api/Purchases/getPurchaseById/{id}` - جلب مشتريات بالمعرف
- **GET** `/api/Purchases/getPurchaseByNum/{billNum}` - جلب بالرقم
- **POST** `/api/Purchases/insertPurchase` - إضافة فاتورة مشتريات
- **PUT** `/api/Purchases/updatePurchase/{id}` - تحديث فاتورة
- **DELETE** `/api/Purchases/deletePurchase/{id}` - حذف فاتورة

### 2.3 مرتجعات المشتريات (Return Purchases)

#### نموذج المرتجعات
```csharp
public class ReturnPurchase : BaseEntity
{
    public int InvoiceNo { get; set; }
    public DateOnly Date { get; set; }
    public decimal Total { get; set; }
    public decimal FinalTotal { get; set; }
    public string? Note { get; set; }
    public Guid? ClientId { get; set; }
    
    public ICollection<ReturnPurchaseItem>? ReturnPurchaseItems { get; set; }
}
```

---

## 3. نظام المبيعات (Sales Management)

### 3.1 البنية الأساسية للمبيعات

#### نموذج المبيعات (Sell Model)
```csharp
public class Sell : BaseEntity
{
    public int InvoiceNo { get; set; }              // رقم الفاتورة
    public DateOnly Date { get; set; }              // تاريخ الفاتورة
    public decimal Total { get; set; }              // الإجمالي
    public decimal DiscountValue { get; set; }      // قيمة الخصم
    public bool IsDiscountValue { get; set; }       // نوع الخصم
    public decimal FinalTotal { get; set; }         // الإجمالي النهائي
    public Guid? ClientId { get; set; }             // معرف العميل
    public Guid StoreId { get; set; }               // معرف المخزن
    
    // العلاقات
    public Client? Client { get; set; }                     // العميل
    public Store? Store { get; set; }                       // المخزن
    public ICollection<SellItem>? SellItems { get; set; }   // أصناف الفاتورة
    public ICollection<Receipt>? Receipts { get; set; }     // الإيصالات
}
```

#### عناصر المبيعات (SellItem)
```csharp
public class SellItem : BaseEntity
{
    public decimal Quantity { get; set; }                    // الكمية
    public decimal CostPrice { get; set; }                   // سعر التكلفة
    public decimal SalePrice { get; set; }                   // سعر البيع
    public decimal SalePriceAfterDiscount { get; set; }      // السعر بعد الخصم
    public Guid StoreItemExpId { get; set; }                 // معرف المخزون
    public Guid ItemUnitId { get; set; }                     // معرف وحدة القياس
    public decimal ReturnQuantity { get; set; }              // الكمية المرتجعة
}
```

### 3.2 واجهات برمجة التطبيقات للمبيعات

#### العمليات الأساسية
- **GET** `/api/Sells/getSellById/{id}` - جلب مبيعات بالمعرف
- **GET** `/api/Sells/getSellByNum/{billNum}` - جلب بالرقم
- **GET** `/api/Sells/getAllSellsNum` - جلب أرقام الفواتير
- **POST** `/api/Sells/insertSell` - إضافة فاتورة مبيعات
- **PUT** `/api/Sells/updateSell/{id}` - تحديث فاتورة
- **DELETE** `/api/Sells/deleteSell/{id}` - حذف فاتورة

### 3.3 مرتجعات المبيعات (Return Sales)

#### نموذج المرتجعات
```csharp
public class ReturnSell : BaseEntity
{
    public short InvoiceNo { get; set; }
    public DateOnly Date { get; set; }
    public decimal Total { get; set; }
    public decimal FinalTotal { get; set; }
    public Guid? ClientId { get; set; }
    
    public ICollection<ReturnSellItem>? ReturnSellItems { get; set; }
}
```

---

## 4. نظام التقارير والإحصائيات

### 4.1 تقارير المشتريات والمبيعات

#### خدمة التقارير (ReportService)
```csharp
public class ReportService
{
    public byte[] CreateReport(DataSet DS, string ReportName, 
                              List<string> DataSourceNames, 
                              string files, 
                              ReportParameter[]? Params = null)
}
```

#### تقارير متاحة
- **تقرير المشتريات**: `/api/Report/ReportPurchase/{id}`
- **تقرير المبيعات**: `/api/Report/ReportSell/{id}`

### 4.2 نماذج بيانات التقارير

#### تقرير المشتريات
```csharp
public class PurchaseItemRVM
{
    public string Quantity { get; set; }
    public string Price { get; set; }
    public string PriceAfterDiscount { get; set; }
    public string Exp { get; set; }
    public string ItemName { get; set; }
    public string ItemUnit { get; set; }
    public string FinalTotal { get; set; }
}
```

#### تقرير المبيعات
```csharp
public class SellItemRVM
{
    public string Quantity { get; set; }
    public string SalePrice { get; set; }
    public string ItemName { get; set; }
    public string SalePriceAfterDiscount { get; set; }
    public string Total { get; set; }
    public string ItemUnit { get; set; }
}
```

---

## 5. الأنظمة المساعدة

### 5.1 نظام العملاء والموردين (Clients)

#### نموذج العميل
```csharp
public class Client : BaseEntity
{
    public string Name { get; set; }        // الاسم
    public int? Phone { get; set; }         // الهاتف
    public string? Address { get; set; }    // العنوان
    public decimal Balance { get; set; }    // الرصيد
    public bool IsSupplier { get; set; }    // مورد
    public bool IsCustomer { get; set; }    // عميل
}
```

### 5.2 نظام المخازن (Stores)

#### إدارة المخازن
- **Store**: المخازن الرئيسية
- **StoreItem**: ربط الأصناف بالمخازن
- **StoreItemExp**: إدارة تواريخ الانتهاء

### 5.3 نظام الخزائن (Treasury)

#### نموذج الخزينة
```csharp
public class Treasury : BaseEntity
{
    public string Name { get; set; }        // اسم الخزينة
    public decimal Tax { get; set; }        // الضريبة
}

public class UserTreasury : BaseEntity
{
    public decimal Balance { get; set; }    // الرصيد
    public Guid? TreasuryId { get; set; }   // معرف الخزينة
    public string? UserId { get; set; }     // معرف المستخدم
}
```

### 5.4 نظام الموظفين (Employees)

#### نموذج الموظف
```csharp
public class Employee : BaseEntity
{
    public string Name { get; set; }        // الاسم
    public int? Phone { get; set; }         // الهاتف
    public string? Job { get; set; }        // الوظيفة
    public decimal Salary { get; set; }     // الراتب
    public decimal Balance { get; set; }    // الرصيد
}
```

---

## 6. الميزات التقنية

### 6.1 الأمان والمصادقة
- **JWT Authentication**: مصادقة بالرموز المميزة
- **Authorization**: تخويل العمليات
- **Data Validation**: التحقق من صحة البيانات

### 6.2 إدارة قاعدة البيانات
- **Entity Framework Core**: ORM متقدم
- **Repository Pattern**: نمط المستودع
- **Unit of Work**: وحدة العمل
- **Migrations**: إدارة تطوير قاعدة البيانات

### 6.3 معالجة الأخطاء
- **Transaction Management**: إدارة المعاملات
- **Error Handling**: معالجة الأخطاء
- **Validation**: التحقق من البيانات

---

## 7. الخلاصة والتوصيات

### 7.1 نقاط القوة
✅ **بنية متكاملة**: نظام شامل لإدارة نقاط البيع  
✅ **مرونة عالية**: دعم وحدات قياس متعددة  
✅ **تتبع دقيق**: إدارة المخزون وتواريخ الانتهاء  
✅ **تقارير شاملة**: نظام تقارير متقدم  
✅ **أمان محكم**: مصادقة وتخويل متقدم  

### 7.2 التوصيات للتطوير
🔄 **إضافة Dashboard**: لوحة تحكم تفاعلية  
🔄 **تحسين الأداء**: تحسين استعلامات قاعدة البيانات  
🔄 **إضافة Caching**: تخزين مؤقت للبيانات  
🔄 **تطوير Mobile App**: تطبيق جوال  
🔄 **إضافة Notifications**: نظام إشعارات  

---

## 8. إحصائيات النظام والبنية التقنية

### 8.1 إحصائيات المشروع

#### عدد الملفات والمكونات
- **إجمالي Controllers**: 15+ وحدة تحكم
- **Models**: 25+ نموذج بيانات
- **DTOs**: 50+ نموذج نقل بيانات
- **Repositories**: 20+ مستودع بيانات
- **Services**: 5+ خدمة متخصصة

#### المشاريع الفرعية
1. **PosGTech.API** - واجهة برمجة التطبيقات الرئيسية
2. **PosGTech.Models** - نماذج البيانات الأساسية
3. **PosGTech.ModelsDTO** - نماذج نقل البيانات
4. **PosGTech.DataAccess** - طبقة الوصول للبيانات
5. **PosGTech.Reports** - نظام التقارير
6. **PosGTech.Web** - تطبيق الويب (Blazor)
7. **PosGTech.UI** - واجهة المستخدم (MAUI)
8. **PosGTech.Tests** - اختبارات النظام

### 8.2 التقنيات المستخدمة

#### Backend Technologies
- **.NET 8.0**: الإطار الأساسي
- **ASP.NET Core Web API**: واجهة برمجة التطبيقات
- **Entity Framework Core**: ORM لقاعدة البيانات
- **SQL Server**: قاعدة البيانات الرئيسية
- **AutoMapper**: تحويل البيانات
- **JWT Authentication**: المصادقة والأمان

#### Frontend Technologies
- **Blazor WebAssembly**: تطبيق الويب
- **MAUI**: تطبيق سطح المكتب والجوال
- **MudBlazor**: مكتبة واجهة المستخدم
- **Local Storage**: تخزين البيانات محلياً

#### Reporting & Documentation
- **Microsoft Reporting (RDLC)**: نظام التقارير
- **PDF Generation**: إنتاج تقارير PDF
- **Markdown Documentation**: التوثيق

### 8.3 بنية قاعدة البيانات

#### الجداول الرئيسية
```sql
-- جداول الأصناف والمخزون
Items, Categories, Units, ItemUnits, ItemNums
StoreItems, StoreItemExps, Stores

-- جداول المشتريات والمبيعات
Purchases, PurchaseItems, ReturnPurchases, ReturnPurchaseItems
Sells, SellItems, ReturnSells, ReturnSellItems

-- جداول العملاء والموظفين
Clients, Employees, Users

-- جداول المالية والخزائن
Treasuries, UserTreasuries, Receipts, Financials
Expenses, Salaries

-- جداول العمليات الأخرى
Inventories, InventoryItems, Transfers, TransferItems
Consumeds, ConsumedItems
```

#### العلاقات الرئيسية
- **One-to-Many**: Item → ItemUnits, Purchase → PurchaseItems
- **Many-to-Many**: Items ↔ Stores (via StoreItems)
- **Self-Referencing**: Categories, Financial hierarchies

### 8.4 أنماط التصميم المستخدمة

#### Repository Pattern
```csharp
public interface IRepository<T> where T : class
{
    Task<IEnumerable<T>> GetAll();
    Task<T> GetFirstOrDefault(Expression<Func<T, bool>> filter);
    Task Add(T entity);
    Task Update(T entity);
    Task Remove(T entity);
}
```

#### Unit of Work Pattern
```csharp
public interface IUnitOfWork : IDisposable
{
    IItemRepository Item { get; }
    IPurchaseRepository Purchase { get; }
    ISellRepository Sell { get; }
    // ... other repositories

    Task<ResponseVM> SaveWithTransaction();
}
```

#### DTO Pattern
- فصل نماذج البيانات عن نماذج العرض
- تحسين الأداء وتقليل البيانات المنقولة
- حماية البيانات الحساسة

### 8.5 الأمان والحماية

#### مستويات الأمان
1. **Authentication**: JWT Token validation
2. **Authorization**: Role-based access control
3. **Data Validation**: Input validation and sanitization
4. **SQL Injection Protection**: Parameterized queries
5. **CORS Configuration**: Cross-origin resource sharing

#### معالجة الأخطاء
```csharp
public class ResponseVM
{
    public bool State { get; set; }
    public string Message { get; set; }
}
```

### 8.6 إدارة المعاملات (Transactions)

#### Transaction Management
- **Database Transactions**: ضمان تكامل البيانات
- **Rollback Support**: التراجع عند الأخطاء
- **Concurrent Access**: إدارة الوصول المتزامن

#### مثال على معاملة مشتريات
```csharp
public async Task<Guid> AddPurchase(Purchase purchase, decimal paid = 0)
{
    // 1. تحديث المخزون
    await _storeItemRepository.AddQuantity(items, purchase.StoreId);

    // 2. تحديث رصيد المورد
    await _clientRepository.UpdateClientBalanceInc(purchase.ClientId, purchase.FinalTotal);

    // 3. إضافة إيصال الدفع (إن وجد)
    if (paid > 0)
        await _receiptRepository.AddReceipt(receipt);

    return purchase.Id;
}
```

---

## 9. دليل الاستخدام السريع

### 9.1 إعداد النظام

#### متطلبات التشغيل
- **.NET 8.0 Runtime**
- **SQL Server 2019+**
- **IIS 10.0+** (للنشر)

#### خطوات التثبيت
1. استنساخ المشروع من المستودع
2. تحديث connection string في appsettings.json
3. تشغيل migrations لإنشاء قاعدة البيانات
4. تشغيل المشروع

### 9.2 العمليات الأساسية

#### إضافة صنف جديد
```http
POST /api/Items/insertItem
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "منتج جديد",
  "categoryId": "guid-here",
  "costPrice": 100.00,
  "itemUnits": [...]
}
```

#### إنشاء فاتورة مشتريات
```http
POST /api/Purchases/insertPurchase
Content-Type: application/json
Authorization: Bearer {token}

{
  "date": "2025-01-01",
  "client": {...},
  "storeId": "guid-here",
  "purchaseItemDTOs": [...]
}
```

#### إنشاء فاتورة مبيعات
```http
POST /api/Sells/insertSell
Content-Type: application/json
Authorization: Bearer {token}

{
  "date": "2025-01-01",
  "client": {...},
  "storeId": "guid-here",
  "sellItemDTOs": [...]
}
```

### 9.3 التقارير والاستعلامات

#### تقرير المبيعات
```http
GET /api/Report/ReportSell/{sellId}
Authorization: Bearer {token}
Accept: application/pdf
```

#### تقرير المشتريات
```http
GET /api/Report/ReportPurchase/{purchaseId}
Authorization: Bearer {token}
Accept: application/pdf
```

---

## 10. الصيانة والتطوير المستقبلي

### 10.1 خطة الصيانة

#### الصيانة الدورية
- **نسخ احتياطية يومية** لقاعدة البيانات
- **مراقبة الأداء** والاستعلامات البطيئة
- **تحديث الأمان** والحماية
- **تنظيف البيانات** القديمة

#### مراقبة النظام
- **Application Insights**: مراقبة الأداء
- **Health Checks**: فحص صحة النظام
- **Logging**: تسجيل العمليات والأخطاء

### 10.2 التطوير المستقبلي

#### الميزات المقترحة
1. **Dashboard تفاعلي** مع إحصائيات مباشرة
2. **تطبيق جوال** للمبيعات الميدانية
3. **تكامل مع أنظمة الدفع** الإلكترونية
4. **نظام إشعارات** للمخزون المنخفض
5. **تحليلات متقدمة** للمبيعات والأرباح
6. **API للتكامل** مع أنظمة خارجية
7. **نظام النسخ الاحتياطي** التلقائي
8. **دعم متعدد اللغات** والعملات

#### التحسينات التقنية
- **Microservices Architecture**: تقسيم النظام لخدمات صغيرة
- **Redis Caching**: تحسين الأداء
- **Docker Containerization**: سهولة النشر
- **CI/CD Pipeline**: أتمتة النشر
- **Load Balancing**: توزيع الأحمال

---

## 11. الخلاصة النهائية

### 11.1 تقييم شامل للنظام

#### نقاط القوة الرئيسية
✅ **بنية تقنية متقدمة**: استخدام أحدث تقنيات .NET
✅ **تصميم قابل للتوسع**: أنماط تصميم محترفة
✅ **أمان محكم**: مصادقة وتخويل متقدم
✅ **إدارة شاملة**: تغطية جميع جوانب نقاط البيع
✅ **تقارير احترافية**: نظام تقارير متكامل
✅ **واجهات متعددة**: Web, Desktop, Mobile

#### المجالات القابلة للتحسين
🔄 **الأداء**: تحسين استعلامات قاعدة البيانات
🔄 **المراقبة**: إضافة نظام مراقبة شامل
🔄 **التوثيق**: توسيع التوثيق التقني
🔄 **الاختبارات**: زيادة تغطية الاختبارات
🔄 **التكامل**: ربط مع أنظمة خارجية

### 11.2 التوصيات الاستراتيجية

#### للمطورين
- **Code Review**: مراجعة دورية للكود
- **Performance Testing**: اختبارات الأداء
- **Security Audits**: مراجعات أمنية
- **Documentation**: تحديث التوثيق

#### للإدارة
- **Training Programs**: برامج تدريب المستخدمين
- **Backup Strategy**: استراتيجية النسخ الاحتياطي
- **Disaster Recovery**: خطة استرداد الكوارث
- **Scalability Planning**: تخطيط التوسع

---

**تاريخ التقرير**: 2025-07-01
**إصدار النظام**: .NET 8.0
**حالة النظام**: نشط ومستقر
**مستوى النضج**: Production Ready
**التقييم العام**: ⭐⭐⭐⭐⭐ (ممتاز)
