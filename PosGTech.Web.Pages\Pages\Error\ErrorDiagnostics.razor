@page "/error-diagnostics"
@using PosGTech.Web.Services.Interfaces
@using MudBlazor
@using Microsoft.AspNetCore.Components
@inject IErrorLoggingService ErrorLoggingService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>تشخيص الأخطاء - PosGTech</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <MudPaper Elevation="3" Class="pa-6">
        <div dir="rtl">
            <!-- Header -->
            <div class="d-flex align-center justify-space-between mb-6">
                <div>
                    <MudText Typo="Typo.h4" Color="Color.Primary" Class="mb-2">
                        <MudIcon Icon="Icons.Material.Filled.BugReport" Class="me-2" />
                        تشخيص الأخطاء
                    </MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        عرض وتحليل آخر الأخطاء المسجلة في النظام
                    </MudText>
                </div>
                <div class="d-flex gap-2">
                    <MudButton Variant="Variant.Filled"
                              Color="Color.Primary"
                              StartIcon="Icons.Material.Filled.Refresh"
                              OnClick="LoadRecentErrors"
                              Disabled="IsLoading">
                        تحديث
                    </MudButton>

                    <MudButton Variant="Variant.Outlined"
                              Color="Color.Info"
                              StartIcon="Icons.Material.Filled.Timeline"
                              OnClick="NavigateToSystemActivity">
                        نشاط النظام
                    </MudButton>
                </div>
            </div>

            <!-- Loading -->
            @if (IsLoading)
            {
                <div class="text-center py-8">
                    <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    <MudText Typo="Typo.body1" Class="mt-4">جاري تحميل بيانات الأخطاء...</MudText>
                </div>
            }
            else
            {
                <!-- Statistics Cards -->
                <MudGrid Class="mb-6">
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard Elevation="2">
                            <MudCardContent Class="text-center">
                                <MudIcon Icon="Icons.Material.Filled.Error" 
                                        Color="Color.Error" 
                                        Size="Size.Large" 
                                        Class="mb-2" />
                                <MudText Typo="Typo.h5" Color="Color.Error">@RecentErrors.Count</MudText>
                                <MudText Typo="Typo.body2">إجمالي الأخطاء</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard Elevation="2">
                            <MudCardContent Class="text-center">
                                <MudIcon Icon="Icons.Material.Filled.Warning" 
                                        Color="Color.Warning" 
                                        Size="Size.Large" 
                                        Class="mb-2" />
                                <MudText Typo="Typo.h5" Color="Color.Warning">@CriticalErrorsCount</MudText>
                                <MudText Typo="Typo.body2">أخطاء حرجة</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard Elevation="2">
                            <MudCardContent Class="text-center">
                                <MudIcon Icon="Icons.Material.Filled.Schedule" 
                                        Color="Color.Info" 
                                        Size="Size.Large" 
                                        Class="mb-2" />
                                <MudText Typo="Typo.h5" Color="Color.Info">@LastErrorTime</MudText>
                                <MudText Typo="Typo.body2">آخر خطأ</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard Elevation="2">
                            <MudCardContent Class="text-center">
                                <MudIcon Icon="Icons.Material.Filled.TrendingUp" 
                                        Color="Color.Success" 
                                        Size="Size.Large" 
                                        Class="mb-2" />
                                <MudText Typo="Typo.h5" Color="Color.Success">@MostCommonErrorType</MudText>
                                <MudText Typo="Typo.body2">النوع الأكثر شيوعاً</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>

                <!-- Recent Errors Table -->
                @if (RecentErrors.Any())
                {
                    <MudCard Elevation="2">
                        <MudCardHeader>
                            <CardHeaderContent>
                                <MudText Typo="Typo.h6">
                                    <MudIcon Icon="Icons.Material.Filled.List" Class="me-2" />
                                    آخر الأخطاء المسجلة
                                </MudText>
                            </CardHeaderContent>
                            <CardHeaderActions>
                                <MudButton Variant="Variant.Text" 
                                          Color="Color.Error" 
                                          StartIcon="Icons.Material.Filled.Delete"
                                          OnClick="ClearErrorLog">
                                    مسح السجل
                                </MudButton>
                            </CardHeaderActions>
                        </MudCardHeader>
                        <MudCardContent>
                            <MudTable Items="@RecentErrors" 
                                     Hover="true" 
                                     Striped="true" 
                                     Dense="true"
                                     FixedHeader="true"
                                     Height="400px">
                                <HeaderContent>
                                    <MudTh>الوقت</MudTh>
                                    <MudTh>نوع الخطأ</MudTh>
                                    <MudTh>الرسالة</MudTh>
                                    <MudTh>السياق</MudTh>
                                    <MudTh>الشدة</MudTh>
                                    <MudTh>الإجراءات</MudTh>
                                </HeaderContent>
                                <RowTemplate>
                                    <MudTd DataLabel="الوقت">
                                        <MudText Typo="Typo.body2">
                                            @context.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")
                                        </MudText>
                                    </MudTd>
                                    <MudTd DataLabel="نوع الخطأ">
                                        <MudChip T="string"
                                                Color="GetErrorTypeColor(context.ExceptionType)"
                                                Size="Size.Small">
                                            @context.ExceptionType
                                        </MudChip>
                                    </MudTd>
                                    <MudTd DataLabel="الرسالة">
                                        <MudText Typo="Typo.body2" Style="max-width: 300px; overflow: hidden; text-overflow: ellipsis;">
                                            @context.Message
                                        </MudText>
                                    </MudTd>
                                    <MudTd DataLabel="السياق">
                                        <MudText Typo="Typo.body2">@context.Context</MudText>
                                    </MudTd>
                                    <MudTd DataLabel="الشدة">
                                        <MudChip T="string"
                                                Color="GetSeverityColor(context.Severity)"
                                                Size="Size.Small">
                                            @GetSeverityText(context.Severity)
                                        </MudChip>
                                    </MudTd>
                                    <MudTd DataLabel="الإجراءات">
                                        <MudButton Variant="Variant.Text" 
                                                  Color="Color.Primary" 
                                                  Size="Size.Small"
                                                  StartIcon="Icons.Material.Filled.Visibility"
                                                  OnClick="() => ShowErrorDetails(context)">
                                            التفاصيل
                                        </MudButton>
                                    </MudTd>
                                </RowTemplate>
                            </MudTable>
                        </MudCardContent>
                    </MudCard>
                }
                else
                {
                    <MudAlert Severity="Severity.Success" Class="text-center">
                        <MudIcon Icon="Icons.Material.Filled.CheckCircle" Class="me-2" />
                        لا توجد أخطاء مسجلة حديثاً. النظام يعمل بشكل طبيعي!
                    </MudAlert>
                }
            }
        </div>
    </MudPaper>
</MudContainer>

<!-- Error Details Dialog -->
<MudDialog @bind-IsVisible="ShowDetailsDialog" Options="DialogOptions">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="Icons.Material.Filled.Info" Class="me-2" />
            تفاصيل الخطأ
        </MudText>
    </TitleContent>
    <DialogContent>
        @if (SelectedError != null)
        {
            <div dir="rtl">
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudTextField Label="نوع الخطأ" 
                                     Value="@SelectedError.ExceptionType" 
                                     ReadOnly="true" 
                                     Variant="Variant.Outlined" />
                    </MudItem>
                    <MudItem xs="12" md="6">
                        <MudTextField Label="الوقت" 
                                     Value="@SelectedError.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")" 
                                     ReadOnly="true" 
                                     Variant="Variant.Outlined" />
                    </MudItem>
                    <MudItem xs="12">
                        <MudTextField Label="رسالة الخطأ" 
                                     Value="@SelectedError.Message" 
                                     ReadOnly="true" 
                                     Variant="Variant.Outlined" 
                                     Lines="3" />
                    </MudItem>
                    <MudItem xs="12">
                        <MudTextField Label="السياق" 
                                     Value="@SelectedError.Context" 
                                     ReadOnly="true" 
                                     Variant="Variant.Outlined" />
                    </MudItem>
                    @if (!string.IsNullOrEmpty(SelectedError.StackTrace))
                    {
                        <MudItem xs="12">
                            <MudTextField Label="تتبع المكدس" 
                                         Value="@SelectedError.StackTrace" 
                                         ReadOnly="true" 
                                         Variant="Variant.Outlined" 
                                         Lines="8" />
                        </MudItem>
                    }
                    @if (!string.IsNullOrEmpty(SelectedError.InnerException))
                    {
                        <MudItem xs="12">
                            <MudTextField Label="الخطأ الداخلي" 
                                         Value="@SelectedError.InnerException" 
                                         ReadOnly="true" 
                                         Variant="Variant.Outlined" 
                                         Lines="3" />
                        </MudItem>
                    }
                </MudGrid>
            </div>
        }
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="CloseDetailsDialog">إغلاق</MudButton>
    </DialogActions>
</MudDialog>

@code {
    private List<ErrorInfo> RecentErrors = new();
    private bool IsLoading = true;
    private bool ShowDetailsDialog = false;
    private ErrorInfo? SelectedError;
    
    private int CriticalErrorsCount => RecentErrors.Count(e => e.Severity == ErrorSeverity.Critical);
    private string LastErrorTime => RecentErrors.Any() ? 
        GetRelativeTime(RecentErrors.First().Timestamp) : "لا توجد أخطاء";
    private string MostCommonErrorType => RecentErrors.Any() ? 
        RecentErrors.GroupBy(e => e.ExceptionType)
                   .OrderByDescending(g => g.Count())
                   .First().Key : "لا توجد أخطاء";

    private DialogOptions DialogOptions = new()
    {
        MaxWidth = MaxWidth.Large,
        FullWidth = true,
        CloseButton = true
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadRecentErrors();
    }

    private async Task LoadRecentErrors()
    {
        IsLoading = true;
        StateHasChanged();
        
        try
        {
            RecentErrors = await ErrorLoggingService.GetRecentErrorsAsync(50);
            RecentErrors = RecentErrors.OrderByDescending(e => e.Timestamp).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"فشل في تحميل الأخطاء: {ex.Message}", Severity.Error);
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private async Task ClearErrorLog()
    {
        try
        {
            await ErrorLoggingService.ClearOldLogsAsync(0);
            await LoadRecentErrors();
            Snackbar.Add("تم مسح سجل الأخطاء بنجاح", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"فشل في مسح السجل: {ex.Message}", Severity.Error);
        }
    }

    private void ShowErrorDetails(ErrorInfo error)
    {
        SelectedError = error;
        ShowDetailsDialog = true;
    }

    private void CloseDetailsDialog()
    {
        ShowDetailsDialog = false;
        SelectedError = null;
    }

    private Color GetErrorTypeColor(string errorType)
    {
        return errorType switch
        {
            "NullReferenceException" => Color.Error,
            "ArgumentNullException" => Color.Error,
            "InvalidOperationException" => Color.Warning,
            "NotSupportedException" => Color.Info,
            "TimeoutException" => Color.Warning,
            _ => Color.Default
        };
    }

    private Color GetSeverityColor(ErrorSeverity severity)
    {
        return severity switch
        {
            ErrorSeverity.Critical => Color.Error,
            ErrorSeverity.High => Color.Warning,
            ErrorSeverity.Medium => Color.Info,
            ErrorSeverity.Low => Color.Success,
            _ => Color.Default
        };
    }

    private string GetSeverityText(ErrorSeverity severity)
    {
        return severity switch
        {
            ErrorSeverity.Critical => "حرج",
            ErrorSeverity.High => "عالي",
            ErrorSeverity.Medium => "متوسط",
            ErrorSeverity.Low => "منخفض",
            _ => "غير محدد"
        };
    }

    private string GetRelativeTime(DateTime dateTime)
    {
        var timeSpan = DateTime.UtcNow - dateTime;

        if (timeSpan.TotalMinutes < 1)
            return "الآن";
        if (timeSpan.TotalMinutes < 60)
            return $"منذ {(int)timeSpan.TotalMinutes} دقيقة";
        if (timeSpan.TotalHours < 24)
            return $"منذ {(int)timeSpan.TotalHours} ساعة";
        if (timeSpan.TotalDays < 7)
            return $"منذ {(int)timeSpan.TotalDays} يوم";

        return dateTime.ToString("yyyy-MM-dd");
    }

    private void NavigateToSystemActivity()
    {
        Navigation.NavigateTo("/system-activity");
    }
}
