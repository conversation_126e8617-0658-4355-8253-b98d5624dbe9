﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PosGTech.ModelsDTO.Purchases
{
    public class PurchaseCMDTO
    {
        public int InvoiceNo { get; set; } 

        public DateOnly Date { get; set; }
        public decimal Paid { get; set; } = decimal.Zero;
        public decimal DiscountValue { get; set; } = decimal.Zero;

        public decimal FinalTotal { get; set; }

        public decimal Total { get; set; }
        public string ClientName { get; set; }

        public List<PurchaseItemDTO> PurchaseItemDTOs { get; set; } = new List<PurchaseItemDTO>();



    }
}
