@page "/error"
@page "/error/{ErrorType}"
@using MudBlazor
@using PosGTech.Web.Services.Interfaces
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject IErrorLoggingService ErrorLoggingService

<PageTitle>@GetPageTitle()</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium" Class="mt-8">
    <MudPaper Elevation="3" Class="pa-8">
        <div dir="rtl" class="text-center">
            <!-- Error Icon -->
            <MudIcon Icon="@GetErrorIcon()" 
                     Color="@GetErrorColor()" 
                     Size="Size.Large" 
                     Style="font-size: 6rem;" 
                     Class="mb-4" />
            
            <!-- Error Code/Type -->
            @if (!string.IsNullOrEmpty(ErrorCode))
            {
                <MudText Typo="Typo.h2" Color="@GetErrorColor()" Class="mb-2">
                    @ErrorCode
                </MudText>
            }
            
            <!-- Main Error Message -->
            <MudText Typo="Typo.h4" Class="mb-4">
                @GetErrorTitle()
            </MudText>
            
            <!-- Description -->
            <MudText Typo="Typo.body1" Color="Color.Secondary" Class="mb-6">
                @GetErrorDescription()
            </MudText>
            
            <!-- Error Details -->
            @if (!string.IsNullOrEmpty(ErrorMessage))
            {
                <MudAlert Severity="@GetErrorSeverity()" Class="mb-4">
                    <strong>تفاصيل الخطأ:</strong> @ErrorMessage
                </MudAlert>
            }
            
            <!-- Error ID -->
            @if (!string.IsNullOrEmpty(ErrorId))
            {
                <MudAlert Severity="Severity.Warning" Class="mb-4">
                    <strong>رقم الخطأ:</strong> @ErrorId<br/>
                    <small>يرجى ذكر هذا الرقم عند الاتصال بالدعم الفني</small>
                </MudAlert>
            }
            
            <!-- Action Buttons -->
            <div class="d-flex justify-center flex-wrap gap-3 mb-6">
                <MudButton Variant="Variant.Filled" 
                           Color="Color.Primary" 
                           StartIcon="@Icons.Material.Filled.Refresh"
                           OnClick="RetryOperation"
                           Size="Size.Large"
                           Disabled="@IsRetrying">
                    @if (IsRetrying)
                    {
                        <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                        <span class="ml-2">جاري إعادة المحاولة...</span>
                    }
                    else
                    {
                        <span>إعادة المحاولة</span>
                    }
                </MudButton>
                
                <MudButton Variant="Variant.Outlined" 
                           Color="Color.Secondary" 
                           StartIcon="@Icons.Material.Filled.Home"
                           OnClick="NavigateToHome"
                           Size="Size.Large">
                    الصفحة الرئيسية
                </MudButton>
                
                <MudButton Variant="Variant.Text"
                           Color="Color.Info"
                           StartIcon="@Icons.Material.Filled.ArrowBack"
                           OnClick="GoBack"
                           Size="Size.Large">
                    العودة للخلف
                </MudButton>

                <MudButton Variant="Variant.Text"
                           Color="Color.Warning"
                           StartIcon="@Icons.Material.Filled.BugReport"
                           OnClick="NavigateToErrorDiagnostics"
                           Size="Size.Large">
                    تشخيص الأخطاء
                </MudButton>

                <MudButton Variant="Variant.Text"
                           Color="Color.Success"
                           StartIcon="@Icons.Material.Filled.Dashboard"
                           OnClick="NavigateToSystemStatus"
                           Size="Size.Large">
                    حالة النظام
                </MudButton>
            </div>
            
            <!-- Help Section -->
            <MudDivider Class="my-6" />
            
            <MudText Typo="Typo.h6" Class="mb-3">
                هل تحتاج مساعدة؟
            </MudText>
            
            <div class="d-flex justify-center flex-wrap gap-2">
                <MudChip T="bool" Icon="@Icons.Material.Filled.ContactSupport" 
                         Color="Color.Success" 
                         Variant="Variant.Outlined"
                         OnClick="ContactSupport">
                    الاتصال بالدعم
                </MudChip>
                
                <MudChip T="bool" Icon="@Icons.Material.Filled.BugReport"
                         Color="Color.Warning" 
                         Variant="Variant.Outlined"
                         OnClick="ReportIssue">
                    الإبلاغ عن المشكلة
                </MudChip>
                
                <MudChip T="bool" Icon="@Icons.Material.Filled.History"
                         Color="Color.Info" 
                         Variant="Variant.Outlined"
                         OnClick="ShowErrorHistory">
                    سجل الأخطاء
                </MudChip>
            </div>
            
            <!-- Status Information -->
            <MudDivider Class="my-6" />
            
            <MudText Typo="Typo.caption" Color="Color.Secondary">
                وقت الخطأ: @ErrorTime.ToString("yyyy-MM-dd HH:mm:ss")
            </MudText>
        </div>
    </MudPaper>
</MudContainer>

@code {
    [Parameter] public string? ErrorType { get; set; }
    [Parameter] public string? ErrorMessage { get; set; }
    [Parameter] public string? ErrorCode { get; set; }
    [Parameter] public string? ErrorId { get; set; }
    
    private bool IsRetrying = false;
    private DateTime ErrorTime = DateTime.Now;
    
    protected override void OnInitialized()
    {
        // Generate error ID if not provided
        if (string.IsNullOrEmpty(ErrorId))
        {
            ErrorId = Guid.NewGuid().ToString("N")[..8].ToUpper();
        }
        
        // Log the error
        LogError();
    }
    
    private async void LogError()
    {
        try
        {
            await ErrorLoggingService.LogExceptionAsync(
                new Exception($"{ErrorType ?? "General"} Error: {ErrorMessage ?? "Unknown error"}"),
                "GeneralErrorPage",
                new Dictionary<string, object>
                {
                    ["ErrorType"] = ErrorType ?? "Unknown",
                    ["ErrorCode"] = ErrorCode ?? "Unknown",
                    ["ErrorId"] = ErrorId ?? "Unknown",
                    ["UserAgent"] = "Web Browser",
                    ["Timestamp"] = ErrorTime
                }
            );
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to log error: {ex.Message}");
        }
    }
    
    private string GetPageTitle()
    {
        return ErrorType?.ToLower() switch
        {
            "404" or "notfound" => "الصفحة غير موجودة - 404",
            "500" or "server" => "خطأ في الخادم - 500",
            "network" => "خطأ في الشبكة",
            "400" => "طلب غير صحيح - 400",
            "403" => "غير مصرح - 403",
            _ => "حدث خطأ"
        };
    }
    
    private string GetErrorIcon()
    {
        return ErrorType?.ToLower() switch
        {
            "404" or "notfound" => Icons.Material.Filled.SearchOff,
            "500" or "server" => Icons.Material.Filled.ErrorOutline,
            "network" => Icons.Material.Filled.WifiOff,
            "400" => Icons.Material.Filled.Warning,
            "403" => Icons.Material.Filled.Lock,
            _ => Icons.Material.Filled.Error
        };
    }
    
    private Color GetErrorColor()
    {
        return ErrorType?.ToLower() switch
        {
            "404" or "notfound" => Color.Warning,
            "500" or "server" => Color.Error,
            "network" => Color.Warning,
            "400" => Color.Warning,
            "403" => Color.Error,
            _ => Color.Error
        };
    }
    
    private string GetErrorTitle()
    {
        return ErrorType?.ToLower() switch
        {
            "404" or "notfound" => "الصفحة غير موجودة",
            "500" or "server" => "خطأ في الخادم",
            "network" => "مشكلة في الاتصال",
            "400" => "طلب غير صحيح",
            "403" => "غير مصرح بالوصول",
            _ => "حدث خطأ غير متوقع"
        };
    }
    
    private string GetErrorDescription()
    {
        return ErrorType?.ToLower() switch
        {
            "404" or "notfound" => "الصفحة التي تبحث عنها غير موجودة.",
            "500" or "server" => "حدث خطأ في الخادم. نحن نعمل على حل هذه المشكلة.",
            "network" => "لا يمكن الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت.",
            "400" => "الطلب المرسل غير صحيح أو يحتوي على بيانات خاطئة.",
            "403" => "ليس لديك صلاحية للوصول إلى هذا المورد.",
            _ => "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى."
        };
    }
    
    private Severity GetErrorSeverity()
    {
        return ErrorType?.ToLower() switch
        {
            "404" or "notfound" => Severity.Warning,
            "500" or "server" => Severity.Error,
            "network" => Severity.Warning,
            "400" => Severity.Warning,
            "403" => Severity.Error,
            _ => Severity.Error
        };
    }
    
    private async Task RetryOperation()
    {
        IsRetrying = true;
        StateHasChanged();
        
        try
        {
            await Task.Delay(2000);
            Navigation.NavigateTo(Navigation.Uri, forceLoad: true);
        }
        catch (Exception ex)
        {
            Snackbar.Add("فشلت إعادة المحاولة", Severity.Error);
            Console.WriteLine($"Retry failed: {ex.Message}");
        }
        finally
        {
            IsRetrying = false;
            StateHasChanged();
        }
    }
    
    private void NavigateToHome()
    {
        try
        {
            Navigation.NavigateTo("/", forceLoad: true);
        }
        catch (Exception ex)
        {
            Snackbar.Add("حدث خطأ أثناء الانتقال إلى الصفحة الرئيسية", Severity.Error);
        }
    }
    
    private void GoBack()
    {
        try
        {
            Navigation.NavigateTo("javascript:history.back()", forceLoad: true);
        }
        catch (Exception ex)
        {
            NavigateToHome();
        }
    }
    
    private void ContactSupport()
    {
        var message = $"يرجى الاتصال بالدعم الفني وذكر رقم الخطأ: {ErrorId}";
        Snackbar.Add(message, Severity.Info, config =>
        {
            config.VisibleStateDuration = 10000;
        });
    }
    
    private void ReportIssue()
    {
        Snackbar.Add($"تم تسجيل المشكلة برقم: {ErrorId}. شكراً لك على الإبلاغ.", Severity.Success);
    }

    private void NavigateToErrorDiagnostics()
    {
        try
        {
            Navigation.NavigateTo("/error-diagnostics", forceLoad: true);
        }
        catch (Exception ex)
        {
            Snackbar.Add("حدث خطأ أثناء الانتقال إلى صفحة تشخيص الأخطاء", Severity.Error);
        }
    }

    private void NavigateToSystemStatus()
    {
        try
        {
            Navigation.NavigateTo("/system-status", forceLoad: true);
        }
        catch (Exception ex)
        {
            Snackbar.Add("حدث خطأ أثناء الانتقال إلى صفحة حالة النظام", Severity.Error);
        }
    }
    
    private async void ShowErrorHistory()
    {
        try
        {
            var recentErrors = await ErrorLoggingService.GetRecentErrorsAsync(10);
            var errorCount = recentErrors.Count;

            if (errorCount == 0)
            {
                Snackbar.Add("لا توجد أخطاء مسجلة حديثاً", Severity.Success);
                return;
            }

            // عرض تفاصيل آخر خطأ
            var lastError = recentErrors.First();
            var errorDetails = $@"آخر خطأ مسجل:
النوع: {lastError.ExceptionType}
الرسالة: {lastError.Message}
الوقت: {lastError.Timestamp:yyyy-MM-dd HH:mm:ss}
السياق: {lastError.Context}
المنصة: {(lastError.AdditionalData.ContainsKey("Platform") ? lastError.AdditionalData["Platform"] : "غير محدد")}";

            Snackbar.Add($"تم العثور على {errorCount} أخطاء حديثة في السجل", Severity.Info, config =>
            {
                config.VisibleStateDuration = 8000;
            });

            // عرض تفاصيل آخر خطأ في رسالة منفصلة
            await Task.Delay(1000);
            Snackbar.Add(errorDetails, Severity.Warning, config =>
            {
                config.VisibleStateDuration = 15000;
            });
        }
        catch (Exception ex)
        {
            Snackbar.Add("لا يمكن الوصول إلى سجل الأخطاء حالياً", Severity.Warning);
        }
    }
}
