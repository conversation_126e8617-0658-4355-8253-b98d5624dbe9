﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Inventories;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class InventoriesController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("getAllInventories")]
        public async Task<IActionResult> GetAllInventories()
        {
            var inventories = maper.Map<IEnumerable<Inventory>, IEnumerable<InventoryDTO>>(await unitOfWork.Inventory.GetAll());
            return Ok(inventories);
        }


        [HttpGet("getInventoryById/{id:Guid}")]
        public async Task<IActionResult> GetInventoryById([FromRoute] Guid id)
        {
            var inventory = await unitOfWork.Inventory.GetInventoryById(maper, id);
            return Ok(inventory);
        }
        [HttpPost("insertInventory")]
        public async Task<IActionResult> InsertInventory([FromBody] InventoryDTO model)
        {

            var newinventory = maper.Map<InventoryDTO, Inventory>(model);
            await unitOfWork.Inventory.AddInventory(newinventory);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }
        [HttpPut("updateInventory/{id:Guid}")]
        public async Task<IActionResult> UpdateInventory([FromRoute] Guid id, InventoryDTO model)
        {
            var oldinventory = await unitOfWork.Inventory.GetFirstOrDefault(x => x.Id == id, "InventoryItems.Item,InventoryItems.ItemUnit.Unit");
            if (oldinventory == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            await unitOfWork.Inventory.UpdateInventory(oldinventory, maper.Map<InventoryDTO, Inventory>(model));
            if (unitOfWork.StoreItemExp.CheckQuantityItemsBelowZero()) return BadRequest(new ResponseVM() { State = false, Message = "الكمية غير موجودة" });
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpDelete("deleteInventory/{id:Guid}")]
        public async Task<IActionResult> DeleteInventory([FromRoute] Guid id)
        {
            var oldinventory = await unitOfWork.Inventory.GetFirstOrDefault(x => x.Id == id, "InventoryItems.Item,InventoryItems.ItemUnit.Unit");
            if (oldinventory == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            await unitOfWork.Inventory.DeleteInventory(oldinventory);
            if (unitOfWork.StoreItemExp.CheckQuantityItemsBelowZero()) return BadRequest(new ResponseVM() { State = false, Message = "الكمية غير موجودة" });
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }

    }
}
