﻿using PosGTech.Models.ViewModels;

namespace PosGTech.DataAccess.Repository.IRepository
{
    public interface IUnitOfWork : IDisposable
    {
        ICategoryRepository Category { get; }
        IClientRepository Client { get; }
        IConsumedRepository Consumed { get; }
        IConsumedItemRepository ConsumedItem { get; }
        IEmployeeRepository Employee { get; }
        IExpenseRepository Expense { get; }
        IFinancialRepository Financial { get; }
        IInventoryRepository Inventory { get; }
        IInventoryItemRepository InventoryItem { get; }
        IItemNumRepository ItemNum { get; }
        IItemUnitRepository ItemUnit { get; }
        IItemRepository Item { get; }
        IPurchaseItemRepository PurchaseItem { get; }
        IPurchaseRepository Purchase { get; }
        IReceiptRepository Receipt { get; }
        ISalaryRepository Salary { get; }
        ISellRepository Sell { get; }
        ISellItemRepository SellItem { get; }
        IStoreItemExpRepository StoreItemExp { get; }
        IStoreItemRepository StoreItem { get; }
        IStoreRepository Store { get; }
        IShopSettingsRepository ShopSettings { get; }
        ITransferItemRepository TransferItem { get; }
        ITransferRepository Transfer { get; }
        ITreasuryRepository Treasury { get; }
        IUserRepository User { get; }
        IUnitRepository Unit { get; }
        IUserTreasuryRepository UserTreasury { get; }

        // ==================== Repositories جديدة لإدارة الأدوار والصلاحيات ====================
        IRoleRepository Role { get; }
        IUserClaimRepository UserClaim { get; }

        Task SaveAsync();
        Task<ResponseVM> SaveWithTransaction();
        void Reverse();
    }
}
