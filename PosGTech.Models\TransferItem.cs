﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    public class TransferItem : BaseEntity
    {
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid TransferId { get; set; }
        public Transfer? Transfer { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid StoreItemExpFromId { get; set; }
        public StoreItemExp? StoreItemExpFrom { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal Quantity { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid ItemUnitId { get; set; }
        public ItemUnit? ItemUnit { get; set; }
    }
}
