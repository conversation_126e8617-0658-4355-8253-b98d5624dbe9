﻿namespace PosGTech.ModelsDTO.Finacnial;

public static class FinancialId
{
    public static Guid Client { get; set; } = new Guid("a35f6e62-afde-47cd-969c-ce7913ba9fdc");
    public static Guid Deposit { get; set; } = new Guid("42f890fc-3aff-4aef-a4c8-9b40d07ad186");
    public static Guid Withdrawal { get; set; } = new Guid("827a77f1-b9b2-4e76-9a32-8077d4214084");
    public static Guid Transfer { get; set; } = new Guid("bcc4fc15-7d12-44fa-89fe-aaf30e4982ab");
    public static Guid SalaryPayment { get; set; } = new Guid("b78421b2-ed3a-4400-a049-a75ecf661ca4");
    public static Guid OpeningBalanceForClient { get; set; } = new Guid("ca2aa458-16e8-4108-80eb-e7b39607eec5");
    public static Guid OpeningBalanceForEmployee { get; set; } = new Guid("6b0f1190-7298-4de7-a5e4-111369d31c11");
    public static Guid Employee { get; set; } = new Guid("ac8cc2e7-b59b-4d26-8b08-1fda72bf3c92");
    public static Guid Sale { get; set; } = new Guid("846a2f9e-b511-48c3-a872-93d28bcd25dc");
    public static Guid Purchase { get; set; } = new Guid("058813dc-c668-4483-811f-8a228941b662");
    public static Guid Expense { get; set; } = new Guid("cd05e0d2-914b-4d0f-8e59-a6e582386b89");
}
