using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.Web.Pages.Pages.Purchases;
using PosGTech.Web.Pages.Pages.Sells;

namespace PosGTech.Web.Pages.Layout;

public partial class NavMenu
{
    [Inject]
    IDialogService DialogService { get; set; }
    async void ShowPurchase()
    {
        var parameters = new DialogParameters<UpsertPurchase>();
        parameters.Add(x => x.id, Guid.Empty);
        var options = new DialogOptions() { CloseButton = false, MaxWidth = MaxWidth.Medium, FullWidth = true };
        var result = await DialogService.Show<UpsertPurchase>("", parameters, options).Result;
    }
    async void ShowSell()
    {
        var parameters = new DialogParameters<UpsertSell>();
        parameters.Add(x => x.id, Guid.Empty);
        var options = new DialogOptions() { CloseButton = false, MaxWidth = MaxWidth.Medium, FullWidth = true };
        var result = await DialogService.Show<UpsertSell>("", parameters, options).Result;
    }
    const string Purchase = @"<svg style=""width:24px;height:24px"" viewBox=""0 0 128 128"">
    <path fill=""#65878d"" d=""m95.84 41.06l-9.21.97l4.02-19.64a9.985 9.985 0 0 1 6.92-7.57l18.49-5.56c3.67-1.1 7.37 1.64 7.37 5.48c0 3.61-3.59 6.13-6.98 4.9l-2.57-.58l-10.36 3.07a6.019 6.019 0 0 0-4.13 4.34zm-9.55 41.69L79.88 110l-7.79-2.75l4.8-24.5z"" /><path fill=""#65878d"" d=""M81.76 102H18.84v8.33h61.5z"" /><path fill=""#fac136"" d=""M1.95 48.08L9.36 84.2c.57 2.8 3.03 4.8 5.88 4.8h68.52a2 2 0 0 0 1.95-1.55l12.02-52.01c.31-1.33-.77-2.56-2.13-2.44L7.29 40.9a5.995 5.995 0 0 0-5.34 7.18m13.95 1.09l4.26-.38a3.001 3.001 0 0 1 3.27 2.99v4.82c0 1.66-1.34 3-3 3h-4.48c-1.42 0-2.65-1-2.94-2.4l-.66-3.24a3.981 3.981 0 0 1 3.55-4.79m38.21 18.42h5.68c1.66 0 3 1.34 3 3V77c0 1.66-1.34 3-3 3h-5.68c-1.66 0-3-1.34-3-3v-6.41c0-1.66 1.34-3 3-3m5.68-8h-5.68c-1.66 0-3-1.34-3-3v-7.83c0-1.55 1.19-2.85 2.73-2.99l5.68-.51a3.001 3.001 0 0 1 3.27 2.99v8.34c0 1.66-1.34 3-3 3m-16.68 11V77c0 1.66-1.34 3-3 3h-5.68c-1.66 0-3-1.34-3-3v-6.41c0-1.66 1.34-3 3-3h5.68c1.66 0 3 1.34 3 3m-3-11h-5.68c-1.66 0-3-1.34-3-3v-6.07c0-1.55 1.19-2.85 2.73-2.99l5.68-.51a3.001 3.001 0 0 1 3.27 2.99v6.58c0 1.66-1.34 3-3 3m45.74-12.74l-2.41 10.41a2.995 2.995 0 0 1-2.92 2.32h-6.73c-1.66 0-3-1.34-3-3V47c0-1.55 1.19-2.85 2.73-2.99l9.13-.82c2.04-.18 3.66 1.68 3.2 3.66M75.01 80H72.8c-1.1 0-2-.9-2-2v-7.41c0-1.66 1.34-3 3-3h3.49c1.93 0 3.36 1.8 2.92 3.68l-1.3 5.64a4.01 4.01 0 0 1-3.9 3.09m-59.26-9.44c-.32-1.54.86-2.98 2.43-2.98h2.25c1.66 0 3 1.34 3 3v6.93a2.48 2.48 0 0 1-2.48 2.48c-1.9 0-3.54-1.34-3.92-3.2z"" /><circle cx=""75.54"" cy=""106.33"" r=""11"" fill=""#2f2f2f"" /><circle cx=""75.54"" cy=""106.33"" r=""5.13"" fill=""#65878d"" /><circle cx=""22.7"" cy=""106.33"" r=""11"" fill=""#2f2f2f"" /><circle cx=""22.7"" cy=""106.33"" r=""5.13"" fill=""#65878d"" /><circle cx=""118.59"" cy=""16.25"" r=""7.41"" fill=""#65878d"" /></svg>";

    const string Users = @"<svg width=""24"" height=""24"" viewBox=""0 0 128 128"">
    <path fill=""#212121"" d=""M123 89.33H5c-2.75 0-5-2.25-5-5V5c0-2.75 2.25-5 5-5h118c2.75 0 5 2.25 5 5v79.33c0 2.75-2.25 5-5 5"" />
    <g fill=""#00dc00"">
    <path d=""M6.74 7.9h66.74v2.03H6.74zm106.57 0h7.94v2.03h-7.94zm-23.76 0h19.62v2.03H89.55zM6.74 13.75h5.03v2.03H6.74zm92.65 0h21.86v2.03H99.39zm-80.85 0h19.62v2.03H18.54zm-8.29 5.84H28v2.03H10.25zm79.3 0h19.62v2.03H89.55zM6.74 25.44h9.1v2.03h-9.1zm91.73 0h19.62v2.03H98.47zm-86.9 5.84H68.1v2.03H11.57zm77.98 0h19.62v2.03H89.55zm0 5.85h19.62v2.03H89.55z"" />
    <path d=""M50.24 25.55h52.17v2.03H50.24zM6.74 38.09h66.74v2.03H6.74zm106.57 0h7.94v2.03h-7.94zm-23.76 0h19.62v2.03H89.55zM6.74 43.93h5.03v2.03H6.74zm108.75 8.18h5.03v2.03h-5.03zm-16.1-8.18h21.86v2.03H99.39zM7.2 69.4h19.62v2.03H7.2zm3.05-19.62H28v2.03H10.25zm79.3 0h19.62v2.03H89.55zM6.74 55.62h9.1v2.03h-9.1zm82.81 0h19.62v2.03H89.55zm-77.98 5.85H68.1v2.03H11.57z"" />
    <path d=""M60.83 61.47h56.53v2.03H60.83z"" />
    <path d=""M89.55 61.47h19.62v2.03H89.55zm-35.59 5.85h66.74v2.03H53.96z"" />
    <path d=""M89.55 67.32h19.62v2.03H89.55z"" />
    </g>
    <path fill=""#00bfa5"" d=""M20.23 118.09v9.89h87.14v-9.89c0-15.17-21.39-22.87-43.24-22.84c-21.72.03-43.9 6.78-43.9 22.84"" />
    <path fill=""#e49500"" d=""M63.8 90.25h-9.08v9.59c0 4.34 3.7 7.86 8.26 7.86h1.65c4.56 0 8.26-3.52 8.26-7.86v-9.59z"" />
    <path fill=""#fac036"" d=""M17.4 109.03s-.52-.71-2.21-.79c-.14-.21-.28-.4-.41-.58c-.26-.35-.92-1.86-3.43-.49c-.81.44-1.09.84-.96 1.41c-.1.12-.28.26-.56.15c-.87-.97-1.95-1.18-3.2-.2c-1.42 1.11-1.42 1.9-1.16 2.69h-.01s-.06.55-.47.5c-.43-.37-.95-.37-1.76.22c-2.19 1.58-1.28 3.08-.92 3.96c-.05.08-.13.14-.27.13c-.28-.15-.65-.15-1.14.13c-1.16.66-.99 2.85-.66 3.57c.36.78 2.35 3.52 3.04 4.29s2.08.71 2.77-.09c.5-.58.87-1.08-.26-2.12c-.03-.03-.07-.07-.11-.1l.23-.09c.63.83 1.56 1.72 2.88.97c.66-.38 1.24-1.24.71-2.41l.77-.31c.06.09.12.17.18.26c.71 1.02 2.2.66 3.05.18l-.5.56l-.87.36s-1.4 1.28-.05 2.7c.78.82 1.71.32 3.25-.3c1.17-.47 5.77-3.23 5.87-4.1c0 0 .9-3.45.13-5.44c-.5-1.32-2.15-3.35-3.93-5.06"" />
    <path fill=""#e48c15"" d=""M15.93 108.32s-.63-.07-.74-.08c1.06 1.58 2.3 3.93 2.8 4.89c.8 1.54 1.33 3.05-.1 4.13c-.66.5-1.26.57-1.75.37c-.48-.39-.91-1.19-.91-1.19c-1.89-3.47-3.2-5.21-3.2-5.21c-.13-.15-.41-.63-.8-.87c-.5-.3-.35.23-.35.23c2.2 3.43 3.54 6.17 3.54 6.17c.57 1.04.47 1.78.31 2.19c-.19.5-1.28 1.2-1.28 1.2c-2.22 1.33-3.24-.44-3.24-.44s-1.3-1.63-3.48-5.7c0 0-.34-.56-.55-.17c0 0 .12.69.44 1.34c0 0 .99 2.36 2.92 5.08c.47 1.13-.09 1.96-.74 2.34c-.79.45-1.44.31-1.97-.05c-.49-.48-1.64-1.79-3.32-4.81c0 0-.33-.75-.49-.49c0 0-.17.2.29 1.36c0 0 .67 1.67 2.34 3.89c.62.86.55 1.28.3 1.54c-.56.56-1.49.69-2.18.36c.6.37 1.64.82 2.65.19c0 0 .65-.47.7-.97c1.1.61 2.62-.53 2.62-.53c.71-.55.9-1.16.88-1.69c1.46.87 3.33-.33 3.33-.33c1.64-.93 1.96-1.91 1.96-2.55c.85.53 1.9.13 1.9.13c1.85-.82 1.79-2.63 1.79-2.63c-.08-2.5-2.5-6.04-3.67-7.7"" />
    <path fill=""#e0e0e0"" d=""M18.8 116.32c.01.15.01.3.01.45c0 4.12-1.99 7.45-4.43 7.45c-2.45 0-4.43-3.34-4.43-7.45c0-.15.01-.3.01-.45c-2.42 1.75-4.04 4.9-4.04 8.49v.65c0 1.41.97 2.55 2.16 2.55h12.6c1.19 0 2.16-1.14 2.16-2.55v-.65c0-3.6-1.62-6.74-4.04-8.49"" />
    <path fill=""#bdbdbd"" d=""M14.38 124.22c9.04 0 4.43-3.34 4.43-7.45c0-.15-.01-.3-.01-.45c-1.29-.93-2.8-1.48-4.42-1.48s-3.13.55-4.42 1.48c-.01.15-.01.3-.01.45c0 4.12-4.65 7.45 4.43 7.45"" />
    <path fill=""none"" d=""M9.68 121.37c0 .03-.01.05-.02.08c0-.03.01-.05.02-.08m-2.35 1.4c-.05-.01-.09-.03-.13-.05c.04.02.08.03.13.05m-1.37 1.24s.01-.01.01-.02l-.06.06c.02-.01.04-.02.05-.04m.87-1.48l.06.03c-.02-.02-.05-.03-.08-.05c.01.01.02.01.02.02m-1.56 1.91c-.04.01-.07.03-.11.04c.04-.02.08-.03.11-.04m3-1.65c-.05.01-.1.02-.16.03c.06-.01.11-.01.16-.03m1.17-.82c-.02.03-.04.05-.06.08c.02-.03.04-.05.06-.08m-1.66.88c-.05 0-.11-.01-.16-.02c.06.01.11.02.16.02m1.47-.64c-.03.04-.07.07-.11.1c.04-.03.08-.06.11-.1m.34-.52c-.01.03-.03.05-.04.07c.01-.03.03-.05.04-.07m-4.63 2.83c-.05.01-.1.01-.15.02c.05-.01.1-.01.15-.02m4.57-4.28s0-.01-.01-.01c.02.04.03.08.04.12c0-.04-.01-.07-.03-.11m.13.39c.01.04.01.07.02.11c-.01-.04-.01-.07-.02-.11m-5.47 3.88a1.17 1.17 0 0 1-.2-.05c.06.02.13.03.2.05m.44.04c-.1 0-.21 0-.31-.02c.1.02.21.03.31.02m5.08-3.53c0 .03 0 .06-.01.09c0-.03.01-.06.01-.09"" />
    <path fill=""#fac036"" d=""m4.19 124.51l.13.03c.1.01.21.02.31.02c.06 0 .12-.01.18-.02c.05-.01.1-.01.15-.02c.07-.01.13-.03.2-.05c.04-.01.07-.02.11-.04c.24-.08.46-.21.64-.38l.06-.06c.24-.26.3-.68-.31-1.52c-1.68-2.22-2.34-3.89-2.34-3.89c-.46-1.17-.29-1.36-.29-1.36c.16-.26.49.49.49.49c1.67 3 2.81 4.31 3.3 4.8c.02.02.05.03.08.05c.1.06.2.12.3.16c.04.02.09.03.13.05c.1.03.2.05.3.07c.05.01.1.01.16.02c.11 0 .22-.01.33-.03c.05-.01.1-.01.16-.03c.17-.05.34-.11.53-.22c.08-.05.16-.1.24-.16c.04-.03.07-.07.11-.1c.04-.03.08-.07.11-.1c.05-.05.08-.11.13-.16c.02-.03.04-.05.06-.08c.04-.06.08-.14.11-.21c.01-.02.03-.05.04-.07c.03-.08.05-.16.07-.24c.01-.03.02-.05.02-.08c.02-.08.02-.17.02-.26c0-.03.01-.06.01-.09c0-.09-.02-.18-.03-.28c-.01-.04-.01-.07-.02-.11c-.02-.09-.06-.19-.09-.28c-.01-.04-.03-.08-.04-.12c-1.92-2.71-2.91-5.07-2.91-5.07c-.32-.66-.44-1.34-.44-1.34c.21-.39.55.17.55.17c2.18 4.07 3.48 5.7 3.48 5.7s1.02 1.77 3.24.44c0 0 1.1-.71 1.28-1.2c.16-.41.26-1.15-.31-2.19c0 0-1.34-2.74-3.54-6.17c0 0-.15-.54.35-.23c.39.24.67.72.8.87c0 0 1.3 1.74 3.2 5.21c0 0 .42.8.91 1.19c.48.2 1.09.13 1.75-.37c1.43-1.08.9-2.59.1-4.13c-.5-.96-1.74-3.31-2.8-4.89c.09.01.5.06.67.07c-.2-.04-.42-.07-.67-.08c-.14-.21-.28-.4-.41-.58c-.26-.35-.92-1.86-3.43-.49c-.81.44-1.09.84-.96 1.41c-.1.12-.28.26-.56.15c-.87-.97-1.95-1.18-3.2-.2c-1.42 1.11-1.42 1.9-1.16 2.69h-.01s-.06.55-.47.5c-.43-.37-.95-.37-1.76.22c-2.19 1.58-1.28 3.08-.92 3.96c-.05.08-.13.14-.27.13c-.28-.15-.65-.15-1.14.13c-1.16.66-.99 2.85-.66 3.57c.36.78 2.35 3.52 3.04 4.29c.16.17.35.3.56.39c.04.02.09.04.13.05c.06.04.13.05.2.07"" />
    <path fill=""#e48c15"" d=""m13.01 120.86l.5-.56c-.85.48-2.33.84-3.05-.18c-.06-.09-.12-.17-.18-.26l-.77.31c.01.02.01.04.02.06c0 0 0 .01.01.01c.02.04.02.07.04.11c.03.1.07.19.09.28c.01.04.01.07.02.11c.02.09.03.19.03.28c0 .03 0 .06-.01.09c0 .09-.01.18-.02.26c0 .03-.01.05-.02.08c-.02.08-.04.16-.07.24c-.01.03-.03.05-.04.07c-.03.07-.07.14-.11.21c-.02.03-.04.05-.06.08c-.04.06-.08.11-.13.16c-.03.04-.07.07-.11.1c-.04.03-.07.07-.11.1a2.134 2.134 0 0 1-.77.38c-.05.01-.1.02-.16.03c-.11.02-.23.03-.33.03c-.05 0-.11-.01-.16-.02c-.1-.01-.2-.04-.3-.07c-.05-.02-.09-.03-.13-.05c-.1-.04-.2-.1-.3-.16l-.06-.03l-.01-.01c-.35-.24-.65-.58-.9-.91l-.23.09c.04.04.08.07.11.1c1.13 1.04.75 1.54.26 2.12c-.03.04-.06.06-.08.09c0 0-.01.01-.01.02c-.02.01-.03.03-.05.04c-.19.17-.4.3-.64.38c-.04.01-.07.03-.11.04c-.07.02-.13.03-.2.05c-.05.01-.1.01-.15.02c-.06.01-.12.01-.18.02c-.1 0-.21 0-.31-.02c-.05-.01-.09-.02-.13-.03a1.17 1.17 0 0 1-.2-.05c-.05-.02-.09-.03-.13-.05c-.02-.01-.05-.02-.07-.03c.6.37 1.64.82 2.65.19c0 0 .65-.47.7-.97c1.1.61 2.62-.53 2.62-.53c.71-.55.9-1.16.88-1.69c.36.22.75.3 1.14.31c.17-.29.36-.46.36-.46zm2.92-12.54s-.03 0-.07-.01c.02.01.05.01.07.01c0 .01 0 0 0 0"" />
    <path fill=""#e48c15"" d=""M9.68 120.74c.02.09.03.19.03.28c0-.09-.02-.19-.03-.28m.02.37c0 .09-.01.18-.02.26c.02-.08.02-.17.02-.26m-.32.94c-.04.06-.08.11-.13.16c.05-.05.09-.11.13-.16m.17-.29c-.03.07-.07.14-.11.21c.04-.07.08-.14.11-.21m.02-1.41c.03.1.07.19.09.28c-.02-.09-.05-.18-.09-.28m-4.76 4.19c-.06.01-.12.01-.18.02c.06-.01.12-.02.18-.02m-.49-.01c-.05-.01-.09-.02-.13-.03c.04.02.08.03.13.03m-1.29-7.31s-.17.2.29 1.36c0 0 .67 1.67 2.34 3.89c.61.85.55 1.26.31 1.52c.03-.03.06-.05.08-.07c.49-.58.87-1.08-.26-2.12c-.03-.03-.07-.07-.11-.1l.23-.09c.25.33.55.66.9.91c-.5-.49-1.64-1.8-3.3-4.8c0-.01-.33-.76-.48-.5m10.94 3.82c1.64-.93 1.96-1.91 1.96-2.55c.85.53 1.9.13 1.9.13c1.85-.82 1.79-2.63 1.79-2.63c-.09-2.47-2.51-6-3.69-7.66c-.03-.01-.05-.01-.08-.02c-.16-.02-.58-.06-.67-.07c1.06 1.58 2.3 3.93 2.8 4.89c.8 1.54 1.33 3.05-.1 4.13c-.66.5-1.26.57-1.75.37c-.48-.39-.91-1.19-.91-1.19c-1.89-3.47-3.2-5.21-3.2-5.21c-.13-.15-.41-.63-.8-.87c-.5-.3-.35.23-.35.23c2.2 3.43 3.54 6.17 3.54 6.17c.57 1.04.47 1.78.31 2.19c-.19.5-1.28 1.2-1.28 1.2c-2.22 1.33-3.24-.44-3.24-.44s-1.3-1.63-3.48-5.7c0 0-.34-.56-.55-.17c0 0 .12.69.44 1.34c0 0 .99 2.36 2.91 5.07c-.01-.02-.01-.04-.02-.06l.77-.31c.06.09.12.17.18.26c.71 1.02 2.2.66 3.05.18l-.5.56l-.87.36s-.19.17-.36.46c1.14.02 2.2-.66 2.2-.66m-4.83 1.27c-.04.03-.07.07-.11.1c.04-.03.08-.06.11-.1m-5.16 2.14c-.05-.02-.09-.03-.13-.05c.04.02.09.04.13.05m4.82-1.87c-.18.11-.36.17-.53.22c.17-.05.34-.12.53-.22m-1.18.26c-.1-.01-.2-.04-.3-.07c.1.03.2.05.3.07m-.43-.12c-.1-.04-.2-.1-.3-.16c.1.06.2.12.3.16m-2.03 1.75c-.07.02-.13.03-.2.05c.07-.01.14-.03.2-.05"" />
    <path fill=""none"" d=""M118.32 121.37c0 .03.01.05.02.08c0-.03-.01-.05-.02-.08m2.35 1.4c.05-.01.09-.03.13-.05c-.04.02-.08.03-.13.05m1.37 1.24s-.01-.01-.01-.02l.06.06c-.02-.01-.04-.02-.05-.04m-.87-1.48l-.06.03c.02-.02.05-.03.08-.05c-.01.01-.02.01-.02.02m1.56 1.91c.04.01.07.03.11.04c-.04-.02-.08-.03-.11-.04m-3-1.65c.05.01.1.02.16.03c-.06-.01-.11-.01-.16-.03m-.77-.38c.08.06.16.12.24.16c-.08-.04-.16-.1-.24-.16m-.4-.44c.02.03.04.05.06.08c-.02-.03-.04-.05-.06-.08m1.66.88c.05 0 .11-.01.16-.02c-.06.01-.11.02-.16.02m-1.81-1.16c.01.03.03.05.04.07c-.01-.03-.03-.05-.04-.07m4.63 2.83c.05.01.1.01.15.02c-.05-.01-.1-.01-.15-.02m-4.57-4.28s0-.01.01-.01c-.02.04-.03.08-.04.12c0-.04.01-.07.03-.11m-.13.39c-.01.04-.01.07-.02.11c.01-.04.01-.07.02-.11m5.47 3.88c.07-.01.14-.03.2-.05c-.06.02-.13.03-.2.05m-.44.04c.1 0 .21 0 .31-.02c-.1.02-.21.03-.31.02m-5.08-3.53c0 .03 0 .06.01.09c0-.03-.01-.06-.01-.09"" />
    <path fill=""#fac036"" d=""m123.81 124.51l-.13.03c-.1.01-.21.02-.31.02c-.06 0-.12-.01-.18-.02c-.05-.01-.1-.01-.15-.02c-.07-.01-.13-.03-.2-.05a.487.487 0 0 1-.11-.04a1.73 1.73 0 0 1-.64-.38l-.06-.06c-.24-.26-.3-.68.31-1.52c1.68-2.22 2.34-3.89 2.34-3.89c.46-1.17.29-1.36.29-1.36c-.16-.26-.49.49-.49.49c-1.67 3-2.81 4.31-3.3 4.8c-.02.02-.05.03-.08.05c-.1.06-.2.12-.3.16c-.04.02-.09.03-.13.05c-.1.03-.2.05-.3.07c-.05.01-.1.01-.16.02c-.11 0-.22-.01-.33-.03c-.05-.01-.1-.01-.16-.03c-.17-.05-.34-.11-.53-.22c-.08-.05-.16-.1-.24-.16c-.04-.03-.07-.07-.11-.1c-.04-.03-.08-.07-.11-.1c-.05-.05-.08-.11-.13-.16c-.02-.03-.04-.05-.06-.08a1.39 1.39 0 0 1-.11-.21c-.01-.02-.03-.05-.04-.07c-.03-.08-.05-.16-.07-.24c-.01-.03-.02-.05-.02-.08c-.02-.08-.02-.17-.02-.26c0-.03-.01-.06-.01-.09c0-.09.02-.18.03-.28c.01-.04.01-.07.02-.11c.02-.09.06-.19.09-.28c.01-.04.03-.08.04-.12c1.92-2.71 2.91-5.07 2.91-5.07c.32-.66.44-1.34.44-1.34c-.21-.39-.55.17-.55.17c-2.18 4.07-3.48 5.7-3.48 5.7s-1.02 1.77-3.24.44c0 0-1.1-.71-1.28-1.2c-.16-.41-.26-1.15.31-2.19c0 0 1.34-2.74 3.54-6.17c0 0 .15-.54-.35-.23c-.39.24-.67.72-.8.87c0 0-1.3 1.74-3.2 5.21c0 0-.42.8-.91 1.19c-.48.2-1.09.13-1.75-.37c-1.43-1.08-.9-2.59-.1-4.13c.5-.96 1.74-3.31 2.8-4.89c-.09.01-.5.06-.67.07c.2-.04.42-.07.67-.08c.14-.21.28-.4.41-.58c.26-.35.92-1.86 3.43-.49c.81.44 1.09.84.96 1.41c.1.12.28.26.56.15c.87-.97 1.95-1.18 3.2-.2c1.42 1.11 1.42 1.9 1.16 2.69h.01s.06.55.47.5c.43-.37.95-.37 1.76.22c2.19 1.58 1.28 3.08.92 3.96c.05.08.13.14.27.13c.28-.15.65-.15 1.14.13c1.16.66.99 2.85.66 3.57c-.36.78-2.35 3.52-3.04 4.29c-.16.17-.35.3-.56.39c-.04.02-.09.04-.13.05c-.06.04-.13.05-.2.07"" />
    <path fill=""#e48c15"" d=""m114.99 120.86l-.5-.56c.85.48 2.33.84 3.05-.18c.06-.09.12-.17.18-.26l.77.31c-.01.02-.01.04-.02.06c0 0 0 .01-.01.01c-.02.04-.02.07-.04.11c-.03.1-.07.19-.09.28c-.01.04-.01.07-.02.11c-.02.09-.03.19-.03.28c0 .03 0 .06.01.09c0 .09.01.18.02.26c0 .03.01.05.02.08c.02.08.04.16.07.24c.01.03.03.05.04.07c.03.07.07.14.11.21c.02.03.04.05.06.08c.04.06.08.11.13.16c.03.04.07.07.11.1c.04.03.07.07.11.1a2.134 2.134 0 0 0 .77.38c.05.01.1.02.16.03c.11.02.23.03.33.03c.05 0 .11-.01.16-.02c.1-.01.2-.04.3-.07c.05-.02.09-.03.13-.05c.1-.04.2-.1.3-.16l.06-.03l.01-.01c.35-.24.65-.58.9-.91l.23.09c-.04.04-.08.07-.11.1c-1.13 1.04-.75 1.54-.26 2.12c.02.03.06.05.08.07c0 0 .01.01.01.02c.02.01.03.03.05.04c.19.17.4.3.64.38c.04.01.07.03.11.04c.07.02.13.03.2.05c.05.01.1.01.15.02c.06.01.12.01.18.02c.1 0 .21 0 .31-.02c.05-.01.09-.02.13-.03c.07-.01.14-.03.2-.05c.05-.02.09-.03.13-.05c.02-.01.05-.02.07-.03c-.6.37-1.64.82-2.65.19c0 0-.65-.47-.7-.97c-1.1.61-2.62-.53-2.62-.53c-.71-.55-.9-1.16-.88-1.69c-.36.22-.75.3-1.14.31c-.17-.29-.36-.46-.36-.46zm-2.92-12.54s.03 0 .07-.01c-.02.01-.05.01-.07.01c0 .01 0 0 0 0"" />
    <path fill=""#e48c15"" d=""M118.32 120.74c-.02.09-.03.19-.03.28c0-.09.02-.19.03-.28m-.02.37c0 .09.01.18.02.26c-.02-.08-.02-.17-.02-.26m.32.94c.04.06.08.11.13.16c-.05-.05-.09-.11-.13-.16m-.19-1.7c-.03.1-.07.19-.09.28c.02-.09.05-.18.09-.28m-.09 1.1c.02.08.04.16.07.24c-.03-.08-.05-.16-.07-.24m4.85 3.09c.06.01.12.01.18.02c-.06-.01-.12-.02-.18-.02m.49-.01c.05-.01.09-.02.13-.03c-.04.02-.08.03-.13.03m1.29-7.31s.17.2-.29 1.36c0 0-.67 1.67-2.34 3.89c-.61.85-.55 1.26-.31 1.52c-.03-.03-.06-.05-.08-.07c-.49-.58-.87-1.08.26-2.12c.03-.03.07-.07.11-.1l-.23-.09c-.25.33-.55.66-.9.91c.5-.49 1.64-1.8 3.3-4.8c0-.01.33-.76.48-.5m-10.94 3.82c-1.64-.93-1.96-1.91-1.96-2.55c-.85.53-1.9.13-1.9.13c-1.85-.82-1.79-2.63-1.79-2.63c.09-2.47 2.51-6 3.69-7.66c.03-.01.05-.01.08-.02c.16-.02.58-.06.67-.07c-1.06 1.58-2.3 3.93-2.8 4.89c-.8 1.54-1.33 3.05.1 4.13c.66.5 1.26.57 1.75.37c.48-.39.91-1.19.91-1.19c1.89-3.47 3.2-5.21 3.2-5.21c.13-.15.41-.63.8-.87c.5-.3.35.23.35.23c-2.2 3.43-3.54 6.17-3.54 6.17c-.57 1.04-.47 1.78-.31 2.19c.19.5 1.28 1.2 1.28 1.2c2.22 1.33 3.24-.44 3.24-.44s1.3-1.63 3.48-5.7c0 0 .34-.56.55-.17c0 0-.12.69-.44 1.34c0 0-.99 2.36-2.91 5.07c.01-.02.01-.04.02-.06l-.77-.31c-.06.09-.12.17-.18.26c-.71 1.02-2.2.66-3.05.18l.5.56l.87.36s.19.17.36.46c-1.14.02-2.2-.66-2.2-.66m4.83 1.27c.04.03.07.07.11.1c-.04-.03-.08-.06-.11-.1m5.16 2.14c.05-.02.09-.03.13-.05c-.04.02-.09.04-.13.05m-4.82-1.87c.18.11.36.17.53.22a2.85 2.85 0 0 1-.53-.22m1.18.26c.1-.01.2-.04.3-.07c-.1.03-.2.05-.3.07m.43-.12c.1-.04.2-.1.3-.16c-.1.06-.2.12-.3.16m2.03 1.75c.07.02.13.03.2.05a1.17 1.17 0 0 1-.2-.05"" />
    <path fill=""#e39400"" d=""M96.05 52.31s5.89 1.02 5.89 7.37c0 5.6-4.3 7.81-8.6 7.81V52.31zm-64.28 0s-5.89 1.02-5.89 7.37c0 5.6 4.3 7.81 8.6 7.81V52.31z"" />
    <path fill=""#fac01b"" d=""M63.91 10.75c-25.4 0-32.04 19.69-32.04 47.35c0 28.68 18.43 36.1 32.04 36.1c13.41 0 32.04-7.2 32.04-36.1c.01-27.66-6.64-47.35-32.04-47.35"" />
    <path fill=""#444"" d=""M53.36 60.67c0 2.86-1.91 5.17-4.28 5.17c-2.36 0-4.29-2.32-4.29-5.17c0-2.86 1.92-5.18 4.29-5.18s4.28 2.31 4.28 5.18m21.1 0c0 2.86 1.91 5.17 4.29 5.17c2.36 0 4.28-2.32 4.28-5.17c0-2.86-1.92-5.18-4.28-5.18c-2.38 0-4.29 2.31-4.29 5.18"" />
    <path fill=""#513f35"" d=""M73.19 78.14H54.65c-1.06 0-1.52.71-.82 1.79c.98 1.5 4.64 4.38 10.09 4.38s9.11-2.88 10.09-4.38c.7-1.08.24-1.79-.82-1.79"" />
    <path fill=""#e39400"" d=""M68.39 70.25c-1.58.45-3.22.66-4.48.66c-1.26 0-2.9-.22-4.48-.66c-.67-.19-.94.45-.7.87c.5.88 2.52 2.66 5.18 2.66c2.66 0 4.68-1.78 5.18-2.66c.24-.42-.02-1.06-.7-.87"" />
    <path fill=""#6d4c41"" d=""M64.68 7.14s-.5-.02-.71-.01c-.21 0-.71.01-.71.01c-27.41.53-35.67 17.01-35.67 28.45c0 11.11.01 19.28.01 19.28s.44-.45 1.24-1.08c.86-.67 1.81-1.04 1.81-1.04l1.38 9.28l9.13-19.08c.06-.12.18-.19.31-.17l5.41.6c5.44.6 10.91.9 16.37.91v.01c.24 0 .47-.01.71-.01s.47.01.71.01v-.01c5.46-.02 10.93-.31 16.37-.91l5.41-.6c.13-.***********.17l9.13 19.08l1.38-9.28s.95.38 1.81 1.04c.81.63 1.24 1.08 1.24 1.08s.01-8.16.01-19.28c.02-11.44-8.24-27.92-35.65-28.45"" />
    <radialGradient id=""notoV1ManTechnologist0"" cx=""113.473"" cy=""102.745"" r=""32.446"" gradientTransform=""matrix(1.2112 0 0 1.088 -72.905 -26.703)"" gradientUnits=""userSpaceOnUse"">
    <stop offset=""0"" stop-color=""#fff"" />
    <stop offset=""1"" stop-color=""#fff"" stop-opacity=""0"" />
    </radialGradient>
    <ellipse cx=""64.4"" cy=""84.29"" fill=""url(#notoV1ManTechnologist0)"" rx=""43.38"" ry=""39.29"" />
    <path fill=""#4dd0e1"" d=""M104.87 74.71H23.13c-3.58 0-6.5 2.93-6.5 6.5v51.32c0 3.58 2.93 6.5 6.5 6.5h81.75a6.5 6.5 0 0 0 6.5-6.5V81.21c0-3.59-2.91-6.5-6.51-6.5"" />
    <path fill=""#00acc1"" d=""M78.88 125.35c-1.43-4.91-2.03-13.09-2.28-18.81a6.757 6.757 0 0 0-6.75-6.46h-8.94a6.75 6.75 0 0 0-6.75 6.46c-.25 5.72-.85 13.91-2.28 18.81c-2.37 8.13-6.08 14.73.36 14.73h26.28c6.44 0 2.73-6.6.36-14.73"" />
</svg>";


    const string System = @"<svg  width=""24"" height=""24"" viewBox=""0 0 128 128"">
    <path fill=""#78a3ad"" d=""M125.52 109.7c-2.86-4.07-9.18-6.24-14.01-6.11c-1.97.05-3.78.58-5.15 1.77c-1.58-.34-3-.97-3.56-2.19c-.45-.95-.13-2.08.45-3.81c.59-1.75 1.26-3.74.57-5.86c-.87-2.66-3.75-5.85-12.8-5.7c-.98.01-1.78.82-1.76 1.81c.01.98.81 1.77 1.78 1.77h.03c5.25-.08 8.66 1.12 9.35 3.23c.32.99-.09 2.2-.56 3.6c-.63 1.85-1.4 4.15-.3 6.48c.99 2.1 2.87 3.21 4.91 3.84c-.74 4.12 4.04 8.41 7.21 10.28c3.42 2.01 8.1 3.27 11.88 1.75c4.86-1.93 4.55-7.15 1.96-10.86M90.93 97.13H14.26c-1.07 0-2.12.85-2.33 1.9l-4.4 21.55c-.21 1.04.48 1.9 1.55 1.9h87.03c1.06 0 1.76-.85 1.55-1.9l-4.4-21.55c-.22-1.05-1.26-1.9-2.33-1.9M46.21 84.49H31.24c-2.29 0-4.16 1.74-4.16 3.88v3.74h50.15v-3.74c0-2.13-1.88-3.88-4.17-3.88H58.1V77.8h39.86c3.13 0 5.7-2.57 5.7-5.71V11.24c0-3.14-2.57-5.71-5.7-5.71H6.35C3.21 5.53.64 8.1.64 11.24V72.1c0 3.14 2.57 5.71 5.71 5.71h39.86z"" />
    <defs>
    <path id=""notoV1Laptop0"" d=""M12.47 63.07c-2.4 0-4.35-1.96-4.35-4.36V18.12c0-2.4 1.95-4.36 4.35-4.36h79.36c2.39 0 4.35 1.96 4.35 4.36v40.59c0 2.4-1.96 4.36-4.35 4.36z"" />
    </defs>
    <use fill=""#006ca2"" href=""#notoV1Laptop0"" />
    <clipPath id=""notoV1Laptop1"">
    <use href=""#notoV1Laptop0"" />
    </clipPath>
    <path fill=""#fff"" d=""M50.73 11.35L32.85 68.11l-27.63-.28V11.92z"" clip-path=""url(#notoV1Laptop1)"" />
    <path fill=""#fff"" d=""M18.67 102.69h68.42c1.07 0 1.94.87 1.94 1.94c0 1.07-.87 1.94-1.94 1.94H18.67c-1.06 0-1.94-.87-1.94-1.94c0-1.08.88-1.94 1.94-1.94m70.28 13.23H16.82c-1.07 0-1.94-.87-1.94-1.94c0-1.07.87-1.94 1.94-1.94h72.14a1.94 1.94 0 0 1-.01 3.88"" />
    <path fill=""#006ca2"" d=""M91.64 18.3v40.22H12.66V18.3zm.2-4.54H12.47c-2.4 0-4.35 1.96-4.35 4.36v40.59c0 2.4 1.95 4.36 4.35 4.36h79.36c2.39 0 4.35-1.96 4.35-4.36V18.12c0-2.4-1.96-4.36-4.34-4.36"" />
</svg>";


    const string Connection = @"<svg width=""24"" height=""24"" viewBox=""0 0 36 36"">
    <path fill=""#ef9645"" d=""M16.428 30.331a2.31 2.31 0 0 0 3.217-.568a.798.798 0 0 0-.197-1.114l-1.85-1.949l4.222 2.955a1.497 1.497 0 0 0 2.089-.369a1.5 1.5 0 0 0-.369-2.089l-3.596-3.305l5.375 3.763a1.497 1.497 0 0 0 2.089-.369a1.5 1.5 0 0 0-.369-2.089l-4.766-4.073l5.864 4.105a1.497 1.497 0 0 0 2.089-.369a1.5 1.5 0 0 0-.369-2.089L4.733 11.194l-3.467 5.521c-.389.6-.283 1.413.276 1.891l7.786 6.671c.355.304.724.591 1.107.859z"" />
    <path fill=""#ffdc5d"" d=""M29.802 21.752L18.5 13.601l-.059-.08l.053-.08l.053-.053l.854.469c.958.62 3.147 1.536 4.806 1.536c1.135 0 1.815-.425 2.018-1.257a1.409 1.409 0 0 0-1.152-1.622a6.788 6.788 0 0 1-2.801-1.091l-.555-.373c-.624-.421-1.331-.898-1.853-1.206c-.65-.394-1.357-.585-2.163-.585c-1.196 0-2.411.422-3.585.83l-1.266.436a5.18 5.18 0 0 1-1.696.271c-1.544 0-3.055-.586-4.516-1.152l-.147-.058a1.389 1.389 0 0 0-1.674.56L1.35 15.669a1.357 1.357 0 0 0 .257 1.761l7.785 6.672c.352.301.722.588 1.1.852l6.165 4.316a2 2 0 0 0 2.786-.491a.803.803 0 0 0-.196-1.115l-1.833-1.283a.424.424 0 0 1-.082-.618a.422.422 0 0 1 .567-.075l3.979 2.785a1.4 1.4 0 0 0 1.606-2.294l-3.724-2.606a.424.424 0 0 1-.082-.618a.423.423 0 0 1 .567-.075l5.132 3.593a1.4 1.4 0 0 0 1.606-2.294l-4.868-3.407a.42.42 0 0 1-.081-.618a.377.377 0 0 1 .506-.066l5.656 3.959a1.4 1.4 0 0 0 1.606-2.295"" />
    <path fill=""#ef9645"" d=""M16.536 27.929c-.07.267-.207.498-.389.681l-1.004.996a1.494 1.494 0 0 1-1.437.396a1.5 1.5 0 0 1-.683-2.512l1.004-.996a1.494 1.494 0 0 1 1.437-.396a1.502 1.502 0 0 1 1.072 1.831M5.992 23.008l1.503-1.497a1.5 1.5 0 0 0-.444-2.429a1.495 1.495 0 0 0-1.674.31l-1.503 1.497a1.5 1.5 0 0 0 .445 2.429a1.496 1.496 0 0 0 1.673-.31m5.204.052a1.5 1.5 0 1 0-2.122-2.118L6.072 23.94a1.5 1.5 0 1 0 2.122 2.118zm2.25 3a1.5 1.5 0 0 0-.945-2.555a1.489 1.489 0 0 0-1.173.44L9.323 25.94a1.5 1.5 0 0 0 .945 2.556c.455.036.874-.141 1.173-.44zm16.555-4.137l.627-.542l-6.913-10.85l-12.27 1.985a1.507 1.507 0 0 0-1.235 1.737c.658 2.695 6.003.693 8.355-.601z"" />
    <path fill=""#ffcc4d"" d=""M16.536 26.929c-.07.267-.207.498-.389.681l-1.004.996a1.494 1.494 0 0 1-1.437.396a1.5 1.5 0 0 1-.683-2.512l1.004-.996a1.494 1.494 0 0 1 1.437-.396a1.502 1.502 0 0 1 1.072 1.831M5.992 22.008l1.503-1.497a1.5 1.5 0 0 0-.444-2.429a1.497 1.497 0 0 0-1.674.31l-1.503 1.497a1.5 1.5 0 0 0 .445 2.429a1.496 1.496 0 0 0 1.673-.31m5.204.052a1.5 1.5 0 1 0-2.122-2.118L6.072 22.94a1.5 1.5 0 1 0 2.122 2.118zm2.25 3a1.5 1.5 0 0 0-.945-2.555a1.489 1.489 0 0 0-1.173.44L9.323 24.94a1.5 1.5 0 0 0 .945 2.556c.455.036.874-.141 1.173-.44zm21.557-7.456a1.45 1.45 0 0 0 .269-1.885l-.003-.005l-3.467-6.521a1.488 1.488 0 0 0-1.794-.6c-1.992.771-4.174 1.657-6.292.937l-1.098-.377c-1.948-.675-4.066-1.466-6-.294c-.695.409-1.738 1.133-2.411 1.58a6.873 6.873 0 0 1-2.762 1.076a1.502 1.502 0 0 0-1.235 1.737c.613 2.512 5.3.908 7.838-.369a.968.968 0 0 1 1.002.081l11.584 8.416z"" />
</svg>";

    const string BaseData = @"<svg width=""24"" height=""24"" viewBox=""0 0 64 64"">
    <path fill=""#d0d2d3"" d=""M47.563 57.54c0 2.805-2.543 5.076-5.679 5.076h-36.2c-3.136 0-5.679-2.271-5.679-5.076V11.796c0-2.803 2.543-5.076 5.679-5.076h36.21c3.136 0 5.679 2.273 5.679 5.076V57.54z"" />
    <path fill=""#eaeae9"" d=""M47.563 53.962c0 2.805-2.543 5.077-5.679 5.077h-36.2c-3.136 0-5.679-2.272-5.679-5.077V8.215c0-2.803 2.543-5.074 5.679-5.074h36.21c3.136 0 5.679 2.271 5.679 5.074v45.747z"" />
    <path fill=""#d0d2d3"" d=""M39.58 5.899c0 .337-1.688.612-3.769.612h-24.06c-2.085 0-3.772-.275-3.772-.612V.384c0-.339 1.687-.613 3.772-.613h24.06c2.08 0 3.769.274 3.769.613z"" />
    <path fill=""#34484c"" d=""M7.161 15.435h32.21c2.168 0 2.168-3 0-3H7.161c-2.163 0-2.163 3 0 3m0 6.09h32.21a1.504 1.504 0 0 0 0-2.998H7.161a1.504 1.504 0 0 0 0 2.998m0 6.226h32.21c2.168 0 2.168-3 0-3H7.161c-2.163 0-2.163 3 0 3m0 6.089h32.21c2.168 0 2.168-3 0-3H7.161c-2.163 0-2.163 3 0 3m0 22.016h14.823a1.504 1.504 0 0 0 0-2.999H7.161a1.504 1.504 0 0 0 0 2.999"" />
    <path fill=""#ee4237"" d=""m32.951 47.24l-10.188-9.344L51.992 6.02l10.188 9.349z"" />
    <path fill=""#da4241"" d=""m51.993 6.02l3.54 3.245l-29.228 31.88l-3.54-3.246zm7.349 6.747l2.846 2.613l-29.254 31.854l-2.846-2.614z"" />
    <path fill=""#e58732"" d=""m27.858 42.568l5.089 4.662l-6.593 2.08l-6.58 2.07l1.497-6.735l1.492-6.749z"" />
    <path fill=""#ea97bd"" d=""M63 14.469c-1.188 1.303-3.244 1.352-4.595.115l-5.309-4.871c-1.352-1.233-1.475-3.288-.286-4.588c1.191-1.299 3.25-1.352 4.596-.113l5.313 4.871c1.348 1.234 1.475 3.287.281 4.586"" />
    <path fill=""#bcbdbf"" d=""m51.162 6.926l10.19 9.345l-1.012 1.102L50.151 8.03z"" />
    <path fill=""#e7e6e6"" d=""m50.152 8.026l10.189 9.34l-1.01 1.103l-10.19-9.34zm11.198 8.245L51.16 6.926l1.01-1.1l10.19 9.342z"" />
    <path fill=""#f79420"" d=""m24.13 47.914l-.582-.544l2.756-6.23l3.806 3.489z"" />
    <path fill=""#424143"" d=""m21.347 44.31l-.072.335l-1.501 6.745l6.59-2.08l.322-.1z"" /></svg>";

    const string Fwater = @"<svg style=""width:24px;height:24px"" viewBox=""0 0 64 64"">
    <path fill=""#d0d2d3"" d=""M38.986 48.749c0 2.174-2.01 3.932-4.498 3.932H5.805c-2.486 0-4.499-1.758-4.499-3.932V13.314c0-2.17 2.01-3.931 4.499-3.931h28.683c2.488 0 4.498 1.761 4.498 3.931z"" />
    <path fill=""#fff"" d=""M36.672 45.722c0 1.903-1.768 3.448-3.945 3.448H7.567c-2.179 0-3.946-1.545-3.946-3.448v-31.08c0-1.903 1.767-3.448 3.946-3.448h25.16c2.178 0 3.945 1.545 3.945 3.448z"" />
    <path fill=""#34484c"" d=""M8.598 19.549h22.383c1.502 0 1.502-2.04 0-2.04H8.598c-1.505 0-1.505 2.04 0 2.04m0 4.136h22.383c1.502 0 1.502-2.036 0-2.036H8.598c-1.505-.001-1.505 2.036 0 2.036m0 4.229h22.383c1.502 0 1.502-2.036 0-2.036H8.598c-1.505 0-1.505 2.036 0 2.036m0 4.136h22.383c1.502 0 1.502-2.036 0-2.036H8.598c-1.505 0-1.505 2.036 0 2.036m0 14.96h10.299c1.501 0 1.501-2.036 0-2.036H8.598c-1.505 0-1.505 2.036 0 2.036"" />
    <path fill=""#d0d2d3"" d=""M50.951 39.37c0 2.175-2.02 3.932-4.497 3.932H17.765c-2.482 0-4.497-1.757-4.497-3.932V3.936c0-2.17 2.01-3.931 4.497-3.931h28.689c2.482 0 4.497 1.761 4.497 3.931z"" />
    <path fill=""#fff"" d=""M48.634 36.34c0 1.902-1.765 3.447-3.944 3.447H19.53c-2.177 0-3.943-1.545-3.943-3.447V5.26c0-1.903 1.766-3.444 3.943-3.444h25.16c2.18 0 3.944 1.541 3.944 3.444z"" />
    <path fill=""#34484c"" d=""M20.561 10.162H42.94c1.508 0 1.508-2.037 0-2.037H20.561c-1.502 0-1.502 2.037 0 2.037m0 4.139H42.94c1.508 0 1.508-2.036 0-2.036H20.561c-1.502 0-1.502 2.036 0 2.036m0 4.23H42.94c1.508 0 1.508-2.041 0-2.041H20.561c-1.502.001-1.502 2.041 0 2.041m0 4.136H42.94c1.508 0 1.508-2.036 0-2.036H20.561c-1.502-.001-1.502 2.036 0 2.036m0 14.957h10.301c1.501 0 1.501-2.036 0-2.036H20.561c-1.502 0-1.502 2.036 0 2.036"" />
    <path fill=""#d0d2d3"" d=""M62.913 60.16c0 2.174-2.01 3.931-4.497 3.931H29.729c-2.486 0-4.501-1.757-4.501-3.931V24.725c0-2.169 2.02-3.931 4.501-3.931h28.687c2.484 0 4.497 1.761 4.497 3.931z"" />
    <path fill=""#fff"" d=""M60.6 57.14c0 1.905-1.77 3.447-3.947 3.447h-25.16c-2.178 0-3.946-1.542-3.946-3.447V26.06c0-1.902 1.769-3.443 3.946-3.443h25.16c2.178 0 3.947 1.541 3.947 3.443z"" />
    <path fill=""#34484c"" d=""M32.522 30.965h22.383c1.505 0 1.505-2.041 0-2.041H32.522c-1.501 0-1.501 2.041 0 2.041m0 4.135h22.383c1.505 0 1.505-2.036 0-2.036H32.522c-1.501-.001-1.501 2.036 0 2.036m0 4.23h22.383c1.505 0 1.505-2.036 0-2.036H32.522c-1.501 0-1.501 2.036 0 2.036m0 4.14h22.383c1.505 0 1.505-2.041 0-2.041H32.522c-1.501 0-1.501 2.041 0 2.041m0 14.95h10.304c1.501 0 1.501-2.035 0-2.035H32.522c-1.501 0-1.501 2.035 0 2.035"" /></svg>";


    const string Finance = @"<svg  style=""width:24px;height:24px"" viewBox=""0 0 36 36"">
    <path fill=""#fdd888"" d=""M31.898 23.938C31.3 17.32 28 14 28 14l-6-8h-8l-6 8s-1.419 1.433-2.567 4.275C3.444 18.935 2 20.789 2 23a4.97 4.97 0 0 0 1.609 3.655A4.943 4.943 0 0 0 3 29c0 1.958 1.136 3.636 2.775 4.456C7.058 35.378 8.772 36 10 36h16c1.379 0 3.373-.779 4.678-3.31C32.609 31.999 34 30.17 34 28a4.988 4.988 0 0 0-2.102-4.062M18 6c.55 0 1.058-.158 1.5-.416c.443.258.951.416 1.5.416c1.657 0 4-2.344 4-4c0 0 0-2-2-2c-.788 0-1 1-2 1s-1-1-3-1s-2 1-3 1s-1.211-1-2-1c-2 0-2 2-2 2c0 1.656 2.344 4 4 4c.549 0 1.057-.158 1.5-.416c.443.258.951.416 1.5.416"" />
    <path fill=""#bf6952"" d=""M24 6a1 1 0 0 1-1 1H13a1 1 0 0 1 0-2h10a1 1 0 0 1 1 1"" />
    <path fill=""#67757f"" d=""M23.901 24.542c0-4.477-8.581-4.185-8.581-6.886c0-1.308 1.301-1.947 2.811-1.947c2.538 0 2.99 1.569 4.139 1.569c.813 0 1.205-.493 1.205-1.046c0-1.284-2.024-2.256-3.965-2.592V12.4c0-.773-.65-1.4-1.454-1.4c-.805 0-1.456.627-1.456 1.4v1.283c-2.116.463-3.937 1.875-3.937 4.176c0 4.299 8.579 4.125 8.579 7.145c0 1.047-1.178 2.093-3.111 2.093c-2.901 0-3.867-1.889-5.045-1.889c-.574 0-1.087.464-1.087 1.164c0 1.113 1.938 2.451 4.603 2.824l-.001.01v1.398c0 .772.652 1.4 1.456 1.4c.804 0 1.455-.628 1.455-1.4v-1.398c0-.017-.008-.03-.009-.045c2.398-.43 4.398-1.932 4.398-4.619"" /></svg>";

    const string Home = @"<svg style=""width:24px;height:24px"" viewBox=""0 0 36 36"">
    <path fill=""#ccd6dd"" d=""M31 2H5a3 3 0 0 0-3 3v26a3 3 0 0 0 3 3h26a3 3 0 0 0 3-3V5a3 3 0 0 0-3-3"" />
    <path fill=""#e1e8ed"" d=""M31 1H5a4 4 0 0 0-4 4v26a4 4 0 0 0 4 4h26a4 4 0 0 0 4-4V5a4 4 0 0 0-4-4m0 2c1.103 0 2 .897 2 2v4h-6V3zm-4 16h6v6h-6zm0-2v-6h6v6zM25 3v6h-6V3zm-6 8h6v6h-6zm0 8h6v6h-6zM17 3v6h-6V3zm-6 8h6v6h-6zm0 8h6v6h-6zM3 5c0-1.103.897-2 2-2h4v6H3zm0 6h6v6H3zm0 8h6v6H3zm2 14c-1.103 0-2-.897-2-2v-4h6v6zm6 0v-6h6v6zm8 0v-6h6v6zm12 0h-4v-6h6v4c0 1.103-.897 2-2 2"" />
    <path fill=""#dd2e44"" d=""M4.998 33a2 2 0 0 1-1.759-2.948l7-13a1.999 1.999 0 0 1 3.175-.466l6.076 6.076l9.738-18.59a2 2 0 0 1 3.543 1.857l-11 21a2.002 2.002 0 0 1-1.47 1.05a2.017 2.017 0 0 1-1.716-.563l-6.1-6.099l-5.724 10.631A2 2 0 0 1 4.998 33"" />
</svg>";

    const string client = @"<svg style=""width:24px;height:24px"" viewBox=""0 0 128 128"">
    <path fill=""#607d8b"" d=""M20.43 118.11V128h87.14v-9.89c0-15.17-21.39-22.87-43.24-22.84c-21.72.03-43.9 6.78-43.9 22.84"" />
    <path fill=""#d8d8d8"" d=""M64.44 95.27c-8.69 0-14.54 1.06-14.54 1.06L56.3 128h8.14z"" />
    <path fill=""#d8d8d8"" d=""M64 95.27c8.4 0 14.12 1.05 14.12 1.05L71.69 128H64z"" />
    <path fill=""#444"" d=""M66.82 114.09h-5.64l-1.77-7.5h9.12zm2 18.29h-9.64l2-18.29h5.64z"" />
    <path fill=""#e49800"" d=""M64 90.27h-9.08v9.59c0 4.34 3.22 6.58 7.78 6.58l3.67-.17c4.56 0 6.72-2.08 6.72-6.41v-9.59z"" />
    <path fill=""#e39400"" d=""M96.14 52.63s5.89 1.02 5.89 7.37c0 5.6-4.3 7.81-8.6 7.81V52.63zm-64.28 0s-5.89 1.02-5.89 7.37c0 5.6 4.3 7.81 8.6 7.81V52.63z"" />
    <path fill=""#fac01b"" d=""M64 11.06c-25.4 0-32.04 19.69-32.04 47.35c0 28.68 18.43 36.1 32.04 36.1c13.41 0 32.04-7.2 32.04-36.1c0-27.66-6.64-47.35-32.04-47.35"" />
    <path fill=""#444"" d=""M53.45 60.98c0 2.86-1.91 5.17-4.28 5.17c-2.36 0-4.29-2.32-4.29-5.17c0-2.86 1.92-5.18 4.29-5.18s4.28 2.31 4.28 5.18m21.1 0c0 2.86 1.91 5.17 4.29 5.17c2.36 0 4.28-2.32 4.28-5.17c0-2.86-1.92-5.18-4.28-5.18c-2.38 0-4.29 2.31-4.29 5.18"" />
    <path fill=""#513f35"" d=""M73.27 78.45H54.73c-1.06 0-1.52.71-.82 1.79c.98 1.5 4.64 4.38 10.09 4.38s9.11-2.88 10.09-4.38c.7-1.08.25-1.79-.82-1.79"" />
    <path fill=""#e39400"" d=""M68.48 70.57c-1.58.45-3.22.66-4.48.66c-1.26 0-2.9-.22-4.48-.66c-.67-.19-.94.45-.7.87c.5.88 2.52 2.66 5.18 2.66c2.66 0 4.68-1.78 5.18-2.66c.24-.43-.03-1.07-.7-.87"" />
    <path fill=""#6d4c41"" d=""M64.77 7.45s-.5-.02-.71-.01c-.21 0-.71.01-.71.01c-27.41.53-35.67 17.02-35.67 28.45c0 11.11.01 19.28.01 19.28s.44-.45 1.24-1.08c.86-.67 1.81-1.04 1.81-1.04l1.38 9.28l9.13-19.08c.06-.12.18-.19.31-.17l5.41.6c5.44.6 10.91.9 16.37.91v.01c.24 0 .47-.01.71-.01s.47.01.71.01v-.01c5.46-.02 10.93-.31 16.37-.91l5.41-.6c.13-.***********.17l9.13 19.08l1.38-9.28s.95.38 1.81 1.04c.81.63 1.24 1.08 1.24 1.08s.01-8.16.01-19.28c.01-11.43-8.25-27.92-35.65-28.45"" />
    <path fill=""#fff"" d=""M73.19 95.67c0 1.53-1.31 4.42-3.4 6.82c-2.12 2.44-5.65 3.95-5.65 3.95l10.39 11.08l3.72-21.19c0 .01-3.09-.47-5.06-.66"" />
    <path fill=""#455a64"" d=""M76.48 96.07L70.08 128h7.26l11.48-11.99l-4.75-6.6l7.12-4.71l-4.01-6.35s-5.05-1.54-10.7-2.28"" />
    <path fill=""#fff"" d=""M55.05 95.66c0 1.53 1.31 4.42 3.4 6.82c2.12 2.44 5.65 3.95 5.65 3.95l-10.38 11.09l-3.74-21.21c.01 0 3.04-.48 5.07-.65"" />
    <path fill=""#455a64"" d=""m51.76 96.05l6.4 31.95h-7.29l-11.44-12.04l4.75-6.6l-7.12-4.71l4.06-6.44s4.98-1.42 10.64-2.16"" />
</svg>";


    const string Emplyee = @"<svg style=""width:24px;height:24px"" viewBox=""0 0 36 36"">
    <path fill=""#ccd6dc"" d=""M30 26a4 4 0 0 1-4 4H4a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4h22a4 4 0 0 1 4 4z"" />
    <path fill=""#f5f8fa"" d=""M28 26a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h22a2 2 0 0 1 2 2z"" />
    <path fill=""#50a5e6"" d=""M6 26a1 1 0 0 1-1-1V13a1 1 0 1 1 2 0v12a1 1 0 0 1-1 1"" />
    <path fill=""#77b255"" d=""M10 26a1 1 0 0 1-1-1V8a1 1 0 1 1 2 0v17a1 1 0 0 1-1 1"" />
    <path fill=""#dd2f45"" d=""M14 26a1 1 0 0 1-1-1v-7a1 1 0 1 1 2 0v7a1 1 0 0 1-1 1"" />
    <path fill=""#226798"" d=""M36 36v-2c0-3.314-2.685-6-6-6H14a6 6 0 0 0-6 6v2z"" />
    <path fill=""#3a87c2"" d=""M16.667 36H20.2L17 28h-2l-1 6l3 1zm10.666 0H23.8l3.2-8h2l1 6l-3 1z"" />
    <path fill=""#ffdb5e"" d=""M17.64 28.101c1.744 1.268 2.857 2.032 4.37 2.032c1.512 0 2.606-.766 4.35-2.032V24.29h-8.72z"" />
    <path fill=""#f8c954"" d=""M17.632 25.973c1.216 1.374 2.724 1.746 4.364 1.746c1.639 0 3.147-.373 4.363-1.746v-3.491h-8.728v3.491z"" />
    <path fill=""#ffdb5e"" d=""M15.445 15.936c0 1.448-.734 2.622-1.639 2.622s-1.639-1.174-1.639-2.622s.734-2.623 1.639-2.623s1.639 1.174 1.639 2.623m16.388 0c0 1.448-.733 2.622-1.639 2.622c-.905 0-1.639-1.174-1.639-2.622s.733-2.623 1.639-2.623s1.639 1.174 1.639 2.623"" />
    <path fill=""#ffdb5e"" d=""M13.478 16.96c0-5.589 3.816-10.121 8.523-10.121s8.523 4.532 8.523 10.121s-3.816 10.121-8.523 10.121c-4.707-.001-8.523-4.532-8.523-10.121"" />
    <path fill=""#faaa35"" d=""M22 3.48c5.648 0 9.178 4.168 9.178 7.641s-.706 4.863-1.412 3.473l-1.412-2.778s-4.235 0-5.647-1.39c0 0 2.118 4.168-2.118 0c0 0 .706 2.779-3.53-.694c0 0-2.118 1.389-2.824 4.862c-.196.964-1.412 0-1.412-3.473C12.822 7.648 15.646 3.48 22 3.48"" />
    <path fill=""#c1694f"" d=""M22 23.802c-2.754 0-3.6-.705-3.741-.848a.655.655 0 0 1 .902-.95c.052.037.721.487 2.839.487c2.2 0 2.836-.485 2.842-.49a.638.638 0 0 1 .913.015a.669.669 0 0 1-.014.938c-.141.143-.987.848-3.741.848m.75-4.052h-1.5c-.413 0-.75-.337-.75-.75s.337-.75.75-.75h1.5c.413 0 .75.337.75.75s-.337.75-.75.75"" />
    <path fill=""#662213"" d=""M26 17c-.55 0-1-.45-1-1v-1c0-.55.45-1 1-1s1 .45 1 1v1c0 .55-.45 1-1 1m-8 0c-.55 0-1-.45-1-1v-1c0-.55.45-1 1-1s1 .45 1 1v1c0 .55-.45 1-1 1"" />
    <path fill=""#ccd6dc"" d=""M20.461 36H24l2-4l-3.99-1.867L18 32l2 4z"" />
    <path fill=""#9f1d22"" d=""M22.031 33.957c.744 0 1.246 1.025 1.562 2.043h.549c-.394-1.262-.841-2.438-.841-2.438s.375-.625.531-.906c.184-.33.453-1.57.453-1.57l-2.188-.963c0-.006-.16.006-.16.006l-.184.043l-.172.062c-.217.07.094.008.094.014l-1.973.838s.287 1.24.469 1.57c.156.281.529.906.529.906s-.43 1.106-.797 2.438h.559c.319-1.018.826-2.043 1.569-2.043"" />
    <path fill=""#dd2f45"" d=""M22.031 33.957c-.744 0-1.25 1.025-1.57 2.043h3.132c-.316-1.018-.818-2.043-1.562-2.043m-.027-3.144c.391-.023 1.543.771 1.422 1.25c-.461 1.826-.848 1.391-1.391 1.391c-.611 0-.963.473-1.391-1.312c-.091-.388.797-1.298 1.36-1.329"" />
    <path fill=""#f4f7f9"" d=""M26.719 26.75c-.567.566-4.709 3.383-4.709 3.383s2.127 1.242 4.084 3.533c.197.23 1.543-4.625 1.584-5.709c.011-.303-.688-1.478-.959-1.207m-9.418 0c.566.566 4.709 3.383 4.709 3.383s-2.127 1.242-4.084 3.533c-.197.23-1.543-4.625-1.584-5.709c-.012-.303.687-1.478.959-1.207"" />
</svg>";


    const string ClientJudge = @"<svg style=""width:24px;height:24px"" viewBox=""0 0 128 128"">
    <path fill=""#a1887f"" d=""M103.31 110.19H24.78c-6.64 0-12.08-5.44-12.08-12.08l-2.42-49.49c0-6.64 5.44-12.08 12.08-12.08h83.36c6.64 0 12.08 5.44 12.08 12.08l-2.42 49.49c.01 6.64-5.43 12.08-12.07 12.08"" />
    <path fill=""#6d4c41"" d=""M-4.11 119.67H131.9V132H-4.11z"" />
    <path fill=""#231815"" d=""M115.43 109.33c-4.72-18.14-31.19-21.45-51.31-21.66h-.16c-20.12.22-47.64 3.52-52.37 21.66l-2.83 11.28h109.5z"" />
    <path fill=""#e49800"" d=""M63.99 82.91h-8.62v9.1c0 4.12 3.51 7.45 7.84 7.45h1.57c4.33 0 7.84-3.34 7.84-7.45v-9.1z"" />
    <path fill=""#513f35"" d=""M72.8 71.39H55.28c-1 0-1.44.67-.77 1.69c.93 1.42 4.38 4.14 9.53 4.14c5.15 0 8.6-2.72 9.53-4.14c.66-1.01.23-1.69-.77-1.69"" />
    <path fill=""#d8d8d8"" d=""m72.07 115.57l-16.34.37V97.43h16.34z"" />
    <path fill=""#444"" d=""M54.08 54.9c0 2.7-1.8 4.88-4.04 4.88c-2.23 0-4.05-2.19-4.05-4.88c0-2.7 1.82-4.89 4.05-4.89c2.23 0 4.04 2.18 4.04 4.89m19.92 0c0 2.7 1.81 4.88 4.05 4.88c2.23 0 4.05-2.19 4.05-4.88c0-2.7-1.82-4.89-4.05-4.89c-2.24 0-4.05 2.18-4.05 4.89"" />
    <path fill=""#c30d23"" d=""M66.43 103.8h-4.78l-1.5-6.37h7.74zm1.7 15.51h-8.17l1.69-15.51h4.78z"" />
    <path fill=""#fff"" d=""m51.73 88.22l3.12 21.9l9.13-12.68l-8.65-9.48zm24.63 0l-3.12 21.9l-9.14-12.68l8.66-9.48z"" />
    <path fill=""#e49500"" d=""M68.22 63.95c-1.49.42-3.04.63-4.23.63c-1.19 0-2.74-.21-4.23-.63c-.64-.18-.89.42-.66.82c.47.83 2.38 2.51 4.89 2.51s4.42-1.68 4.89-2.51c.23-.4-.02-1-.66-.82"" />
    <path fill=""#231815"" d=""M47.15 89.12c.47-.32 5.44-1 5.44-1l11.39 31.54l-21.43-.08z"" />
    <path fill=""#231815"" d=""M81.46 89.12c-.47-.32-5.44-1-5.44-1l-12.05 31.54l22.32-.08z"" />
    <path fill=""#e39400"" d=""M94.29 46.67s5.53.96 5.53 6.91c0 5.25-4.03 7.32-8.06 7.32V46.67zm-60.26 0s-5.53.96-5.53 6.91c0 5.25 4.03 7.32 8.06 7.32V46.67z"" />
    <path fill=""#fac01b"" d=""M64.16 7.7c-23.81 0-30.04 18.46-30.04 44.39c0 26.89 17.28 33.84 30.04 33.84c12.57 0 30.04-6.75 30.04-33.84c.01-25.93-6.22-44.39-30.04-44.39"" />
    <path fill=""#444"" d=""M54.27 54.49c0 2.68-1.79 4.85-4.01 4.85c-2.22 0-4.02-2.17-4.02-4.85c0-2.68 1.8-4.85 4.02-4.85c2.22 0 4.01 2.17 4.01 4.85m19.78 0c0 2.68 1.79 4.85 4.02 4.85c2.21 0 4.02-2.17 4.02-4.85c0-2.68-1.8-4.85-4.02-4.85c-2.22 0-4.02 2.17-4.02 4.85"" />
    <path fill=""#513f35"" d=""M72.86 70.87H55.48c-1 0-1.42.67-.77 1.67c.92 1.41 4.35 4.11 9.46 4.11c5.11 0 8.54-2.7 9.46-4.11c.65-1 .23-1.67-.77-1.67"" />
    <path fill=""#e39400"" d=""M68.36 63.48c-1.48.42-3.01.62-4.2.62c-1.19 0-2.72-.2-4.2-.62c-.63-.18-.88.42-.65.82c.47.83 2.36 2.49 4.85 2.49c2.5 0 4.38-1.67 4.85-2.49c.24-.4-.01-1-.65-.82"" />
    <path fill=""#6d4c41"" d=""M64.88 4.31s-.46-.01-.67-.01c-.2 0-.67.01-.67.01c-25.69.5-33.44 15.95-33.44 26.67c0 10.42.01 18.07.01 18.07s.41-.42 1.16-1.01c.8-.63 1.7-.98 1.7-.98l1.3 8.7l8.56-17.89c.05-.11.17-.18.29-.16l5.07.56c5.1.57 10.22.84 15.35.86v.01c.22 0 .44-.01.67-.01c.22 0 .44.01.67.01v-.01c5.12-.02 10.25-.29 15.35-.86l5.07-.56c.12-.01.24.05.29.16l8.56 17.89l1.3-8.7s.89.35 1.7.98c.76.59 1.16 1.01 1.16 1.01s.01-7.65.01-18.07c0-10.71-7.74-26.17-33.44-26.67"" />
</svg>";

    const string judge = @"<svg style=""width:24px;height:24px"" viewBox=""0 0 36 36"">
    <path fill=""#292f33"" d=""M36 36v-2a6 6 0 0 0-6-6H14a6 6 0 0 0-6 6v2z"" />
    <path fill=""#e1e8ed"" d=""M17 28h9.929v8H17z"" />
    <path fill=""#ffdc5d"" d=""M17.64 28.101c1.744 1.268 2.848 1.963 4.36 1.963c1.512 0 2.615-.696 4.359-1.963V24.29h-8.72z"" />
    <path fill=""#f9ca55"" d=""M17.632 25.973c1.216 1.374 2.724 1.746 4.364 1.746c1.639 0 3.146-.373 4.363-1.746v-3.491h-8.728v3.491z"" />
    <path fill=""#6d6e71"" d=""M25.597 3.516c-1.925-.623-6.455-.453-7.588 1.019c-2.944.057-6.398 2.718-6.851 6.228c-.448 3.475.551 5.088.906 7.701c.403 2.96 2.067 3.907 3.397 4.303c1.914 2.529 3.949 2.421 7.366 2.421c6.672 0 9.85-4.464 10.131-12.047c.17-4.585-2.521-8.059-7.361-9.625"" />
    <path fill=""#ffdc5d"" d=""M29.413 13.466c-.646-.894-1.472-1.614-3.284-1.868c.68.311 1.331 1.387 1.416 1.982c.085.595.17 1.076-.368.481c-2.155-2.382-4.502-1.444-6.827-2.899c-1.624-1.016-2.119-2.141-2.119-2.141s-.198 1.5-2.661 3.029c-.714.443-1.566 1.43-2.038 2.888c-.34 1.048-.234 1.982-.234 3.578c0 4.66 3.841 8.578 8.578 8.578s8.578-3.953 8.578-8.578c-.001-2.898-.305-4.031-1.041-5.05"" />
    <path fill=""#c1694f"" d=""M22.827 20.9h-1.906a.477.477 0 1 1 0-.954h1.906a.477.477 0 1 1 0 .954"" />
    <path fill=""#662113"" d=""M18.062 17.564a.953.953 0 0 1-.953-.953v-.953a.953.953 0 0 1 1.906 0v.953a.953.953 0 0 1-.953.953m7.625 0a.953.953 0 0 1-.953-.953v-.953a.953.953 0 0 1 1.906 0v.953a.953.953 0 0 1-.953.953"" />
    <path fill=""#c1694f"" d=""M22 24.88c-2.754 0-3.6-.705-3.741-.848a.655.655 0 0 1 .902-.95c.052.037.721.487 2.839.487c2.2 0 2.836-.485 2.842-.49a.638.638 0 0 1 .913.015a.669.669 0 0 1-.014.938c-.141.142-.987.848-3.741.848"" />
    <path fill=""#99aab5"" d=""M22.001 30.878s3.381 2.494 4.093 2.778c.156.063-.366-3.656-.366-3.656h-7.454s-.523 3.781-.231 3.625c.638-.343 3.958-2.747 3.958-2.747"" />
    <path fill=""#f5f8fa"" d=""M17.292 26.625c.566.566 4.709 3.383 4.709 3.383s-2.126 1.242-4.084 3.534c-.197.23-1.542-4.625-1.583-5.709c-.012-.303.687-1.479.958-1.208m9.438 0c-.566.566-4.709 3.383-4.709 3.383s2.126 1.242 4.084 3.534c.196.23 1.542-4.625 1.583-5.709c.012-.303-.687-1.479-.958-1.208"" />
    <path fill=""#292f33"" d=""M27.62 28s-1.542 5.463-5.62 7.411C17.922 33.463 16.38 28 16.38 28H15v8h14v-8z"" />
    <path fill=""#66757f"" d=""M12.083 33.341c.251 0 .401 2.659.401 2.659h-.956s.193-2.659.555-2.659m3 0c.251 0 .401 2.659.401 2.659h-.956s.193-2.659.555-2.659m16.846 0c-.251 0-.401 2.659-.401 2.659h.956c-.001 0-.194-2.659-.555-2.659m-3 0c-.251 0-.401 2.659-.401 2.659h.956c-.001 0-.194-2.659-.555-2.659"" />
    <path fill=""#c1694f"" d=""M7.854 23.665c.468 1.24.178 2.498-.649 2.812c-.826.311-1.876-.441-2.345-1.681c-.468-1.239-.178-2.498.649-2.811c.826-.312 1.876.44 2.345 1.68"" />
    <path fill=""#662113"" d=""m9.092 36l-3.479-9.208l.25-.094a2.14 2.14 0 0 0 1.242-2.75a2.14 2.14 0 0 0-2.75-1.241l-1.995.754a2.137 2.137 0 0 0-1.242 2.749a2.138 2.138 0 0 0 2.749 1.241l.25-.094L7.382 36z"" />
    <path fill=""#c1694f"" d=""M3.363 25.361c.468 1.24.178 2.498-.649 2.812c-.826.312-1.876-.44-2.345-1.681c-.468-1.239-.178-2.498.649-2.811c.827-.312 1.877.44 2.345 1.68"" />
    <path fill=""#ffdc5d"" d=""m11.318 36l-.466-1.588a2.353 2.353 0 0 0-2.911-1.592l-1.255.368A2.35 2.35 0 0 0 5.076 36z"" />
</svg>";

    const string Receipt = @"<svg style=""width:24px;height:24px"" viewBox=""0 0 64 64"">
    <g fill=""#f4ae7f"">
    <path d=""M52.11 58.32c0 3.056-2.289 5.531-5.116 5.531H14.379c-2.824 0-5.114-2.476-5.114-5.531V8.447c0-3.059 2.291-5.534 5.114-5.534h32.615c2.827 0 5.116 2.475 5.116 5.534z"" />
    <path d=""M30.899 10.509c0 .581-1.158 1.051-2.58 1.051H11.848c-1.426 0-2.582-.47-2.582-1.051v-9.46C9.266.47 10.421 0 11.848 0h16.471c1.422 0 2.58.47 2.58 1.049z"" />
    </g>
    <path fill=""#d0d2d3"" d=""M54.662 56c0 2.593-2.312 4.69-5.167 4.69H16.536c-2.851 0-5.167-2.098-5.167-4.69V13.73c0-2.591 2.316-4.69 5.167-4.69h32.959c2.855 0 5.167 2.1 5.167 4.69z"" />
    <path fill=""#fff"" d=""M54.662 52.694c0 2.593-2.312 4.69-5.167 4.69H16.536c-2.851 0-5.167-2.098-5.167-4.69v-42.27c0-2.591 2.316-4.688 5.167-4.688h32.959c2.855 0 5.167 2.098 5.167 4.688z"" />
    <path fill=""#d0d2d3"" d=""M43.1 8.28c0 .312-1.538.566-3.43.566h-21.9c-1.896 0-3.434-.254-3.434-.566V3.185c0-.315 1.538-.566 3.434-.566h21.9c1.892 0 3.43.251 3.43.566z"" />
    <path fill=""#35494d"" d=""M20.07 18.03h28.562c1.922 0 1.922-2.7 0-2.7H20.07c-1.915 0-1.915 2.7 0 2.7m0 5.485h28.562c1.922 0 1.922-2.698 0-2.698H20.07c-1.915 0-1.915 2.698 0 2.698m0 5.605h28.562c1.922 0 1.922-2.7 0-2.7H20.07c-1.915 0-1.915 2.7 0 2.7m0 5.48h28.562c1.922 0 1.922-2.698 0-2.698H20.07c-1.915 0-1.915 2.698 0 2.698m0 10.58h13.148c1.916 0 1.916-2.699 0-2.699H20.07c-1.915-.001-1.915 2.699 0 2.699"" />
</svg>";

    const string Consumed = @"<svg style=""width:24px;height:24px"" viewBox=""0 0 128 128"">
    <path fill=""#006ca2"" d=""M16.28 98.57C12.28 96.75 9 91.66 9 87.26v-34c0-4.4 3.28-9.49 7.28-11.31l40.43-18.38c4.01-1.82 10.56-1.82 14.57 0l40.43 18.38c4.01 1.82 7.28 6.91 7.28 11.31v34c0 4.4-3.28 9.49-7.28 11.31l-40.43 18.38c-4.01 1.82-10.56 1.82-14.57 0z"" />
    <path fill=""#40c0e7"" d=""M71.28 66.95c-4.01 1.82-10.56 1.82-14.57 0L16.28 48.57c-4.01-1.82-4.01-4.8 0-6.62l40.43-18.38c4.01-1.82 10.56-1.82 14.57 0l40.43 18.38c4.01 1.82 4.01 4.8 0 6.62z"" />
    <path fill=""#006ca2"" d=""M82.69 55.93c-1 .46-2.64.46-3.64 0L40.52 38.42c-1-.46-1-1.2 0-1.66l4.79-2.18c1-.46 2.64-.46 3.64 0L87.48 52.1c1 .46 1 1.2 0 1.66z"" />
    <path fill=""#fbc21a"" d=""M79.34 53.73c0 .01-.01.01-.01.01L46.01 38.6c-.01-.01-.01-.01-.01-.02L57.56 7.75c0-.01.01-.01.01-.01l33.32 15.14c.01 0 .01.01.01.01z"" />
    <path fill=""none"" stroke=""#875a3b"" stroke-miterlimit=""10"" stroke-width=""2"" d=""M79.33 53.74L90.9 22.89"" />
    <path fill=""#006ca2"" d=""M163.15 98.32c-4.01-1.82-7.28-6.91-7.28-11.31v-34c0-4.4 3.28-9.49 7.28-11.31l40.43-18.38c4.01-1.82 10.56-1.82 14.57 0l40.43 18.38c4.01 1.82 7.28 6.91 7.28 11.31v34c0 4.4-3.28 9.49-7.28 11.31l-40.43 18.38c-4.01 1.82-10.56 1.82-14.57 0z"" />
    <path fill=""#40c0e7"" d=""M218.15 66.7c-4.01 1.82-10.56 1.82-14.57 0l-40.43-18.38c-4.01-1.82-4.01-4.8 0-6.62l40.43-18.38c4.01-1.82 10.56-1.82 14.57 0l40.43 18.38c4.01 1.82 4.01 4.8 0 6.62z"" />
    <path fill=""#006ca2"" d=""M228.82 55.19c-1 .46-2.64.46-3.64 0l-36.69-16.68c-1-.46-1-1.2 0-1.66l4.43-2.01c1-.46 2.64-.46 3.64 0l36.69 16.68c1 .46 1 1.2 0 1.66z"" />
    <path fill=""#006ca2"" d=""M229.55 55.69c-1 .46-2.64.46-3.64 0l-38.53-17.51c-1-.46-1-1.2 0-1.66l4.79-2.18c1-.46 2.64-.46 3.64 0l38.53 17.51c1 .46 1 1.2 0 1.66z"" />
    <path fill=""#fbc21a"" d=""m239.93 23.14l-9.08 13.03c-4.97 7.13-4.66 17.33-4.66 17.33l-33.32-15.15s-.32-10.2 4.66-17.33l9.08-13.03z"" />
    <path fill=""none"" stroke=""#875a3b"" stroke-miterlimit=""10"" stroke-width=""2"" d=""m239.93 23.14l-9.08 13.03c-4.97 7.13-4.66 17.33-4.66 17.33"" />
    <g fill=""none"" stroke=""#875a3b"" stroke-linecap=""round"" stroke-linejoin=""round"" stroke-miterlimit=""10"" stroke-width=""4"">
    <path d=""M203 34.54c2.99-2.98 20.09-7.79 23.53-7.6"" />
    <path d=""M209.87 19.36c.07 2.81 5.37 16.89 9.79 22.76"" />
    </g>
</svg>";

    const string Sells = @"<svg  style=""width:24px;height:24px"" viewBox=""0 0 36 36"">
    <path fill=""#e1e8ed"" d=""M36 18c0 9.941-8.059 18-18 18S0 27.941 0 18S8.059 0 18 0s18 8.059 18 18"" />
    <path fill=""#ccd6dd"" d=""M30 18c0 6.628-5.372 12-12 12S6 24.628 6 18S11.372 6 18 6s12 5.372 12 12"" />
    <path fill=""#e1e8ed"" d=""M29 18c0 6.075-4.925 11-11 11c-6.076 0-11-4.925-11-11c0-6.076 4.925-11 11-11s11 4.924 11 11"" />
    <g fill=""#99aab5"">
    <circle cx=""1"" cy=""1"" r=""1"" />
    <path d=""M0 1h2v7H0z"" />
    <circle cx=""5"" cy=""1"" r=""1"" />
    <path d=""M4 1h2v7H4z"" />
    <circle cx=""9"" cy=""1"" r=""1"" />
    <path d=""M8 1h2v7H8zM3 14h4v20H3z"" />
    <circle cx=""5"" cy=""34"" r=""2"" />
    <path d=""M8 8a1 1 0 0 1-2 0H4a1 1 0 0 1-2 0H0c0 3.866 1 7 5 7s5-3.134 5-7z"" />
    <circle cx=""1"" cy=""8"" r=""1"" />
    <circle cx=""5"" cy=""8"" r=""1"" />
    <circle cx=""9"" cy=""8"" r=""1"" />
    </g>
    <g fill=""#99aab5"">
    <path d=""M30 14h4v20h-4z"" />
    <circle cx=""32"" cy=""34"" r=""2"" />
    <path d=""M32 0c1 0 2 1 2 3v16s-7 2-7-8c0-6 3-11 5-11"" />
    </g>
</svg>";

    const string Expenses = @"<svg  style=""width:24px;height:24px"" viewBox=""0 0 48 48"">
    <path fill=""#6dd627"" stroke=""#45413c"" stroke-linecap=""round"" stroke-linejoin=""round"" d=""m30.82 30.66l-3.24 2.75a19.87 19.87 0 0 1-7.49 3.24h0a19.92 19.92 0 0 0-7.48 3.24l-3.22 2.27a.94.94 0 0 1-1.33-.24l-6.91-10a.9.9 0 0 1-.15-.46l-.48-6l30.2-1.37l.46 5.8a.94.94 0 0 1-.36.77"" />
    <path fill=""#45413c"" d=""M6.38 45.5a10.97 1.5 0 1 0 21.94 0a10.97 1.5 0 1 0-21.94 0"" opacity=""0.15"" />
    <path fill=""#e5feff"" d=""M27.71 37.79a.74.74 0 0 1 .12-1.31a21.53 21.53 0 0 1 10.66-1.59c6.06.87 10.27 4 8.67 6.66c-1.39 2.27-5.38.38-5.38.38s1.38 2.66-.88 3.79s-5.51-3.14-5.51-3.14s-.5 2.56-2.46 2c-2.61-.71-1.75-3-2.59-4.5a8.94 8.94 0 0 0-2.63-2.29"" />
    <path fill=""#fff"" d=""M27.71 37.79c.28.19.6.4.91.63a21.06 21.06 0 0 1 9.71-1.23c4.41.64 7.84 2.49 8.76 4.46a.86.86 0 0 0 .07-.1c1.6-2.63-2.61-5.79-8.67-6.66a21.53 21.53 0 0 0-10.66 1.59a.74.74 0 0 0-.12 1.31"" />
    <path fill=""none"" stroke=""#45413c"" stroke-linecap=""round"" stroke-linejoin=""round"" d=""M27.71 37.79a.74.74 0 0 1 .12-1.31a21.53 21.53 0 0 1 10.66-1.59c6.06.87 10.27 4 8.67 6.66c-1.39 2.27-5.38.38-5.38.38s1.38 2.66-.88 3.79s-5.51-3.14-5.51-3.14s-.5 2.56-2.46 2c-2.61-.71-1.75-3-2.59-4.5a8.94 8.94 0 0 0-2.63-2.29"" />
    <path fill=""#ffaa54"" stroke=""#45413c"" stroke-linecap=""round"" stroke-linejoin=""round"" d=""M41.78 41.93A10.57 10.57 0 0 0 39 38.55m-3.61 4.03a5 5 0 0 0-1-3.61"" />
    <path fill=""#e5feff"" d=""M11.19 16.6a.59.59 0 0 0 1-.46a17.16 17.16 0 0 0-1.75-8.41C8.05 3.44 4.52 1.16 3 3.09C1.69 4.75 4.21 7.21 4.21 7.21s-2.38-.29-2.6 1.71s3.88 3.25 3.88 3.25s-1.78 1.08-.85 2.4c1.25 1.77 2.77.47 4.08.7a6.88 6.88 0 0 1 2.47 1.33"" />
    <path fill=""#fff"" d=""M10.19 10.54a15.46 15.46 0 0 1 1.68 6.06a.55.55 0 0 0 .27-.46a17.16 17.16 0 0 0-1.75-8.41C8.05 3.44 4.52 1.16 3 3.09a2.28 2.28 0 0 0 0 2.56c1.68-1.44 5 .82 7.19 4.89"" />
    <path fill=""none"" stroke=""#45413c"" stroke-linecap=""round"" stroke-linejoin=""round"" d=""M11.19 16.6a.59.59 0 0 0 1-.46a17.16 17.16 0 0 0-1.75-8.41C8.05 3.44 4.52 1.16 3 3.09C1.69 4.75 4.21 7.21 4.21 7.21s-2.38-.29-2.6 1.71s3.88 3.25 3.88 3.25s-1.78 1.08-.85 2.4c1.25 1.77 2.77.47 4.08.7a6.88 6.88 0 0 1 2.47 1.33"" />
    <path fill=""#ffaa54"" stroke=""#45413c"" stroke-linecap=""round"" stroke-linejoin=""round"" d=""M4.21 7.21A8.12 8.12 0 0 1 7.48 8.4m-1.99 3.77a4 4 0 0 1 2.94-.25"" />
    <path fill=""#6dd627"" stroke=""#45413c"" stroke-linecap=""round"" stroke-linejoin=""round"" d=""M11.61 19.05a19.76 19.76 0 0 0-7.48 3.24L.9 24.56a1 1 0 0 0-.23 1.33l6.88 9.75a1 1 0 0 0 1.33.23l3.23-2.28a20.08 20.08 0 0 1 7.48-3.24h0a19.87 19.87 0 0 0 7.49-3.24l3.22-2.27a1 1 0 0 0 .23-1.33l-6.88-9.75a1 1 0 0 0-1.33-.23l-3.22 2.28a20 20 0 0 1-7.49 3.24"" />
    <path fill=""#e4ffd1"" stroke=""#45413c"" stroke-linecap=""round"" stroke-linejoin=""round"" d=""m4 26.47l4.13 5.84a1 1 0 0 0 1.33.23l1.66-1.17a19.8 19.8 0 0 1 7.49-3.24h0a19.92 19.92 0 0 0 7.48-3.24l.89-.62a1 1 0 0 0 .22-1.33l-4.12-5.85a1 1 0 0 0-1.33-.23L20.08 18a19.76 19.76 0 0 1-7.48 3.24h0a20.19 20.19 0 0 0-7.49 3.24l-.88.63A1 1 0 0 0 4 26.47"" />
    <path fill=""#9ceb60"" stroke=""#45413c"" stroke-linecap=""round"" stroke-linejoin=""round"" d=""M18.3 23.71c.35 1-.57 2.23-2.06 2.75s-3 .15-3.34-.84s.57-2.23 2.07-2.76s3.03-.14 3.33.85"" />
    <path fill=""none"" stroke=""#45413c"" stroke-linecap=""round"" stroke-linejoin=""round"" d=""m8.21 36.06l.5 6.26M31 28.3l-3.33 2.82a20.78 20.78 0 0 1-7.82 3.39h0a20.56 20.56 0 0 0-7.81 3.38l-3.49 2.49M30.87 26.2L27.54 29a20.72 20.72 0 0 1-7.81 3.38h0a21 21 0 0 0-7.82 3.38l-3.52 2.51"" />
    <path fill=""#ffaa54"" stroke=""#45413c"" stroke-linecap=""round"" stroke-linejoin=""round"" d=""M8.5 39.71L.75 28.5"" />
</svg>";

    const string items = @"<svg style=""width:24px;height:24px"" viewBox=""0 0 48 48"">

    <path fill=""#45413c"" d=""M3.88 45.11a20.12 1.89 0 1 0 40.24 0a20.12 1.89 0 1 0-40.24 0"" opacity=""0.15"" />

    <path fill=""#b89558"" d=""M23.37 7.15c-9.53 0-16 2.83-19.49 6.92L16.46 31l7.54-.34V45.5l16.92-4a2.52 2.52 0 0 0 1.94-2.45V25.9a8.92 8.92 0 0 1 1.38-4.58a8.05 8.05 0 0 0 1.13-4.48c-.37-7.66-14.02-9.69-22-9.69"" />

    <path fill=""#debb7e"" d=""M43.87 22c.11-.22.24-.45.37-.67a8.05 8.05 0 0 0 1.13-4.48c-.35-7.66-14-9.69-22-9.69c-9.53 0-16 2.83-19.49 6.92l8.73 11.77h1s9-8.49 18.65-9.85c6.68-1 11.93-.36 11.61 6"" />

    <path fill=""none"" stroke=""#45413c"" stroke-linecap=""round"" stroke-linejoin=""round"" d=""M23.37 7.15c-9.53 0-16 2.83-19.49 6.92L16.46 31l7.54-.34V45.5l16.92-4a2.52 2.52 0 0 0 1.94-2.45V25.9a8.92 8.92 0 0 1 1.38-4.58a8.05 8.05 0 0 0 1.13-4.48c-.37-7.66-14.02-9.69-22-9.69"" />

    <path fill=""#f7e5c6"" d=""M26.51 22.85A8.08 8.08 0 0 0 20.28 15L8.8 12.31a4.86 4.86 0 0 0-6.17 4.53a8.05 8.05 0 0 0 1.13 4.48a8.92 8.92 0 0 1 1.38 4.58v13.2a2.51 2.51 0 0 0 1.94 2.45L24 45.5V30a5.94 5.94 0 0 1 1.31-3.88a5 5 0 0 0 1.2-3.27"" />

    <path fill=""#fff5e3"" d=""M3.47 19.35a4.58 4.58 0 0 1 5.73-4.52l10.68 2.68a8 8 0 0 1 5.8 7.86a2.3 2.3 0 0 1 0 .26a4.87 4.87 0 0 0 .86-2.78A8.08 8.08 0 0 0 20.28 15L8.8 12.31a4.86 4.86 0 0 0-6.17 4.53a8 8 0 0 0 .94 4.09a8.23 8.23 0 0 1-.1-1.58"" />

    <path fill=""none"" stroke=""#45413c"" stroke-linecap=""round"" stroke-linejoin=""round"" d=""M26.51 22.85A8.08 8.08 0 0 0 20.28 15L8.8 12.31a4.86 4.86 0 0 0-6.17 4.53a8.05 8.05 0 0 0 1.13 4.48a8.92 8.92 0 0 1 1.38 4.58v13.2a2.51 2.51 0 0 0 1.94 2.45L24 45.5V30a5.94 5.94 0 0 1 1.31-3.88a5 5 0 0 0 1.2-3.27"" />
</svg>";



    const string Products = @"<svg style=""width:24px;height:24px"" viewBox=""0 0 36 36"">
    <path fill=""#d99e82"" d=""M18 20.411c-9.371 0-16.967-.225-16.967 6.427C1.033 33.487 8.629 35 18 35c9.371 0 16.967-1.513 16.967-8.162c0-6.651-7.596-6.427-16.967-6.427"" />
    <path fill=""#662113"" d=""M34.47 20.916S26.251 19.932 18 19.89c-8.251.042-16.47 1.026-16.47 1.026C.717 27.39 7.467 30.057 18 30.057s17.283-2.667 16.47-9.141"" />
    <path fill=""#ffcc4d"" d=""m33.886 18.328l-31.855.646c-1.1 0-2.021 2.229-.854 2.812c8.708 2.708 15.708 5.448 15.708 5.448c.962.532 1.287.534 2.25.003c0 0 9.666-3.868 15.875-5.493c.881-.23-.025-3.416-1.124-3.416"" />
    <path fill=""#77b255"" d=""M34.725 18.412c-1.9-1.751-1.79-.819-3.246-1.23c-.553-.156-4.51-5.271-13.529-5.271h-.02c-9.019 0-12.976 5.115-13.529 5.271c-1.456.411-1.346-.521-3.246 1.23c-.872.804-1.108 1.222-.188 1.43c1.386.313 1.26 1.152 2.253 1.444c1.202.353 1.696-.292 3.634-.028c1.653.225 1.761 2.369 3.429 2.369s1.668-.8 3.335-.8s2.653 2.146 4.321 2.146s2.653-2.146 4.321-2.146c1.668 0 1.668.8 3.335.8c1.668 0 1.776-2.144 3.429-2.369c1.938-.263 2.433.381 3.634.028c.993-.292.867-1.13 2.253-1.444c.922-.207.687-.626-.186-1.43"" />
    <path fill=""#dd2e44"" d=""M34.077 16.52c0 2.984-7.198 4.393-16.077 4.393S1.923 19.504 1.923 16.52c0-5.403.966-5.403 16.077-5.403s16.077.001 16.077 5.403"" />
    <path fill=""#d99e82"" d=""M18 .524C8.629.524 1.033 4.915 1.033 11.566c0 6.125 7.596 6.375 16.967 6.375s16.967-.25 16.967-6.375C34.967 4.914 27.371.524 18 .524"" />
    <path fill=""#ffe8b6"" d=""M10.784 3.695a1.069 1.069 0 1 0-1.152 1.802c.498.319 1.76.557 2.079.059c.318-.498-.429-1.543-.927-1.861m9.734-1.035c-.562.182-1.549 1.006-1.366 1.568c.183.562 1.464.648 2.026.466a1.069 1.069 0 0 0-.66-2.034m10.909 7.035c-.452-.38-1.585.225-1.966.677a1.07 1.07 0 0 0 1.638 1.376c.381-.453.781-1.673.328-2.053m-3.643-5a1.07 1.07 0 0 0-1.152 1.803c.498.319 1.76.557 2.078.059c.319-.499-.428-1.544-.926-1.862m-15 7a1.07 1.07 0 0 0-1.478.326a1.068 1.068 0 0 0 .326 1.476c.498.319 1.76.558 2.078.059c.319-.498-.428-1.543-.926-1.861m3.046-4.808c-.336.486-.62 1.739-.133 2.075c.486.336 1.557-.374 1.893-.86a1.07 1.07 0 0 0-1.76-1.215m7.954 4.808a1.07 1.07 0 0 0-1.478.326a1.068 1.068 0 0 0 .326 1.476c.498.319 1.76.558 2.078.059c.319-.498-.428-1.543-.926-1.861M4.948 7.808c-.394.441-.833 1.648-.392 2.042c.439.394 1.591-.174 1.985-.615a1.07 1.07 0 1 0-1.593-1.427"" />
</svg>";



    const string Category = @"<svg style=""width:24px;height:24px"" viewBox=""0 0 128 128"">
    <path fill=""#fff"" d=""M109.98 98.53c-.28-6.25-2.39-10.31-6.21-13.83c-9.54-8.81-21.04-17.22-31.63-24.75c-4.91-3.49-10.37-8.1-16.06-10.23C48.06 46.71 39 47.78 30.41 49c-3.75.53-7.52 1.13-11.22 2c-4.28 1.02-9.36 2.23-12.99 4.8c-3.26 2.31-2.86 5.2-1.26 7.88c.24 1.23.01 2.4.02 3.65c.02 2.36-.25 4.72-.82 7c-.36 1.41-.85 2.77-1.31 4.15C1 83.82 6.05 88.3 9.79 91.39c2.1 1.73 4.34 3.29 6.45 5.01c8.2 6.67 16.6 12.7 24.99 19.05c9.83 7.45 13.58 10.04 25.85 5.85c3.31-1.13 6.7-2.03 10.09-2.9c4.8-1.23 11.18-2.4 15.85-4.04c4.73-1.66 10.84-3.86 14.31-7.65c1.74-1.89 2.48-4.6 2.49-7.12c.12-.31.18-.65.16-1.06"" />
    <path fill=""#bdcf46"" d=""M13.37 58.22c4.72-1.99 23.27-4.87 27.36-5.34c3.47-.4 7.84-.96 11.28-.45c1.39.23 5.85 2.56 10.48 5.71c9.47 6.44 34.45 25.17 36.03 26.62c.42.39 3.85 3.66 2.12 3.64c-1.75-.02-3.5.1-5.23.3c-2.96.35-20.23 4.46-28.07 6.38c-1.55.38-5.75 1.16-6.91 1.39c-3.28.67-5.97-.38-8.65-2.26c-5.59-3.9-40.37-30.7-41.28-32.59c-.66-1.34.67-2.47 2.87-3.4"" />
    <path fill=""#2f2f2f"" d=""M110.83 91.97c-1.08-2.84-3.64-5.09-5.95-7.23C94.31 74.98 83.2 67 71.56 58.6c-2.99-2.16-5.92-4.24-9.03-6.23c-1.54-.98-3.45-1.81-5.09-2.63c-5.42-2.71-11.06-2.51-16.92-1.84c-10.67 1.22-20.98 1.58-31.22 5.08c-2.85.98-5.64 2.34-6.3 5.77c-.26 1.39.51 3.74 1.01 5c.39.96-2.89 12.98-3.38 14.42c-1.22 3.59.54 6.81 2.99 9.5c1.34 1.48 39.08 30.57 40.03 31.26c3.6 2.65 6.45 4.46 9.5 5.34c3.86 1.11 8.03.69 14.43-1.49c2.57-.88 23.26-6.01 34.77-10.56c3.92-1.55 6.17-3.63 7.09-4.64c1.12-1.22 1.84-2.77 2.23-4.4c.24-.99.36-2.02.37-3.02c.1-.31-.43-6.14-1.21-8.19M13.37 58.22c4.72-1.99 23.27-4.87 27.36-5.34c3.47-.4 7.84-.96 11.28-.45c1.39.23 5.85 2.56 10.48 5.71c9.47 6.44 34.45 25.17 36.03 26.62c.42.39 3.85 3.66 2.12 3.64c-1.75-.02-3.5.1-5.23.3c-2.96.35-20.23 4.46-28.07 6.38c-1.55.38-5.75 1.16-6.91 1.39c-3.28.67-5.97-.38-8.65-2.26c-5.59-3.9-40.37-30.7-41.28-32.59c-.66-1.34.67-2.47 2.87-3.4M9.39 85.99c-.97-.98-4.78-3.5-3.74-6.68c1.12-3.44 2.19-9.01 2.43-12.68c4.3 3.45 44.34 34.01 46.82 34.72c1.55 4.82 1.57 10.23-.15 15c-.24.66-.43 1.3-.71 1.94c-1.1-.41-2.18-.99-3.34-1.7c-1.86-1.16-38.07-27.33-41.31-30.6m93.34 20.05c-3.6 2.57-9.03 3.6-12.59 4.65c-7.62 2.26-15.31 4.32-23.01 6.28c-2.29.58-4.61 1.28-6.95 1.67c.43-.95.8-1.93 1.06-2.96c.69-2.72.82-5.64.52-8.43c-.16-1.43-.21-4.04-.55-5.42c.4-.03 9.83-2.26 17.28-4.16c7.58-1.93 16.37-4.6 21.64-4.71a6.71 6.71 0 0 1 4.33 1.5c1.84 1.51 2.26 4.3 1.8 6.52c-.43 2.18-1.76 3.8-3.53 5.06"" />
    <path fill=""#bdcf46"" d=""M23.86 79.56c5.72 4.39 20.66 15.8 27.52 21.04c2.18 1.66 4.63 3.38 9.6 2.48c4.97-.9 35.97-8.94 35.97-8.94c2.83-.74 5.96-.94 8.86-.55c1.05.14 2.08.47 3.02.97c.33.18.68.36.98.6c.16.13.27.3.44.42c2.29 1.55.89-3.1.63-3.73c-.63-1.53-1.5-2.96-2.5-4.28a23.6 23.6 0 0 0-2.66-2.91c-10.56-9.75-22.24-18.19-33.88-26.59c-2.99-2.16-5.94-4.4-9.05-6.39c-1.54-.98-3.1-1.95-4.73-2.77c-5.43-2.71-11.5-2.44-17.36-1.77c-10.67 1.22-21.34 1.82-31.58 5.33c-2.86.98-6.31 2.81-6.95 6.26c-.26 1.39-.26 3.81 2.19 5.98c1.78 1.58 11.96 9.06 19.5 14.85"" />
    <path fill=""#fff"" d=""M125.71 71.96c-.28-6.25-2.39-10.31-6.21-13.83c-9.54-8.81-21.04-17.22-31.64-24.75c-4.91-3.49-10.37-8.1-16.06-10.23c-8.02-3.01-17.08-1.94-25.68-.72c-3.75.53-7.52 1.13-11.21 2c-4.28 1.01-9.37 2.23-13 4.8c-3.26 2.31-2.86 5.21-1.26 7.88c.24 1.23.01 2.4.02 3.65c.02 2.36-.25 4.72-.82 7c-.36 1.41-.85 2.77-1.31 4.15c-1.82 5.34 3.23 9.81 6.98 12.91c2.1 1.73 4.34 3.29 6.45 5.01c8.19 6.67 16.6 12.7 24.98 19.05c9.83 7.45 13.58 10.04 25.85 5.85c3.31-1.13 6.7-2.03 10.09-2.9c4.8-1.23 11.17-2.4 15.85-4.04c4.73-1.66 10.84-3.86 14.31-7.65c1.73-1.89 2.48-4.6 2.5-7.12c.12-.31.18-.66.16-1.06"" />
    <path fill=""#bdcf46"" d=""M29.1 31.65c4.72-1.99 23.26-4.87 27.36-5.34c3.47-.4 7.83-.96 11.28-.45c1.39.23 5.85 2.56 10.48 5.71c9.47 6.44 34.45 25.17 36.02 26.62c.42.39 3.86 3.66 2.13 3.64c-1.75-.02-3.49.1-5.23.3c-2.95.35-20.23 4.46-28.07 6.38c-1.55.38-5.75 1.16-6.91 1.39c-3.28.67-5.97-.38-8.66-2.26c-5.59-3.9-40.37-30.7-41.28-32.59c-.65-1.34.68-2.47 2.88-3.4"" />
    <path fill=""#2f2f2f"" d=""M126.56 67.38c-1.08-2.85-3.64-5.1-5.95-7.23c-10.56-9.75-21.67-17.74-33.32-26.13c-3-2.16-5.92-4.24-9.03-6.22c-1.55-.98-3.45-1.81-5.09-2.63c-5.44-2.71-11.06-2.51-16.93-1.84c-10.66 1.22-20.97 1.58-31.21 5.09c-2.85.98-5.65 2.33-6.3 5.77c-.26 1.39.5 3.74 1.01 5c.39.96-2.89 12.98-3.38 14.41c-1.22 3.59.54 6.81 2.99 9.51c1.34 1.48 39.08 30.57 40.03 31.26c3.6 2.64 6.45 4.46 9.5 5.33c3.85 1.1 8.03.69 14.43-1.49c2.57-.88 23.26-6.01 34.77-10.55c3.92-1.55 6.17-3.64 7.09-4.64c1.12-1.22 1.84-2.77 2.24-4.4c.24-.99.36-2.02.37-3.02c.09-.34-.44-6.17-1.22-8.22M29.1 33.63c4.72-1.99 23.26-4.86 27.36-5.34c3.47-.41 7.83-.96 11.28-.45c1.39.23 5.85 2.56 10.48 5.71c9.47 6.44 34.45 25.17 36.02 26.62c.42.39 3.86 3.66 2.13 3.64c-1.75-.02-3.49.09-5.23.3c-2.95.35-20.23 4.47-28.07 6.38c-1.55.38-5.75 1.15-6.91 1.39c-3.28.67-5.97-.38-8.66-2.25c-5.59-3.9-40.37-30.7-41.28-32.59c-.65-1.35.68-2.48 2.88-3.41M25.12 61.4c-.97-.98-4.78-3.49-3.74-6.68c1.12-3.44 2.19-9 2.42-12.68c4.3 3.44 44.34 34.01 46.82 34.72c1.55 4.82 1.57 10.23-.15 14.99c-.24.66-.43 1.31-.71 1.94c-1.1-.41-2.18-.99-3.34-1.7c-1.85-1.15-38.06-27.32-41.3-30.59m93.34 20.05c-3.6 2.56-9.03 3.6-12.58 4.66c-7.62 2.26-15.31 4.31-23.01 6.28c-2.3.59-4.61 1.28-6.95 1.67c.43-.95.79-1.93 1.06-2.96c.69-2.73.82-5.64.53-8.43c-.16-1.43-.22-4.03-.56-5.42c.4-.03 9.83-2.26 17.28-4.16c7.58-1.93 16.36-4.6 21.65-4.71c2.09-.04 3.63.93 4.32 1.5c1.84 1.51 2.27 4.3 1.81 6.52c-.45 2.17-1.78 3.79-3.55 5.05"" />
    <path fill=""#006ca2"" d=""M39.59 52.99c5.72 4.39 20.66 15.8 27.53 21.04c2.18 1.67 4.63 3.38 9.59 2.48c4.96-.9 35.97-8.94 35.97-8.94a23.4 23.4 0 0 1 8.86-.55c1.06.14 2.09.47 3.02.97c.33.18.68.36.98.6c.16.13.27.3.44.41c2.29 1.55.88-3.09.62-3.73c-.63-1.53-1.5-2.96-2.5-4.28a23.6 23.6 0 0 0-2.66-2.91C110.88 48.34 99.21 39.9 87.56 31.5c-3-2.16-5.94-4.4-9.05-6.39c-1.54-.98-3.1-1.95-4.73-2.77c-5.43-2.71-11.5-2.44-17.36-1.77c-10.67 1.22-21.34 1.82-31.58 5.33c-2.86.98-6.3 2.82-6.95 6.26c-.26 1.39-.26 3.81 2.19 5.98c1.79 1.58 11.97 9.06 19.51 14.85"" />
    <path fill=""#fff"" d=""M115.93 54.96c-.28-6.25-2.38-10.31-6.21-13.83c-9.54-8.81-21.04-17.22-31.63-24.75c-4.91-3.49-10.37-8.1-16.06-10.23c-8.02-3.01-17.09-1.94-25.67-.72c-3.76.53-7.53 1.13-11.22 2c-4.28 1.02-9.37 2.23-13 4.8c-3.26 2.31-2.86 5.21-1.26 7.88c.23 1.23.01 2.4.02 3.65c.02 2.36-.25 4.72-.83 7c-.35 1.41-.84 2.77-1.31 4.15c-1.82 5.34 3.23 9.81 6.98 12.91c2.1 1.73 4.34 3.29 6.45 5.01c8.19 6.67 16.6 12.7 24.98 19.05c9.83 7.45 13.59 10.04 25.86 5.85c3.31-1.13 6.69-2.03 10.09-2.9c4.8-1.23 11.17-2.4 15.84-4.04c4.74-1.66 10.84-3.86 14.32-7.65c1.73-1.89 2.48-4.6 2.5-7.12c.11-.31.17-.66.15-1.06"" />
    <path fill=""#bdcf46"" d=""M19.32 14.65c4.72-1.99 23.27-4.87 27.36-5.34c3.47-.4 7.84-.96 11.28-.45c1.38.23 5.85 2.56 10.48 5.71c9.47 6.44 34.45 25.17 36.02 26.62c.42.39 3.85 3.66 2.12 3.64c-1.75-.02-3.49.1-5.23.3c-2.96.35-20.23 4.46-28.07 6.38c-1.55.38-5.75 1.16-6.91 1.39c-3.28.67-5.97-.38-8.66-2.26c-5.59-3.9-40.37-30.7-41.28-32.59c-.63-1.35.69-2.48 2.89-3.4"" />
    <path fill=""#2f2f2f"" d=""M116.78 48.39c-1.08-2.84-3.63-5.09-5.95-7.23c-10.56-9.75-21.67-17.73-33.32-26.13c-2.99-2.16-5.92-4.24-9.03-6.23c-1.54-.98-3.45-1.81-5.09-2.63c-5.43-2.71-11.06-2.51-16.92-1.84C35.8 5.55 25.5 5.91 15.25 9.42c-2.85.98-5.65 2.33-6.3 5.77c-.26 1.39.5 3.74 1.01 5c.39.96-2.9 12.98-3.38 14.42c-1.22 3.59.54 6.81 2.99 9.5c1.34 1.48 39.08 30.57 40.03 31.26c3.6 2.65 6.45 4.46 9.5 5.33c3.85 1.11 8.03.69 14.43-1.49c2.58-.88 23.26-6.01 34.77-10.56c3.92-1.55 6.17-3.63 7.09-4.64c1.12-1.22 1.84-2.77 2.24-4.4c.24-.99.35-2.02.37-3.02c.09-.31-.44-6.14-1.22-8.2M19.32 14.65c4.72-1.99 23.27-4.87 27.36-5.34c3.47-.4 7.84-.96 11.28-.45c1.38.23 5.85 2.56 10.48 5.71c9.47 6.44 34.45 25.17 36.02 26.62c.42.39 3.85 3.66 2.12 3.64c-1.75-.02-3.49.1-5.23.3c-2.96.35-20.23 4.46-28.07 6.38c-1.55.38-5.75 1.16-6.91 1.39c-3.28.67-5.97-.38-8.66-2.26c-5.59-3.9-40.37-30.7-41.28-32.59c-.63-1.35.69-2.48 2.89-3.4m-3.98 27.77c-.97-.98-4.78-3.5-3.74-6.68c1.12-3.44 2.19-9.01 2.43-12.68c4.29 3.45 44.33 34.01 46.82 34.72c1.55 4.82 1.57 10.23-.15 15c-.24.66-.43 1.3-.71 1.94c-1.11-.41-2.18-.99-3.34-1.7c-1.85-1.16-38.07-27.34-41.31-30.6m93.35 20.04c-3.61 2.56-9.03 3.6-12.59 4.65c-7.62 2.26-15.31 4.32-23.01 6.28c-2.29.58-4.61 1.28-6.95 1.67c.43-.95.8-1.93 1.06-2.96c.69-2.72.82-5.64.52-8.43c-.16-1.43-.21-4.04-.55-5.43c.4-.03 9.83-2.26 17.28-4.15c7.58-1.93 16.37-4.6 21.65-4.71a6.71 6.71 0 0 1 4.33 1.5c1.84 1.51 2.26 4.3 1.8 6.52c-.45 2.18-1.78 3.81-3.54 5.06"" />
    <path fill=""#ed6c30"" d=""M29.81 35.98c5.73 4.39 20.66 15.8 27.53 21.04c2.18 1.67 4.63 3.38 9.59 2.48c4.96-.9 35.97-8.94 35.97-8.94c2.83-.74 5.96-.94 8.86-.55c1.05.14 2.08.47 3.02.97c.33.18.68.36.98.6c.16.13.27.3.44.42c2.29 1.55.88-3.1.62-3.73c-.63-1.53-1.5-2.96-2.5-4.28c-.8-1.04-1.69-2.01-2.65-2.91c-10.56-9.75-22.24-18.19-33.88-26.59c-2.99-2.16-5.94-4.4-9.06-6.39c-1.54-.97-3.09-1.94-4.73-2.76c-5.43-2.71-11.5-2.44-17.36-1.77C35.97 4.8 25.31 5.4 15.07 8.9c-2.86.98-6.31 2.82-6.96 6.26c-.26 1.39-.26 3.81 2.19 5.98c1.79 1.58 11.97 9.06 19.51 14.84"" />
    <path fill=""#fff"" d=""M39.65 24.54c-.87-.1-1.71-.35-2.51-.82c-1.61-.95-3.42-2.35-4.43-3.83c-1.19-1.74.2-2.38 1.95-2.82c2.42-.6 4.91-.99 7.4-1.32c2.56-.34 5.18-.54 7.74-.88c1.96-.27 3.97-.45 5.91-.64c1.17-.1 2.41-.3 3.56.06c1.18.37 2.24 1.19 3.14 1.96c1 .86 2.08 1.65 2.91 2.66c.23.29.49.56.57.93c.05.22.07.36-.05.55c-.33.48-1.22.81-1.78.87c-3.1.35-6.03 1.22-9.08 1.77c-2.69.48-5.48.6-8.2.92c-1.39.17-2.79.27-4.19.41c-.98.11-1.94.3-2.94.18"" /></svg>";

    const string Roles = @"<svg width=""24"" height=""24"" viewBox=""0 0 24 24"" fill=""currentColor"">
    <path d=""M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7.5V9C15 10.1 14.1 11 13 11S11 10.1 11 9V7.5L5 7V9C5 10.1 4.1 11 3 11S1 10.1 1 9V7C1 6.4 1.4 6 2 6L11 6.5C11 5.1 11.9 4 13 4S15 5.1 15 6.5L22 6C22.6 6 23 6.4 23 7V9C23 10.1 22.1 11 21 11S19 10.1 19 9ZM7.5 12C8.3 12 9 12.7 9 13.5S8.3 15 7.5 15 6 14.3 6 13.5 6.7 12 7.5 12ZM16.5 12C17.3 12 18 12.7 18 13.5S17.3 15 16.5 15 15 14.3 15 13.5 15.7 12 16.5 12ZM12 14C12.8 14 13.5 14.7 13.5 15.5S12.8 17 12 17 10.5 16.3 10.5 15.5 11.2 14 12 14ZM7.5 18C8.3 18 9 18.7 9 19.5S8.3 21 7.5 21 6 20.3 6 19.5 6.7 18 7.5 18ZM16.5 18C17.3 18 18 18.7 18 19.5S17.3 21 16.5 21 15 20.3 15 19.5 15.7 18 16.5 18Z""/>
</svg>";





}