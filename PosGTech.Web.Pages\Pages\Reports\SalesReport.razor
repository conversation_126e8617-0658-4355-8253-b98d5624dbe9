@page "/salesReport"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<PageTitle>تقرير المبيعات</PageTitle>

<AuthorizeView Policy="@PermissionConstants.ReportsView" Context="auth">
    <Authorized>
        <MudPaper Class="d-flex flex-column desktop rounded-0" Elevation="0">
    
    <!-- Filter Section -->
    <MudPaper Class="pa-4 mb-4" Elevation="2">
        <MudGrid>
            <MudItem xs="12" md="3">
                <MudDatePicker @bind-Date="fromDate" Label="من تاريخ" DateFormat="yyyy-MM-dd" />
            </MudItem>
            <MudItem xs="12" md="3">
                <MudDatePicker @bind-Date="toDate" Label="إلى تاريخ" DateFormat="yyyy-MM-dd" />
            </MudItem>
            <MudItem xs="12" md="3">
                <MudSelect T="Guid?" @bind-Value="selectedStoreId" Label="المخزن" Clearable="true">
                    @foreach (var store in stores)
                    {
                        <MudSelectItem T="Guid?" Value="store.Id">@store.Name</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="12" md="3">
                <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="LoadReport" 
                          StartIcon="@Icons.Material.Filled.Search" FullWidth="true">
                    عرض التقرير
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>

    @if (loading)
    {
        <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="my-7" />
    }
    else if (salesReport != null)
    {
        <!-- Statistics Cards -->
        <MudGrid Class="mb-4">
            <MudItem xs="12" sm="6" md="3">
                <MudCard>
                    <MudCardContent>
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.AttachMoney" Color="Color.Success" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h6">@salesReport.Statistics.TotalRevenue.ToString("N2")</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Secondary">إجمالي المبيعات</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudCard>
                    <MudCardContent>
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.Receipt" Color="Color.Info" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h6">@salesReport.Statistics.TotalInvoices</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Secondary">عدد الفواتير</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudCard>
                    <MudCardContent>
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.Discount" Color="Color.Warning" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h6">@salesReport.Statistics.TotalDiscount.ToString("N2")</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Secondary">إجمالي الخصومات</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" sm="6" md="3">
                <MudCard>
                    <MudCardContent>
                        <div class="d-flex align-center">
                            <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Color="Color.Primary" Size="Size.Large" Class="mr-3" />
                            <div>
                                <MudText Typo="Typo.h6">@salesReport.Statistics.AverageInvoiceValue.ToString("N2")</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Secondary">متوسط قيمة الفاتورة</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>

        <!-- Tabs for different views -->
        <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
            <MudTabPanel Text="تفاصيل المبيعات" Icon="@Icons.Material.Filled.List">
                <MudTable Items="@salesReport.SalesDetails" Height="400px" Virtualize="true" 
                         Striped="true" Dense="true" FixedHeader="true" Elevation="0"
                         Filter="new Func<SalesReportDetailDTO,bool>(FilterSalesDetails)" @bind-SelectedItem="selectedSale">
                    
                    <ToolBarContent>
                        <MudText Typo="Typo.h6">تفاصيل المبيعات</MudText>
                        <MudSpacer />
                        <MudTextField @bind-Value="searchString" Placeholder="بحث" 
                                     Adornment="Adornment.End" AdornmentIcon="@Icons.Material.Filled.Search" 
                                     IconSize="Size.Medium" Class="mt-0"></MudTextField>
                    </ToolBarContent>

                    <HeaderContent>
                        <MudTh>رقم الفاتورة</MudTh>
                        <MudTh>التاريخ</MudTh>
                        <MudTh>العميل</MudTh>
                        <MudTh>المخزن</MudTh>
                        <MudTh>الإجمالي</MudTh>
                        <MudTh>الخصم</MudTh>
                        <MudTh>الصافي</MudTh>
                        <MudTh>المدفوع</MudTh>
                        <MudTh>المتبقي</MudTh>
                        <MudTh>عدد الأصناف</MudTh>
                    </HeaderContent>
                    
                    <RowTemplate>
                        <MudTd DataLabel="رقم الفاتورة">@context.InvoiceNo</MudTd>
                        <MudTd DataLabel="التاريخ">@context.Date.ToString("yyyy-MM-dd")</MudTd>
                        <MudTd DataLabel="العميل">@context.Client.Name</MudTd>
                        <MudTd DataLabel="المخزن">@context.StoreName</MudTd>
                        <MudTd DataLabel="الإجمالي">@context.Total.ToString("N2")</MudTd>
                        <MudTd DataLabel="الخصم">@context.DiscountValue.ToString("N2")</MudTd>
                        <MudTd DataLabel="الصافي">@context.FinalTotal.ToString("N2")</MudTd>
                        <MudTd DataLabel="المدفوع">@context.Paid.ToString("N2")</MudTd>
                        <MudTd DataLabel="المتبقي">
                            <MudText Color="@(context.Remaining > 0 ? Color.Error : Color.Success)">
                                @context.Remaining.ToString("N2")
                            </MudText>
                        </MudTd>
                        <MudTd DataLabel="عدد الأصناف">@context.ItemCount</MudTd>
                    </RowTemplate>
                </MudTable>
            </MudTabPanel>

            <MudTabPanel Text="الملخص اليومي" Icon="@Icons.Material.Filled.CalendarToday">
                <MudTable Items="@salesReport.DailySummary" Height="400px" Virtualize="true" 
                         Striped="true" Dense="true" FixedHeader="true" Elevation="0">
                    
                    <ToolBarContent>
                        <MudText Typo="Typo.h6">الملخص اليومي للمبيعات</MudText>
                    </ToolBarContent>

                    <HeaderContent>
                        <MudTh>التاريخ</MudTh>
                        <MudTh>عدد الفواتير</MudTh>
                        <MudTh>إجمالي المبيعات</MudTh>
                        <MudTh>إجمالي الخصومات</MudTh>
                        <MudTh>الصافي</MudTh>
                        <MudTh>عدد الأصناف</MudTh>
                        <MudTh>متوسط قيمة الفاتورة</MudTh>
                    </HeaderContent>
                    
                    <RowTemplate>
                        <MudTd DataLabel="التاريخ">@context.Date.ToString("yyyy-MM-dd")</MudTd>
                        <MudTd DataLabel="عدد الفواتير">@context.InvoiceCount</MudTd>
                        <MudTd DataLabel="إجمالي المبيعات">@context.TotalSales.ToString("N2")</MudTd>
                        <MudTd DataLabel="إجمالي الخصومات">@context.TotalDiscount.ToString("N2")</MudTd>
                        <MudTd DataLabel="الصافي">@context.FinalTotal.ToString("N2")</MudTd>
                        <MudTd DataLabel="عدد الأصناف">@context.ItemCount</MudTd>
                        <MudTd DataLabel="متوسط قيمة الفاتورة">@context.AverageInvoiceValue.ToString("N2")</MudTd>
                    </RowTemplate>
                </MudTable>
            </MudTabPanel>
        </MudTabs>
    }
</MudPaper>

<style>
    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }
</style>
    </Authorized>
    <NotAuthorized>
        <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Class="ma-4">
            ليس لديك صلاحية الوصول لعرض التقارير
        </MudAlert>
    </NotAuthorized>
</AuthorizeView>
