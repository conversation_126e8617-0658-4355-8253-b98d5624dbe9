@page "/listStoreSettings"
@using PosGTech.ModelsDTO.ShopSettings
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<AuthorizeView Policy="@PermissionConstants.StoresView" Context="auth">
    <Authorized>
        <MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <MudPaper Class="pa-4" Elevation="2">
        <!-- Header -->
        <MudGrid AlignItems="Center" Class="mb-4">
            <MudItem xs="12" md="6">
                <MudText Typo="Typo.h4" Color="Color.Primary">
                    <MudIcon Icon="@Icons.Material.Filled.Store" Class="me-2" />
                    إعدادات المتاجر
                </MudText>
            </MudItem>
            <MudItem xs="12" md="6" Class="d-flex justify-end">
                <MudButton Variant="Variant.Filled" 
                           Color="Color.Primary" 
                           StartIcon="@Icons.Material.Filled.Add"
                           OnClick="() => Upsert(Guid.Empty)">
                    إضافة إعدادات متجر جديد
                </MudButton>
            </MudItem>
        </MudGrid>

        <!-- Search and Filter -->
        <MudGrid Class="mb-4">
            <MudItem xs="12" md="6">
                <MudTextField @bind-Value="searchString"
                              Placeholder="البحث في اسم المتجر أو الشركة..."
                              Adornment="Adornment.Start"
                              AdornmentIcon="@Icons.Material.Filled.Search"
                              IconSize="Size.Medium"
                              Immediate="true"
                              OnAdornmentClick="SearchStores"
                              Variant="Variant.Outlined" />
            </MudItem>
            <MudItem xs="12" md="6" Class="d-flex align-center justify-end">
                <MudButton Variant="Variant.Outlined" 
                           Color="Color.Secondary"
                           StartIcon="@Icons.Material.Filled.Refresh"
                           OnClick="LoadingData">
                    تحديث
                </MudButton>
            </MudItem>
        </MudGrid>

        <!-- Statistics Cards -->
        <MudGrid Class="mb-4">
            <MudItem xs="12" md="4">
                <MudCard>
                    <MudCardContent Class="text-center">
                        <MudIcon Icon="@Icons.Material.Filled.Store" Size="Size.Large" Color="Color.Primary" />
                        <MudText Typo="Typo.h6">إجمالي المتاجر</MudText>
                        <MudText Typo="Typo.h4" Color="Color.Primary">@shopSettingsList.Count()</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" md="4">
                <MudCard>
                    <MudCardContent Class="text-center">
                        <MudIcon Icon="@Icons.Material.Filled.Image" Size="Size.Large" Color="Color.Success" />
                        <MudText Typo="Typo.h6">متاجر بشعار</MudText>
                        <MudText Typo="Typo.h4" Color="Color.Success">@shopSettingsList.Count(s => !string.IsNullOrEmpty(s.LogoPath))</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" md="4">
                <MudCard>
                    <MudCardContent Class="text-center">
                        <MudIcon Icon="@Icons.Material.Filled.Phone" Size="Size.Large" Color="Color.Info" />
                        <MudText Typo="Typo.h6">متاجر بهاتف</MudText>
                        <MudText Typo="Typo.h4" Color="Color.Info">@shopSettingsList.Count(s => !string.IsNullOrEmpty(s.CompanyPhone))</MudText>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>

        <!-- Data Table -->
        <MudDataGrid T="ShopSettingsDTO"
                     Items="@FilteredStores"
                     Filterable="true"
                     SortMode="SortMode.Multiple"
                     Hover="true"
                     Dense="true"
                     Loading="@isLoading"
                     LoadingProgressColor="Color.Primary">
            <Columns>
                <PropertyColumn Property="x => x.StoreName" Title="اسم المتجر" />
                <PropertyColumn Property="x => x.CompanyName" Title="اسم الشركة" />
                <PropertyColumn Property="x => x.CompanyPhone" Title="رقم الهاتف" />
                <PropertyColumn Property="x => x.StoreAddress" Title="العنوان" />
                <TemplateColumn Title="الشعار" Sortable="false" Filterable="false">
                    <CellTemplate>
                        @if (!string.IsNullOrEmpty(context.Item.LogoPath))
                        {
                            <MudImage Src="@context.Item.LogoPath" 
                                      Alt="شعار المتجر" 
                                      Width="50" 
                                      Height="50" 
                                      ObjectFit="ObjectFit.Contain"
                                      Class="border rounded" />
                        }
                        else
                        {
                            <MudIcon Icon="@Icons.Material.Filled.ImageNotSupported" Color="Color.Default" />
                        }
                    </CellTemplate>
                </TemplateColumn>
                <TemplateColumn Title="العمليات" Sortable="false" Filterable="false">
                    <CellTemplate>
                        <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                            <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                           Color="Color.Primary" 
                                           Size="Size.Small"
                                           OnClick="() => Upsert(context.Item.Id)"
                                           Title="تعديل" />
                            <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                           Color="Color.Error" 
                                           Size="Size.Small"
                                           OnClick="() => Delete(context.Item)"
                                           Title="حذف" />
                        </MudButtonGroup>
                    </CellTemplate>
                </TemplateColumn>
            </Columns>
            <PagerContent>
                <MudDataGridPager T="ShopSettingsDTO" />
            </PagerContent>
        </MudDataGrid>
    </MudPaper>
</MudContainer>

<!-- Delete Confirmation Dialog -->
<MudMessageBox @ref="mbox" Title="تأكيد الحذف" CancelText="إلغاء">
    <MessageContent>
        هل أنت متأكد من حذف إعدادات المتجر "@NameStoreForDelete"؟
        <br />
        <strong>تحذير:</strong> سيتم حذف جميع البيانات المرتبطة بهذا المتجر نهائياً.
    </MessageContent>
    <YesButton>
        <MudButton Variant="Variant.Filled" Color="Color.Error" StartIcon="@Icons.Material.Filled.DeleteForever">
            حذف نهائي
        </MudButton>
    </YesButton>
</MudMessageBox>
    </Authorized>
    <NotAuthorized>
        <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Class="ma-4">
            ليس لديك صلاحية الوصول لعرض إعدادات المتاجر
        </MudAlert>
    </NotAuthorized>
</AuthorizeView>
