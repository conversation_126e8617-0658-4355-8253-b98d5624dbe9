﻿using PosGTech.ModelsDTO.Units;
using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Items
{
    public class ItemUnitDTO
    {
        public Guid Id { get; set; }

        public UnitDTO? Unit { get; set; }
        public ItemDTO? Item { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal Quantity { get; set; }
        public decimal SalePrice { get; set; }

        /// <summary>
        /// هل هو اكبر من الوحدة الاساسية او لا
        /// اكبر عملة X
        /// اصغر عملة /
        /// </summary>
        public bool IsBigger { get; set; }
        public bool IsBasicUnit { get; set; }
        public bool IsAbleToDelete { get; set; } = true;
    }
}
