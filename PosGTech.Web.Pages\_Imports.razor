﻿@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using PosGTech.Web
@using PosGTech.Web.Pages.Layout
@using MudBlazor
@using MudExtensions
@using PosGTech.ModelsDTO
@inject ISnackbar _snackbar
@inject NavigationManager _navigation
@inject IJSRuntime JS

@using PosGTech.Models.ViewModels
@using PosGTech.ModelsDTO.Authentication
@using PosGTech.ModelsDTO.Categories
@using PosGTech.ModelsDTO.Clients
@using PosGTech.ModelsDTO.Consumeds
@using PosGTech.ModelsDTO.Employees
@using PosGTech.ModelsDTO.Expenses
@using PosGTech.ModelsDTO.Finacnial
@using PosGTech.ModelsDTO.Items
@using PosGTech.ModelsDTO.Purchases
@using PosGTech.ModelsDTO.Inventories
@using PosGTech.ModelsDTO.Receipts
@using PosGTech.ModelsDTO.Stores
@using PosGTech.ModelsDTO.Treasury
@using PosGTech.ModelsDTO.Units
@using PosGTech.ModelsDTO.Users
@using PosGTech.ModelsDTO.Roles
@using PosGTech.ModelsDTO.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@using PosGTech.ModelsDTO.StoreItem
@using PosGTech.ModelsDTO.StoreItemExp
@using PosGTech.ModelsDTO.Sells
@using PosGTech.Models
@using PosGTech.Web.Pages.Components

