﻿using PosGTech.ModelsDTO.Items;
using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Inventories;

public class InventoryItemDTO
{
    public Guid Id { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
    [Range(0, 10000000, ErrorMessage = "خطا في القيمة", MaximumIsExclusive = false)]
    public decimal Quantity { get; set; } = 1;
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
    [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
    public decimal Price { get; set; }
    public DateOnly? Exp { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public ItemCMDTO? Item { get; set; }
    public Guid? InventoryId { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public ItemUnitDTO? ItemUnit { get; set; }
}
