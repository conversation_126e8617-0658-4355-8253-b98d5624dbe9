using Microsoft.EntityFrameworkCore;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository;
using PosGTech.Models;

namespace PosGTech.Tests.Repositories
{
    public class ItemUnitRepositoryTests : IDisposable
    {
        private readonly ApplicationDbContext _context = new ApplicationDbContext(new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase("TestDatabase")
                .Options);

        [Fact]
        public async Task GetAll_ReturnsAllItemUnits()
        {
            // Arrange
            var expectedItemUnits = new List<ItemUnit>
            {
                new ItemUnit { 
                    Id = Guid.NewGuid(), 
                    ItemId = Guid.NewGuid(),
                    UnitId = Guid.NewGuid(),
                    Quantity = 10,
                    SalePrice = 100
                },
                new ItemUnit { 
                    Id = Guid.NewGuid(), 
                    ItemId = Guid.NewGuid(),
                    UnitId = Guid.NewGuid(),
                    Quantity = 20,
                    SalePrice = 200
                }
            };
            await _context.ItemUnits.AddRangeAsync(expectedItemUnits);
            await _context.SaveChangesAsync();
            var repository = new ItemUnitRepository(_context);

            // Act
            var result = await repository.GetAll();

            // Assert
            Assert.Equal(expectedItemUnits.Count, result.Count());
        }

        [Fact]
        public async Task Add_SavesItemUnit()
        {
            // Arrange
            var itemUnit = new ItemUnit { 
                Id = Guid.NewGuid(),
                ItemId = Guid.NewGuid(),
                UnitId = Guid.NewGuid(),
                Quantity = 15,
                SalePrice = 150
            };
            var repository = new ItemUnitRepository(_context);

            // Act
            repository.Add(itemUnit);

            // Assert
            var savedItemUnit = await _context.ItemUnits.FindAsync(itemUnit.Id);
            Assert.NotNull(savedItemUnit);
            Assert.Equal(itemUnit.Quantity, savedItemUnit.Quantity);
            Assert.Equal(itemUnit.SalePrice, savedItemUnit.SalePrice);
        }

        [Fact]
        public async Task Update_UpdatesItemUnit()
        {
            // Arrange
            var itemUnit = new ItemUnit { 
                Id = Guid.NewGuid(),
                ItemId = Guid.NewGuid(),
                UnitId = Guid.NewGuid(),
                Quantity = 10,
                SalePrice = 100
            };
            await _context.ItemUnits.AddAsync(itemUnit);
            await _context.SaveChangesAsync();
            var repository = new ItemUnitRepository(_context);

            // Act
            itemUnit.Quantity = 25;
            itemUnit.SalePrice = 250;
            repository.Update(itemUnit);

            // Assert
            var updatedItemUnit = await _context.ItemUnits.FindAsync(itemUnit.Id);
            Assert.NotNull(updatedItemUnit);
            Assert.Equal(25, updatedItemUnit.Quantity);
            Assert.Equal(250, updatedItemUnit.SalePrice);
        }

        [Fact]
        public async Task Delete_RemovesItemUnit()
        {
            // Arrange
            var itemUnit = new ItemUnit { 
                Id = Guid.NewGuid(),
                ItemId = Guid.NewGuid(),
                UnitId = Guid.NewGuid(),
                Quantity = 10,
                SalePrice = 100
            };
            await _context.ItemUnits.AddAsync(itemUnit);
            await _context.SaveChangesAsync();
            var repository = new ItemUnitRepository(_context);

            // Act
            repository.Remove(itemUnit);
            await _context.SaveChangesAsync();

            // Assert
            var deletedItemUnit = await _context.ItemUnits.FindAsync(itemUnit.Id);
            Assert.Null(deletedItemUnit);
        }

        [Fact]
        public async Task GetById_ReturnsItemUnitById()
        {
            // Arrange
            var itemUnit = new ItemUnit { 
                Id = Guid.NewGuid(),
                ItemId = Guid.NewGuid(),
                UnitId = Guid.NewGuid(),
                Quantity = 10,
                SalePrice = 100
            };
            await _context.ItemUnits.AddAsync(itemUnit);
            await _context.SaveChangesAsync();
            var repository = new ItemUnitRepository(_context);

            // Act
            var result = await repository.GetByIdAsync(itemUnit.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(itemUnit.Quantity, result.Quantity);
            Assert.Equal(itemUnit.SalePrice, result.SalePrice);
        }

        public void Dispose()
        {
            _context.Database.EnsureDeleted();
            _context.Dispose();
        }
    }
}
