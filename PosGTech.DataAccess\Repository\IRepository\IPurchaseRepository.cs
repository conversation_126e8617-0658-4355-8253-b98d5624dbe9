﻿using AutoMapper;
using PosGTech.Models;
using PosGTech.ModelsDTO.Purchases;

namespace PosGTech.DataAccess.Repository.IRepository
{
    public interface IPurchaseRepository : IRepository<Purchase>
    {
        Task<Guid> AddPurchase(Purchase purchase, decimal paid = 0, Guid? cashId = null);
        Task UpdatePurchase(Purchase oldPurchase, Purchase newPurchase);
        Task DeletePurchase(Purchase purchase);
        Task<IEnumerable<PurchaseDTO>> GetAllPurchase(IMapper mapper);
        Task<PurchaseDTO> GetPurchaseById(IMapper mapper, Guid id);
        Task<PurchaseDTO> GetPurchaseByNum(IMapper mapper, int num);
        Task<PurchaseCMDTO> ReportPurchase(Guid id, IMapper mapper);

    }
}
