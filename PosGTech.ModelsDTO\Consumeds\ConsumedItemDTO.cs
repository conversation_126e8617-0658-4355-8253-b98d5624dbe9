﻿using PosGTech.ModelsDTO.Items;
using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Consumeds;

public class ConsumedItemDTO
{
    public Guid Id { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
    [Range(0.1, 10000000, ErrorMessage = "خطا في القيمة")]
    public decimal Quantity { get; set; } = 1;
    public decimal Price { get; set; }
    public Guid? ConsumedId { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public ItemCMDTO? Item { get; set; }
}
