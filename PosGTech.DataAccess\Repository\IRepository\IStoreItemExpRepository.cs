﻿using PosGTech.Models;
using PosGTech.ModelsDTO.Items;

namespace PosGTech.DataAccess.Repository.IRepository
{
    public interface IStoreItemExpRepository : IRepository<StoreItemExp>
    {
        Task UpdateReduceQuantity(IEnumerable<ItemForChangeQte> itemForChangeQte);
        Task UpdateReduceQuantityDirectly(IEnumerable<ItemForChangeQte> itemForChangeQte);
        Task AddQuantity(IEnumerable<ItemForChangeQte> itemForChangeQte);
        Task AddQuantityDirectly(IEnumerable<ItemForChangeQte> itemForChangeQte);
        bool CheckQuantityItemsBelowZero();

    }
}
