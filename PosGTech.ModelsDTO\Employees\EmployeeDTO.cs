﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Employees;

public class EmployeeDTO
{
    public Guid Id { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    [StringLength(100, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
    public string Name { get; set; }
    public int? Phone { get; set; }
    public string? Job { get; set; }
    public decimal Salary { get; set; }
    public decimal Balance { get; set; }

}
