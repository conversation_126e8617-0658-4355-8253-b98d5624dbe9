﻿using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO;
using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.Employees;
using PosGTech.ModelsDTO.Expenses;
using PosGTech.ModelsDTO.Finacnial;
using PosGTech.ModelsDTO.Purchases;
using PosGTech.ModelsDTO.Receipts;
using PosGTech.ModelsDTO.ShopSettings;
using PosGTech.ModelsDTO.Treasury;
using PosGTech.ModelsDTO.Users;
using PosGTech.Web.Services.Interfaces;

/// <summary>
/// صفحة إدارة الإيصالات مع دعم طباعة محسن
/// تتضمن تكامل كامل مع إعدادات المتجر (UpsertStoreSettings) لعرض:
/// - شعار المتجر (LogoPath)
/// - اسم المتجر (StoreName)
/// - اسم الشركة (CompanyName)
/// - رقم الهاتف (CompanyPhone)
/// - عنوان المتجر (StoreAddress)
///
/// التحديثات المضافة:
/// - تحميل تلقائي لإعدادات المتجر الافتراضية
/// - عرض بيانات المتجر في تصميم الإيصال
/// - دعم عرض الشعار مع معالجة أخطاء التحميل
/// - تحسين تخطيط الإيصال لعرض معلومات الشركة
/// </summary>

namespace PosGTech.Web.Pages.Pages.Receipts
{
    public partial class UpsertReceipt
    {
        [CascadingParameter] MudDialogInstance MudDialog { get; set; }
        [Parameter]
        public Guid id { get; set; }
        [Parameter]
        public Guid? financialId { get; set; }
        [Parameter]
        public SellReceiptDTO? Sell { get; set; }
        [Parameter]
        public PurchaseReceiptDTO? Purchase { get; set; }
        [Parameter]
        public ClientCMDTO? Client { get; set; }
        [Parameter]
        public EmployeeCMDTO? Employee { get; set; }
        [Parameter]
        public ExpenseDTO? Expense { get; set; }
        ReceiptDTO receipt = new();
        IEnumerable<FinancialDTO> Financials = new List<FinancialDTO>();
        IEnumerable<UserTreasuryCMDTO> UserTreasury = new List<UserTreasuryCMDTO>();
        IEnumerable<UserCMDTO> Users = new List<UserCMDTO>();

        // متغيرات الطباعة وإعدادات المتجر
        private ShopSettingsDTO? _defaultShopSettings;
        private bool _isPrinting = false;
        private string _printButtonText = "طباعة الإيصال";

        [Inject]
        IGRepository<ReceiptDTO> _receipt { get; set; }
        [Inject]
        IGRepository<ShopSettingsDTO> _shopSettings { get; set; }
        [Inject]
        IJSRuntime _jsRuntime { get; set; }
        [Inject]
        IGRepository<FinancialDTO> _financial { get; set; }
        [Inject]
        IGRepository<UserTreasuryCMDTO> _userTreasury { get; set; }
        [Inject]
        IGRepository<UserCMDTO> _user { get; set; }
        protected override async Task OnInitializedAsync()
        {
            await LoadingData();
            await LoadDefaultShopSettings();

            if (id != Guid.Empty)
            {
                var res = await _receipt.GetByIdAsync("Receipts/getReceiptById", id);
                if (res.response == null)
                {
                    receipt = res.model;

                }
                else
                {
                    _snackbar.Add("خطأ في الاتصال", Severity.Error);
                    MudDialog.Cancel();
                }
            }
            else
            {
                if (financialId != null)
                {
                    receipt.Financial = Financials.First(x => x.Id == financialId);

                }
            }
        }
        async void Upsert()
        {
            if (receipt.Value == 0)
            {
                _snackbar.Add("خطأ في القيمة", Severity.Error);
                return;
            }
            ResponseVM response;
            if (id == Guid.Empty)
                response = await _receipt.Insert("Receipts/insertReceipt", receipt);
            else
                response = await _receipt.Update("Receipts/updateReceipt", receipt, id);

            if (response.State)
            {
                _snackbar.Add(response.Message, Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                _snackbar.Add(response.Message, Severity.Error);
            }
        }

        async Task OnFinChanged(FinancialDTO? args)
        {
            receipt.Financial = args;
            receipt.Purchase = null;
            receipt.Sell = null;
            receipt.Client = null;
            receipt.Employee = null;
            receipt.Expense = null;
            receipt.ToTreasury = null;
            receipt.UserTreasury = null;
            receipt.Value = 0;
        }
        private async Task<IEnumerable<FinancialDTO>> SearchFinancial(string value, CancellationToken token)
        {
            if (string.IsNullOrEmpty(value))
                return Financials;
            return Financials.Where(x => x.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase));
        }
        async Task LoadingData()
        {
            var res = await _financial.GetAll("Financials/getAllFinancials");
            if (res.response == null) Financials = res.list;
            var resUsers = await _user.GetAll("Users/getAllUsersCM");
            if (resUsers.response == null) Users = resUsers.list;
            var resUserTreasury = await _userTreasury.GetAll("Users/getAllUserTreasury");
            if (resUserTreasury.response == null) UserTreasury = resUserTreasury.list;

            receipt.Purchase = Purchase;
            receipt.Sell = Sell;
            receipt.Client = Client;
            receipt.Employee = Employee;
            receipt.Expense = Expense;
        }

        /// <summary>
        /// تحميل إعدادات المتجر الافتراضية
        /// </summary>
        private async Task LoadDefaultShopSettings()
        {
            try
            {
                // استخدام نفس الطريقة المستخدمة في صفحة المبيعات
                var response = await _shopSettings.GetByIdAsync("ShopSettings/getDefaultShopSettings", Guid.Empty);
                if (response.response == null && response.model != null)
                {
                    _defaultShopSettings = response.model;
                }
                else
                {
                    // محاولة جلب أول إعدادات متاحة إذا لم توجد إعدادات افتراضية
                    var allSettingsResponse = await _shopSettings.GetAll("ShopSettings/getAllShopSettings");
                    if (allSettingsResponse.response == null && allSettingsResponse.list != null && allSettingsResponse.list.Any())
                    {
                        _defaultShopSettings = allSettingsResponse.list.First();
                    }
                    else
                    {
                        // في حالة عدم وجود أي إعدادات، استخدم قيم افتراضية
                        _defaultShopSettings = new ShopSettingsDTO
                        {
                            StoreName = "اسم المتجر",
                            CompanyName = "اسم الشركة",
                            CompanyPhone = "رقم الهاتف",
                            StoreAddress = "عنوان المتجر"
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                _snackbar.Add($"خطأ في تحميل إعدادات المتجر: {ex.Message}", Severity.Warning);
                // استخدم قيم افتراضية في حالة الخطأ
                _defaultShopSettings = new ShopSettingsDTO
                {
                    StoreName = "اسم المتجر",
                    CompanyName = "اسم الشركة",
                    CompanyPhone = "رقم الهاتف",
                    StoreAddress = "عنوان المتجر"
                };
            }
        }

        /// <summary>
        /// إعادة تحميل إعدادات المتجر لضمان الحصول على أحدث البيانات
        /// </summary>
        private async Task RefreshShopSettings()
        {
            await LoadDefaultShopSettings();
        }

        /// <summary>
        /// طباعة الإيصال باستخدام Print.js
        /// </summary>
        private async Task Print()
        {
            // إعادة تحميل إعدادات المتجر قبل الطباعة لضمان الحصول على أحدث البيانات
            await RefreshShopSettings();
            await PrintWithFormat("auto");
        }

        /// <summary>
        /// طباعة الإيصال بتنسيق محدد
        /// </summary>
        private async Task PrintWithFormat(string format)
        {
            if (_isPrinting) return;

            // التحقق من صحة البيانات قبل الطباعة
            if (!ValidateReceiptForPrint())
                return;

            try
            {
                _isPrinting = true;
                _printButtonText = GetPrintButtonText(format);
                StateHasChanged();

                // التحقق من تحميل دوال JavaScript
                var isJsReady = await CheckJavaScriptReady();
                if (!isJsReady)
                {
                    // محاولة استخدام طريقة بديلة
                    await PrintUsingFallbackMethod(format);
                    return;
                }

                // إنشاء HTML للإيصال
                var receiptHtml = GenerateReceiptHtml(format);

                // استدعاء JavaScript لطباعة الإيصال
                var printResult = await _jsRuntime.InvokeAsync<bool>("printReceiptFromBlazor", receiptHtml, format);

                if (printResult)
                {
                    _snackbar.Add($"تم إرسال الإيصال للطباعة بنجاح ({GetFormatDisplayName(format)})", Severity.Success);
                }
                else
                {
                    _snackbar.Add("فشل في طباعة الإيصال. تأكد من إعدادات الطابعة", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                _snackbar.Add($"خطأ في طباعة الإيصال: {ex.Message}", Severity.Error);
            }
            finally
            {
                _isPrinting = false;
                _printButtonText = "طباعة الإيصال";
                StateHasChanged();
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات الإيصال قبل الطباعة
        /// </summary>
        private bool ValidateReceiptForPrint()
        {
            if (receipt.Value <= 0)
            {
                _snackbar.Add("لا يمكن طباعة إيصال بقيمة صفر أو سالبة", Severity.Warning);
                return false;
            }

            if (receipt.Financial == null)
            {
                _snackbar.Add("يجب تحديد نوع العملية قبل الطباعة", Severity.Warning);
                return false;
            }

            // التحقق من بيانات المتجر
            if (_defaultShopSettings == null)
            {
                _snackbar.Add("تحذير: لم يتم تحميل إعدادات المتجر. سيتم استخدام قيم افتراضية", Severity.Warning);
            }
            else if (string.IsNullOrEmpty(_defaultShopSettings.StoreName))
            {
                _snackbar.Add("تحذير: اسم المتجر غير محدد في الإعدادات", Severity.Warning);
            }

            return true;
        }

        /// <summary>
        /// معاينة الإيصال قبل الطباعة
        /// </summary>
        private async Task PreviewReceipt()
        {
            try
            {
                // التحقق من وجود بيانات الإيصال
                if (receipt.Value <= 0)
                {
                    _snackbar.Add("لا يمكن معاينة إيصال بقيمة صفر أو سالبة", Severity.Warning);
                    return;
                }

                // إعادة تحميل إعدادات المتجر قبل المعاينة لضمان الحصول على أحدث البيانات
                await RefreshShopSettings();

                // التحقق من تحميل دوال JavaScript
                var isJsReady = await CheckJavaScriptReady();
                if (!isJsReady)
                {
                    // استخدام طريقة بديلة للمعاينة
                    await PreviewUsingFallbackMethod();
                    return;
                }

                // إنشاء HTML للإيصال
                var receiptHtml = GenerateReceiptHtml("preview");

                // استدعاء JavaScript لمعاينة الإيصال
                var previewResult = await _jsRuntime.InvokeAsync<bool>("previewReceiptFromBlazor", receiptHtml, "auto");

                if (!previewResult)
                {
                    _snackbar.Add("فشل في فتح معاينة الإيصال. تأكد من السماح للنوافذ المنبثقة", Severity.Error);
                }
                else
                {
                    _snackbar.Add("تم فتح معاينة الإيصال في نافذة جديدة", Severity.Info);
                }
            }
            catch (Exception ex)
            {
                _snackbar.Add($"خطأ في معاينة الإيصال: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// التحقق من تحميل دوال JavaScript المطلوبة
        /// </summary>
        private async Task<bool> CheckJavaScriptReady()
        {
            try
            {
                // التحقق من وجود دالة الطباعة
                var printFunctionExists = await _jsRuntime.InvokeAsync<bool>("eval", "typeof printReceiptFromBlazor === 'function'");

                // التحقق من وجود دالة المعاينة
                var previewFunctionExists = await _jsRuntime.InvokeAsync<bool>("eval", "typeof previewReceiptFromBlazor === 'function'");

                return printFunctionExists && previewFunctionExists;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// طباعة باستخدام طريقة بديلة عند عدم تحميل مكتبة Print.js
        /// </summary>
        private async Task PrintUsingFallbackMethod(string format)
        {
            try
            {
                // إنشاء HTML للإيصال
                var receiptHtml = GenerateReceiptHtml(format);

                // إنشاء صفحة HTML كاملة للطباعة
                var fullHtml = $@"
                    <!DOCTYPE html>
                    <html dir='rtl' lang='ar'>
                    <head>
                        <meta charset='UTF-8'>
                        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                        <title>طباعة الإيصال</title>
                        <style>
                            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
                            * {{
                                box-sizing: border-box;
                                -webkit-print-color-adjust: exact;
                                print-color-adjust: exact;
                            }}
                            body {{
                                font-family: 'Noto Sans Arabic', Arial, sans-serif;
                                direction: rtl;
                                margin: 0;
                                padding: 20px;
                                -webkit-print-color-adjust: exact;
                                print-color-adjust: exact;
                            }}
                            .receipt-container {{ max-width: 400px; margin: 0 auto; }}
                            @media print {{
                                * {{
                                    -webkit-print-color-adjust: exact !important;
                                    print-color-adjust: exact !important;
                                    color-adjust: exact !important;
                                }}
                                body {{ margin: 0 !important; padding: 0 !important; background: white !important; }}
                                .receipt-container {{
                                    width: 190mm !important;
                                    min-height: 135mm !important;
                                    max-height: 148mm !important;
                                    margin: 10mm auto !important;
                                    padding: 8mm !important;
                                    background: white !important;

                                    border: 4px solid #000000 !important;
                                    border-left: 4px solid #000000 !important;
                                    border-right: 4px solid #000000 !important;
                                    border-top: 4px solid #000000 !important;
                                    border-bottom: 4px solid #000000 !important;

                                    outline: 2px solid #000000 !important;
                                    outline-offset: -2px !important;
                                }}
                                @page {{ size: A4 portrait !important; margin: 5mm !important; }}
                            }}
                        </style>
                    </head>
                    <body>
                        {receiptHtml}
                        <script>
                            window.onload = function() {{
                                setTimeout(function() {{
                                    window.print();
                                }}, 500);
                            }};
                        </script>
                    </body>
                    </html>";

                // فتح نافذة جديدة للطباعة
                await _jsRuntime.InvokeVoidAsync("eval", $@"
                    var printWindow = window.open('', '_blank', 'width=800,height=600');
                    printWindow.document.write(`{fullHtml.Replace("`", "\\`")}`);
                    printWindow.document.close();
                ");

                _snackbar.Add("تم فتح نافذة الطباعة (طريقة بديلة)", Severity.Success);
            }
            catch (Exception ex)
            {
                _snackbar.Add($"خطأ في الطباعة البديلة: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// معاينة باستخدام طريقة بديلة
        /// </summary>
        private async Task PreviewUsingFallbackMethod()
        {
            try
            {
                // إنشاء HTML للإيصال
                var receiptHtml = GenerateReceiptHtml("preview");

                // إنشاء صفحة HTML كاملة للمعاينة
                var fullHtml = $@"
                    <!DOCTYPE html>
                    <html dir='rtl' lang='ar'>
                    <head>
                        <meta charset='UTF-8'>
                        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                        <title>معاينة الإيصال</title>
                        <style>
                            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
                            * {{
                                box-sizing: border-box;
                                -webkit-print-color-adjust: exact;
                                print-color-adjust: exact;
                            }}
                            body {{
                                font-family: 'Noto Sans Arabic', Arial, sans-serif;
                                direction: rtl;
                                margin: 0;
                                padding: 20px;
                                background: #f5f5f5;
                                -webkit-print-color-adjust: exact;
                                print-color-adjust: exact;
                            }}
                            .receipt-container {{
                                max-width: 400px;
                                margin: 0 auto;
                                background: white;
                                padding: 20px;
                                border-radius: 8px;
                                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                                border: 1px solid #ddd;
                            }}
                            .actions {{ text-align: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; }}
                            .btn {{ padding: 10px 20px; margin: 0 5px; border: none; border-radius: 4px; cursor: pointer; font-family: 'Noto Sans Arabic', Arial, sans-serif; }}
                            .btn-print {{ background: #007bff; color: white; }}
                            .btn-close {{ background: #6c757d; color: white; }}
                            @media print {{
                                * {{
                                    -webkit-print-color-adjust: exact !important;
                                    print-color-adjust: exact !important;
                                    color-adjust: exact !important;
                                }}
                                body {{ margin: 0 !important; padding: 0 !important; background: white !important; }}
                                .receipt-container {{
                                    width: 190mm !important;
                                    min-height: 135mm !important;
                                    max-height: 148mm !important;
                                    margin: 10mm auto !important;
                                    padding: 8mm !important;
                                    background: white !important;
                                    box-shadow: none !important;
                                    border-radius: 0 !important;

                                    border: 4px solid #000000 !important;
                                    border-left: 4px solid #000000 !important;
                                    border-right: 4px solid #000000 !important;
                                    border-top: 4px solid #000000 !important;
                                    border-bottom: 4px solid #000000 !important;

                                    outline: 2px solid #000000 !important;
                                    outline-offset: -2px !important;
                                }}
                                .actions {{ display: none !important; }}
                                @page {{ size: A4 portrait !important; margin: 5mm !important; }}
                            }}
                        </style>
                    </head>
                    <body>
                        {receiptHtml}
                        <div class='actions'>
                            <button class='btn btn-print' onclick='window.print()'>طباعة</button>
                            <button class='btn btn-close' onclick='window.close()'>إغلاق</button>
                        </div>
                    </body>
                    </html>";

                // فتح نافذة جديدة للمعاينة
                await _jsRuntime.InvokeVoidAsync("eval", $@"
                    var previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
                    previewWindow.document.write(`{fullHtml.Replace("`", "\\`")}`);
                    previewWindow.document.close();
                ");

                _snackbar.Add("تم فتح معاينة الإيصال (طريقة بديلة)", Severity.Success);
            }
            catch (Exception ex)
            {
                _snackbar.Add($"خطأ في المعاينة البديلة: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// الحصول على نص زر الطباعة حسب التنسيق
        /// </summary>
        private string GetPrintButtonText(string format)
        {
            return "جاري طباعة A4...";
        }

        /// <summary>
        /// الحصول على اسم التنسيق للعرض
        /// </summary>
        private string GetFormatDisplayName(string format)
        {
            return "A4";
        }

        /// <summary>
        /// إنشاء HTML للإيصال مع التصميم المحسن - نصف صفحة A4 مع حواف قوية
        /// الأبعاد: 190mm × 135-148mm مع هوامش داخلية 8mm
        /// </summary>
        private string GenerateReceiptHtml(string format = "auto")
        {
            // إعداد بيانات المتجر
            var logoUrl = "";
            var storeName = _defaultShopSettings?.StoreName ?? "اسم المتجر";
            var companyName = _defaultShopSettings?.CompanyName ?? "اسم الشركة";
            var companyPhone = _defaultShopSettings?.CompanyPhone ?? "رقم الهاتف";
            var storeAddress = _defaultShopSettings?.StoreAddress ?? "عنوان المتجر";

            // تحويل مسار الشعار إلى URL كامل
            if (!string.IsNullOrEmpty(_defaultShopSettings?.LogoPath))
            {
                logoUrl = GetFullLogoUrl(_defaultShopSettings.LogoPath);

                // التحقق من صحة URL
                if (!IsValidImageUrl(logoUrl))
                {
                    logoUrl = ""; // إعادة تعيين إذا كان URL غير صحيح
                }
            }

            var receiptTypeText = receipt.IsExchange == true ? "إيصال صرف نقدية" : "إيصال قبض نقدية";
            var operationType = receipt.Financial?.Name ?? "غير محدد";
            var clientName = GetClientOrEmployeeName();
            var currentDate = receipt.Date?.ToString("yyyy/MM/dd") ?? DateTime.Now.ToString("yyyy/MM/dd");
            var currentTime = DateTime.Now.ToString("HH:mm");

            return $@"
                <style>
                    @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

                    body {{
                        font-family: 'Cairo', Arial, sans-serif;
                        direction: rtl;
                        margin: 0;
                        padding: 20px;
                        background: #f5f5f5;
                        font-size: 14px;
                        line-height: 1.4;
                    }}

                    .receipt-container {{
                        width: 190mm;
                        min-height: 135mm;
                        max-height: 148mm;
                        margin: 10mm auto;
                        background: #ffffff;
                        border: 3px solid #000;
                        position: relative;
                        overflow: visible;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        display: flex;
                        flex-direction: column;
                        padding: 8mm;
                    }}

                    /* الشريط العلوي الأزرق */
                    .top-blue-stripe {{
                        height: 8px;
                        background: linear-gradient(90deg, #1e40af 0%, #3b82f6 60%, transparent 100%);
                        width: 65%;
                    }}

                    /* الهيدر */
                    .receipt-header {{
                        padding: 15px 20px;
                        position: relative;
                        background: #ffffff;
                    }}

                    /* شعار الشركة في الزاوية اليمنى */
                    .company-logo {{
                        position: absolute;
                        top: 15px;
                        right: 20px;
                        width: 70px;
                        height: 70px;
                        border-radius: 4px;
                        overflow: hidden;
                        border: 2px solid #e2e8f0;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    }}

                    .company-logo img {{
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        background: white;
                    }}

                    .company-logo .logo-placeholder {{
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 100%;
                        height: 100%;
                        background: #1e40af;
                        color: white;
                        font-size: 10px;
                        font-weight: bold;
                        text-align: center;
                        line-height: 1.2;
                        padding: 4px;
                        box-sizing: border-box;
                    }}

                    /* معلومات الشركة في الوسط */
                    .company-info {{
                        text-align: center;
                        margin: 0 90px;
                        padding-top: 10px;
                    }}

                    .store-name {{
                        font-size: 18px;
                        font-weight: 700;
                        color: #1e293b;
                        margin-bottom: 5px;
                    }}

                    .company-details {{
                        font-size: 11px;
                        color: #64748b;
                        line-height: 1.3;
                    }}

                    /* التاريخ في الزاوية اليسرى */
                    .date-section {{
                        position: absolute;
                        top: 15px;
                        left: 20px;
                        background: #1e40af;
                        color: white;
                        padding: 8px 12px;
                        border-radius: 6px;
                        font-size: 12px;
                        font-weight: 600;
                        text-align: center;
                        min-width: 80px;
                    }}

                    /* عنوان الإيصال */
                    .receipt-title {{
                        text-align: center;
                        font-size: 20px;
                        font-weight: 700;
                        color: #1e40af;
                        margin: 20px 0 15px 0;
                        padding-bottom: 10px;
                        border-bottom: 2px solid #1e40af;
                    }}

                    .receipt-number {{
                        text-align: center;
                        font-size: 14px;
                        color: #64748b;
                        margin-bottom: 15px;
                    }}

                    /* محتوى الإيصال */
                    .receipt-body {{
                        padding: 10px 20px;
                        flex-grow: 1;
                    }}

                    /* الحقول */
                    .field-row {{
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 8px;
                        padding: 5px 0;
                    }}

                    .field-label {{
                        font-size: 13px;
                        color: #64748b;
                        font-weight: 500;
                        min-width: 80px;
                    }}

                    .field-value {{
                        font-size: 14px;
                        color: #1e293b;
                        font-weight: 600;
                        background: #f8fafc;
                        padding: 6px 12px;
                        border-radius: 4px;
                        border: 1px solid #e2e8f0;
                        flex: 1;
                        margin-right: 10px;
                        text-align: center;
                    }}

                    /* حقل مزدوج */
                    .dual-field {{
                        display: flex;
                        gap: 15px;
                        margin-bottom: 10px;
                        direction: rtl;
                    }}

                    .dual-field .field-half {{
                        flex: 1;
                    }}

                    .dual-field .field-half .field-label {{
                        font-size: 13px;
                        color: #333;
                        font-weight: 600;
                        margin-bottom: 5px;
                        text-align: right;
                    }}

                    .dual-field .field-half .field-value {{
                        background: #f0f0f0;
                        border: 1px solid #ccc;
                        padding: 8px 12px;
                        font-size: 14px;
                        color: #333;
                        width: 100%;
                        box-sizing: border-box;
                        margin-right: 0;
                    }}

                    /* قسم التوقيع والختم */
                    .signature-section {{
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-end;
                        padding: 15px 30px 10px 30px;
                        margin-top: 15px;
                        border-top: 1px solid #e2e8f0;
                        direction: rtl;
                        flex-shrink: 0;
                    }}

                    .signature-box {{
                        text-align: center;
                        width: 100px;
                    }}

                    .signature-label {{
                        font-size: 11px;
                        color: #64748b;
                        font-weight: 500;
                        margin-bottom: 5px;
                    }}

                    .signature-line {{
                        border-bottom: 1px solid #1e40af;
                        height: 30px;
                        margin-bottom: 3px;
                        position: relative;
                    }}

                    .stamp-box {{
                        text-align: center;
                        width: 100px;
                    }}

                    .stamp-label {{
                        font-size: 11px;
                        color: #64748b;
                        font-weight: 500;
                        margin-bottom: 5px;
                    }}

                    .stamp-area {{
                        border: 1px dashed #1e40af;
                        height: 40px;
                        width: 80px;
                        margin: 0 auto;
                        border-radius: 6px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 9px;
                        color: #64748b;
                        background: #f8fafc;
                    }}

                    /* تنسيقات الطباعة */
                    @media print {{
                        * {{
                            -webkit-print-color-adjust: exact !important;
                            print-color-adjust: exact !important;
                            color-adjust: exact !important;
                        }}

                        body {{
                            margin: 0 !important;
                            padding: 0 !important;
                            background: white !important;
                            -webkit-print-color-adjust: exact !important;
                            print-color-adjust: exact !important;
                        }}

                        .receipt-container {{
                            width: 190mm !important;
                            min-height: 135mm !important;
                            max-height: 148mm !important;
                            margin: 10mm auto !important;
                            padding: 8mm !important;
                            background: white !important;
                            box-shadow: none !important;
                            position: relative !important;
                            overflow: visible !important;

                            /* حواف قوية ومرئية */
                            border: 4px solid #000000 !important;
                            border-left: 4px solid #000000 !important;
                            border-right: 4px solid #000000 !important;
                            border-top: 4px solid #000000 !important;
                            border-bottom: 4px solid #000000 !important;

                            /* إضافة outline كطبقة إضافية */
                            outline: 2px solid #000000 !important;
                            outline-offset: -2px !important;
                        }}

                        /* تحسين عرض الشعار في الطباعة */
                        .company-logo {{
                            border: 2px solid #000000 !important;
                            background: white !important;
                        }}

                        .company-logo img {{
                            -webkit-print-color-adjust: exact !important;
                            print-color-adjust: exact !important;
                            color-adjust: exact !important;
                        }}

                        .logo-placeholder {{
                            background: #1e40af !important;
                            color: white !important;
                            -webkit-print-color-adjust: exact !important;
                            print-color-adjust: exact !important;
                            color-adjust: exact !important;
                        }}

                        /* إخفاء أزرار الطباعة والإغلاق */
                        .mud-button, .mud-fab, .mud-dialog-actions,
                        .print-button, .close-button, .preview-button,
                        button, .btn, [role=""button""] {{
                            display: none !important;
                        }}

                        @page {{
                            size: A4 portrait !important;
                            margin: 5mm !important;
                        }}
                    }}

                    /* للعرض على الشاشة */
                    @media screen {{
                        .receipt-container {{
                            transform: scale(0.8);
                            transform-origin: top center;
                            margin-top: 20px;
                            margin-bottom: 40px;
                        }}
                    }}
                </style>

                <div class='receipt-container'>
                    <!-- الشريط الأزرق العلوي -->
                    <div class='top-blue-stripe'></div>

                    <div class='receipt-header'>
                        <!-- شعار الشركة -->
                        <div class='company-logo'>
                            {GenerateLogoHtml(logoUrl, storeName)}
                        </div>

                        <!-- التاريخ -->
                        <div class='date-section'>
                            التاريخ: {currentDate}
                        </div>

                        <!-- معلومات الشركة في الوسط -->
                        <div class='company-info'>
                            <div class='store-name'>{storeName}</div>
                            <div class='company-details'>
                                {(!string.IsNullOrEmpty(companyName) ? $"{companyName}<br>" : "")}
                                {(!string.IsNullOrEmpty(companyPhone) ? $"هاتف: {companyPhone}<br>" : "")}
                                {(!string.IsNullOrEmpty(storeAddress) ? $"{storeAddress}" : "")}
                            </div>
                        </div>

                        <!-- عنوان الإيصال -->
                        <div class='receipt-title'>{receiptTypeText}</div>
                        <div class='receipt-number'>رقم {receipt.ReceiptNo}</div>
                    </div>

                    <!-- محتوى الإيصال -->
                    <div class='receipt-body'>
                        <!-- لصالح الأمر -->
                        <div class='field-row'>
                            <span class='field-label'>لصالح الأمر:</span>
                            <div class='field-value'>{clientName}</div>
                        </div>

                        <!-- نوع العملية -->
                        <div class='field-row'>
                            <span class='field-label'>نوع العملية:</span>
                            <div class='field-value'>{operationType}</div>
                        </div>

                        <!-- حقل مزدوج: المبلغ ونوع الدفع -->
                        <div class='dual-field'>
                            <div class='field-half'>
                                <div class='field-label'>مبلغ وقدره:</div>
                                <div class='field-value'>{receipt.Value:N2} ريال</div>
                            </div>
                            <div class='field-half'>
                                <div class='field-label'>نقد أو صك:</div>
                                <div class='field-value'>{(receipt.IsExchange == true ? "صرف نقدي" : "قبض نقدي")}</div>
                            </div>
                        </div>

                        <!-- البيان -->
                        {(!string.IsNullOrEmpty(receipt.Statement) ? $@"
                        <div class='field-row'>
                            <span class='field-label'>البيان:</span>
                            <div class='field-value'>{receipt.Statement}</div>
                        </div>" : "")}
                    </div>

                    <!-- قسم التوقيع والختم -->
                    <div class='signature-section'>
                        <!-- التوقيع على اليمين -->
                        <div class='signature-box'>
                            <div class='signature-label'>التوقيع</div>
                            <div class='signature-line'></div>
                        </div>

                        <!-- الختم على اليسار -->
                        <div class='stamp-box'>
                            <div class='stamp-label'>الختم</div>
                            <div class='stamp-area'>ختم الشركة</div>
                        </div>
                    </div>
                </div>";
        }

        /// <summary>
        /// الحصول على اسم العميل أو الموظف حسب نوع العملية
        /// </summary>
        private string GetClientOrEmployeeName()
        {
            if (receipt.Client != null)
                return receipt.Client.Name;
            if (receipt.Employee != null)
                return receipt.Employee.Name;
            if (receipt.Sell != null && receipt.Sell.Client != null)
                return receipt.Sell.Client.Name;
            if (receipt.Purchase != null && receipt.Purchase.Client != null)
                return receipt.Purchase.Client.Name;

            return "";
        }

        /// <summary>
        /// الحصول على تسمية العميل أو الموظف حسب نوع العملية
        /// </summary>
        private string GetClientOrEmployeeLabel()
        {
            if (receipt.Client != null || (receipt.Sell?.Client != null) || (receipt.Purchase?.Client != null))
                return "العميل";
            if (receipt.Employee != null)
                return "الموظف";

            return "الطرف";
        }

        /// <summary>
        /// تحديد طريقة الدفع
        /// </summary>
        private string GetPaymentMethodText()
        {
            // يمكن تحديد طريقة الدفع بناءً على نوع المعاملة أو إعداد آخر
            // حالياً سنعرض "نقداً" كافتراضي
            return "نقداً";
        }

        /// <summary>
        /// تحويل الرقم إلى كلمات عربية (تنفيذ مبسط)
        /// </summary>
        private string ConvertNumberToArabicWords(decimal number)
        {
            try
            {
                var integerPart = (int)Math.Floor(number);
                var decimalPart = (int)Math.Round((number - integerPart) * 100);

                if (integerPart == 0 && decimalPart == 0)
                    return "صفر";

                var result = "";

                if (integerPart > 0)
                {
                    result = ConvertIntegerToArabicWords(integerPart);
                }

                if (decimalPart > 0)
                {
                    if (!string.IsNullOrEmpty(result))
                        result += " و ";
                    result += ConvertIntegerToArabicWords(decimalPart) + " هللة";
                }

                return result;
            }
            catch
            {
                return number.ToString("N2");
            }
        }

        /// <summary>
        /// تحويل العدد الصحيح إلى كلمات عربية
        /// </summary>
        private string ConvertIntegerToArabicWords(int number)
        {
            if (number == 0) return "صفر";
            if (number == 1) return "واحد";
            if (number == 2) return "اثنان";
            if (number == 3) return "ثلاثة";
            if (number == 4) return "أربعة";
            if (number == 5) return "خمسة";
            if (number == 6) return "ستة";
            if (number == 7) return "سبعة";
            if (number == 8) return "ثمانية";
            if (number == 9) return "تسعة";
            if (number == 10) return "عشرة";

            // للأرقام الأكبر، استخدم تنفيذ مبسط
            if (number < 100)
                return $"{number}";
            if (number < 1000)
                return $"{number}";

            return number.ToString();
        }

        /// <summary>
        /// تحويل مسار الشعار النسبي إلى URL كامل للـ API
        /// نفس الطريقة المستخدمة في صفحة المبيعات
        /// </summary>
        /// <param name="logoPath">مسار الشعار النسبي</param>
        /// <returns>URL كامل للصورة</returns>
        private string GetFullLogoUrl(string? logoPath)
        {
            if (string.IsNullOrEmpty(logoPath))
            {
                return string.Empty;
            }

            // إذا كان المسار يبدأ بـ http أو https، فهو مسار كامل
            if (logoPath.StartsWith("http://") || logoPath.StartsWith("https://"))
            {
                return logoPath;
            }

            // إذا كان المسار يبدأ بـ data:، فهو base64
            if (logoPath.StartsWith("data:"))
            {
                return logoPath;
            }

            // URL الأساسي للـ API (يجب أن يتطابق مع إعدادات النظام)
            var apiBaseUrl = "https://localhost:7282";

            // تحويل المسار النسبي إلى URL كامل للـ API
            // مثال: /logo/image.png -> https://localhost:7282/logo/image.png
            var fullPath = logoPath.StartsWith("/") ? logoPath : "/" + logoPath;
            return apiBaseUrl + fullPath;
        }

        /// <summary>
        /// التحقق من صحة URL الصورة
        /// </summary>
        /// <param name="url">رابط الصورة</param>
        /// <returns>true إذا كان الرابط صحيح</returns>
        private bool IsValidImageUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
                return false;

            // التحقق من صيغ الصور المدعومة
            var supportedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" };

            // إذا كان base64، فهو صحيح
            if (url.StartsWith("data:image/"))
                return true;

            try
            {
                var uri = new Uri(url);
                var extension = Path.GetExtension(uri.AbsolutePath).ToLowerInvariant();
                return supportedExtensions.Contains(extension);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إنشاء HTML للشعار مع معالجة حالات الخطأ
        /// </summary>
        /// <param name="logoUrl">رابط الشعار</param>
        /// <param name="storeName">اسم المتجر</param>
        /// <returns>HTML للشعار</returns>
        private string GenerateLogoHtml(string logoUrl, string storeName)
        {
            var safeStoreName = storeName.Replace("\"", "&quot;").Replace("'", "&#39;");
            var shortStoreName = safeStoreName.Length > 15 ? safeStoreName.Substring(0, 15) : safeStoreName;

            if (!string.IsNullOrEmpty(logoUrl))
            {
                // إذا كان هناك شعار، اعرضه مع fallback للنص
                return $@"
                    <img src='{logoUrl}'
                         alt='شعار {safeStoreName}'
                         style='width:100%;height:100%;object-fit:contain;background:white;'
                         onerror='this.style.display=""none""; this.parentElement.innerHTML=""<div class=\""logo-placeholder\"">{shortStoreName}</div>"";' />
                ";
            }
            else
            {
                // إذا لم يكن هناك شعار، اعرض النص فقط
                return $"<div class='logo-placeholder'>{shortStoreName}</div>";
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات المتجر
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        private bool ValidateStoreSettings()
        {
            if (_defaultShopSettings == null)
            {
                _snackbar.Add("لم يتم تحميل إعدادات المتجر", Severity.Warning);
                return false;
            }

            if (string.IsNullOrEmpty(_defaultShopSettings.StoreName))
            {
                _snackbar.Add("اسم المتجر غير محدد في الإعدادات", Severity.Warning);
                return false;
            }

            return true;
        }

        void Cancel() => MudDialog.Cancel();

    }
}