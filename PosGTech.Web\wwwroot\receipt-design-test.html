<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تصميم الإيصال الجديد - نصف صفحة A4</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 10px;
            background: #f5f5f5;
            font-size: 14px;
            line-height: 1.4;
        }

        .receipt-container {
            width: 210mm;  /* عرض A4 */
            height: 148.5mm;  /* نصف ارتفاع A4 (297mm ÷ 2) */
            margin: 0 auto;
            background: #ffffff;
            border: 1px solid #ddd;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        /* الشريط العلوي الأزرق */
        .top-blue-line {
            height: 6px;
            background: linear-gradient(90deg, #1e40af 0%, #3b82f6 70%, transparent 100%);
            width: 70%;
            margin-bottom: 15px;
        }
        
        /* الهيدر */
        .receipt-header {
            padding: 15px 20px;
            position: relative;
            background: #ffffff;
        }
        
        /* شعار الشركة في الزاوية اليمنى */
        .company-logo {
            position: absolute;
            top: 15px;
            right: 20px;
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            line-height: 1.2;
        }
        
        /* معلومات الشركة في الوسط */
        .company-info {
            text-align: center;
            margin: 0 90px;
            padding-top: 10px;
        }
        
        .store-name {
            font-size: 18px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }
        
        .company-details {
            font-size: 11px;
            color: #64748b;
            line-height: 1.3;
        }
        
        /* التاريخ في الزاوية اليسرى */
        .date-section {
            position: absolute;
            top: 15px;
            left: 20px;
            background: #1e40af;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            min-width: 80px;
        }
        
        /* عنوان الإيصال */
        .receipt-title {
            text-align: center;
            font-size: 20px;
            font-weight: 700;
            color: #1e40af;
            margin: 20px 0 15px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #1e40af;
        }
        
        /* محتوى الإيصال */
        .receipt-body {
            padding: 15px 20px;
        }
        
        /* الحقول */
        .field-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px 0;
        }
        
        .field-label {
            font-size: 13px;
            color: #64748b;
            font-weight: 500;
            min-width: 80px;
        }
        
        .field-value {
            font-size: 14px;
            color: #1e293b;
            font-weight: 600;
            background: #f8fafc;
            padding: 6px 12px;
            border-radius: 4px;
            border: 1px solid #e2e8f0;
            flex: 1;
            margin-right: 10px;
            text-align: center;
        }
        
        /* حقل البيان */
        .statement-field {
            margin: 15px 0;
        }
        
        .statement-label {
            font-size: 13px;
            color: #64748b;
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
        }
        
        .statement-value {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 10px;
            font-size: 13px;
            color: #1e293b;
            min-height: 40px;
            width: 100%;
        }
        
        /* قسم المبلغ */
        .amount-section {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        
        .amount-label {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
            opacity: 0.9;
        }
        
        .amount-value {
            font-size: 24px;
            font-weight: 800;
            margin-bottom: 5px;
        }
        
        .amount-words {
            font-size: 11px;
            opacity: 0.8;
            font-style: italic;
        }
        
        /* الفوتر */
        .receipt-footer {
            padding: 15px 20px;
            text-align: center;
            border-top: 2px solid #1e40af;
            background: #f8fafc;
            margin-top: 20px;
        }
        
        .footer-message {
            font-size: 14px;
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 8px;
        }
        
        .footer-details {
            font-size: 10px;
            color: #64748b;
            line-height: 1.4;
        }
        
        .test-controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .test-btn {
            padding: 12px 25px;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', Arial, sans-serif;
            font-weight: 600;
            margin: 0 10px;
            box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(30, 64, 175, 0.4);
        }
        
        /* تنسيقات الطباعة */
        @media print {
            body {
                margin: 0;
                padding: 0;
                background: white;
            }

            .receipt-container {
                border: 1px solid #000;
                box-shadow: none;
                width: 210mm;
                height: 148.5mm;
                margin: 0;
            }

            .test-controls {
                display: none;
            }

            @page {
                size: A4 landscape;
                margin: 5mm;
            }
        }

        /* للعرض على الشاشة - محاكاة نصف صفحة A4 */
        @media screen {
            .receipt-container {
                transform: scale(0.8);
                transform-origin: top center;
                margin-top: 20px;
                margin-bottom: 40px;
            }
        }
        
        /* للطباعة الحرارية */
        @media print and (max-width: 80mm) {
            .receipt-container {
                max-width: 72mm;
                font-size: 11px;
            }
            
            .company-logo {
                width: 50px;
                height: 50px;
                font-size: 10px;
            }
            
            .receipt-title {
                font-size: 16px;
            }
            
            .amount-value {
                font-size: 18px;
            }
            
            .field-value {
                font-size: 11px;
                padding: 4px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <button class="test-btn" onclick="window.print()">🖨️ طباعة الإيصال</button>
        <button class="test-btn" onclick="location.reload()">🔄 إعادة تحميل</button>
    </div>

    <div class="receipt-container">
        <div class="top-blue-line"></div>

        <div class="receipt-header">
            <!-- شعار الشركة -->
            <div class="company-logo">
                أضف<br>شعارك
            </div>

            <!-- التاريخ -->
            <div class="date-section">
                التاريخ:<br>2025/07/05
            </div>

            <!-- معلومات الشركة -->
            <div class="company-info">
                <div class="store-name">اسم المتجر</div>
                <div class="company-details">
                    📍 عنوان المتجر<br>
                    📞 رقم الهاتف<br>
                    🏢 اسم الشركة
                </div>
            </div>
        </div>

        <!-- عنوان الإيصال -->
        <div class="receipt-title">إيصال صرف نقدية</div>

        <!-- محتوى الإيصال -->
        <div class="receipt-body">
            <!-- رقم الإيصال -->
            <div class="field-row">
                <span class="field-label">رقم الإيصال</span>
                <div class="field-value">4</div>
            </div>

            <!-- نوع العملية -->
            <div class="field-row">
                <span class="field-label">نوع العملية</span>
                <div class="field-value">فاتورة مبيعات</div>
            </div>

            <!-- العميل -->
            <div class="field-row">
                <span class="field-label">العميل</span>
                <div class="field-value">زبون نقدي</div>
            </div>

            <!-- الوقت -->
            <div class="field-row">
                <span class="field-label">الوقت</span>
                <div class="field-value">21:50</div>
            </div>

            <!-- بيان الإيصال -->
            <div class="statement-field">
                <span class="statement-label">بيان الإيصال</span>
                <div class="statement-value">وصف قيمة</div>
            </div>

            <!-- المبلغ -->
            <div class="amount-section">
                <div class="amount-label">المبلغ الإجمالي</div>
                <div class="amount-value">52.00 ريال</div>
                <div class="amount-words">(52 ريال سعودي)</div>
            </div>
        </div>

        <!-- الفوتر -->
        <div class="receipt-footer">
            <div class="footer-message">🙏 شكراً لتعاملكم معنا</div>
            <div class="footer-details">
                💻 نظام إدارة نقاط البيع - اسم الشركة<br>
                نتطلع لخدمتكم دائماً
            </div>
        </div>
    </div>

</body>
</html>
