﻿using AutoMapper;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.Purchases;
using PosGTech.ModelsDTO.Treasury;

namespace PosGTech.DataAccess.Repository
{
    public class TreasuryRepository : Repository<Treasury>, ITreasuryRepository
    {
        public TreasuryRepository(ApplicationDbContext db) : base(db)
        {
        }

        public async Task<IEnumerable<TreasuryCMDTO>> GetAllTreasurye()
        {

            return await Select(x => new TreasuryCMDTO()
            {
                Id = x.Id,
                Name = x.Name               
            });


        }
        public void UpdateTreasury(ref Treasury oldeTreasury, Treasury newTreasury)
        {
         
            oldeTreasury.Name = newTreasury.Name;
            oldeTreasury.UpdatedAt = DateTime.Now;
            Update(oldeTreasury);
        }
    }
}
