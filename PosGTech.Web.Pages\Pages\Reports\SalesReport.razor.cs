using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using PosGTech.ModelsDTO.Sells;
using PosGTech.ModelsDTO.Stores;
using PosGTech.Web.Services.Interfaces;
using System.Text.Json;

namespace PosGTech.Web.Pages.Pages.Reports
{
    public partial class SalesReport
    {
        [Inject] HttpClient _httpClient { get; set; }
        [Inject] IGRepository<StoreCMDTO> _storeService { get; set; }
        [Inject] ISnackbar Snackbar { get; set; }
        [Inject] AuthenticationStateProvider _auth { get; set; }

        // Filter properties
        private DateTime? fromDate = DateTime.Today.AddDays(-30);
        private DateTime? toDate = DateTime.Today;
        private Guid? selectedStoreId = null;

        // Data properties
        private SalesReportDTO? salesReport = null;
        private List<StoreCMDTO> stores = new();
        private bool loading = false;

        // Search and selection
        private string searchString = "";
        private SalesReportDetailDTO? selectedSale = null;

        protected override async Task OnInitializedAsync()
        {
            await LoadStores();
            await LoadReport();
        }

        private async Task LoadStores()
        {
            try
            {
                var response = await _storeService.GetAll("Stores/getAllStoresCMB");
                if (response.response == null)
                {
                    stores = response.list?.ToList() ?? new List<StoreCMDTO>();
                }
                else
                {
                    Snackbar.Add($"خطأ في تحميل المخازن: {response.response.Message}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"خطأ في تحميل المخازن: {ex.Message}", Severity.Error);
            }
        }

        private async Task LoadReport()
        {
            if (fromDate == null || toDate == null)
            {
                Snackbar.Add("يرجى تحديد تاريخ البداية والنهاية", Severity.Warning);
                return;
            }

            if (fromDate > toDate)
            {
                Snackbar.Add("تاريخ البداية يجب أن يكون أقل من تاريخ النهاية", Severity.Warning);
                return;
            }

            loading = true;
            try
            {
                var fromDateOnly = DateOnly.FromDateTime(fromDate.Value);
                var toDateOnly = DateOnly.FromDateTime(toDate.Value);

                var queryString = $"fromDate={fromDateOnly:yyyy-MM-dd}&toDate={toDateOnly:yyyy-MM-dd}";
                if (selectedStoreId.HasValue)
                {
                    queryString += $"&storeId={selectedStoreId.Value}";
                }

                var response = await _httpClient.GetAsync($"Sells/getSalesReport?{queryString}");

                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    salesReport = JsonSerializer.Deserialize<SalesReportDTO>(jsonContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    Snackbar.Add("تم تحميل التقرير بنجاح", Severity.Success);
                }
                else
                {
                    Snackbar.Add("خطأ في تحميل التقرير من الخادم", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"خطأ في تحميل التقرير: {ex.Message}", Severity.Error);
            }
            finally
            {
                loading = false;
            }
        }

        private bool FilterSalesDetails(SalesReportDetailDTO sale)
        {
            if (string.IsNullOrWhiteSpace(searchString))
                return true;

            return sale.InvoiceNo.ToString().Contains(searchString, StringComparison.OrdinalIgnoreCase) ||
                   sale.Client.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase) ||
                   sale.StoreName.Contains(searchString, StringComparison.OrdinalIgnoreCase);
        }


    }
}
