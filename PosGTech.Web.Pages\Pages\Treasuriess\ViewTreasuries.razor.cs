﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.Treasury;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Treasuriess
{
    public partial class ViewTreasuries
    {
        [Inject]
        IDialogService DialogService { get; set; }
        [Inject]
        IGRepository<TreasuryCMDTO> _Treasury { get; set; }
        IEnumerable<TreasuryCMDTO> _Treasurylist = new List<TreasuryCMDTO>();
        TreasuryCMDTO selectedItem = null;
        bool loading = true;
        string Search = "";
        string NameItemForDelete;
        MudMessageBox mbox { get; set; }

        protected override async Task OnInitializedAsync() => await LoadingData();

        private bool FilterFunc1(TreasuryCMDTO element) => FilterFunc(element, Search);

        private bool FilterFunc(TreasuryCMDTO element, string searchString)
        {
            if (string.IsNullOrWhiteSpace(searchString))
                return true;
            if (element.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;

            return false;
        }
        async void Upsert(Guid id)
        {
            var parameters = new DialogParameters<UpsertTreasuries>();
            parameters.Add(x => x.id, id);
            var options = new DialogOptions() { CloseButton = false, MaxWidth = MaxWidth.ExtraLarge, FullWidth = true };
            var result = await DialogService.Show<UpsertTreasuries>(id != Guid.Empty ? "تعديل " : "إضافة ", parameters, options).Result;
            if ((string?)result.Data != null) await LoadingData();
        }
        async void Delete(TreasuryCMDTO obj)
        {
            NameItemForDelete = obj.Name;
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                var response = await _Treasury.Delete("Treasuries/deleteTreasury", obj.Id);
                if (response.State)
                {
                    _snackbar.Add(response.Message, Severity.Success);
                    await LoadingData();
                }
                else _snackbar.Add(response.Message, Severity.Error);
            }
        }
        async Task LoadingData()
        {
            loading = true;
            StateHasChanged();
            var res = await _Treasury.GetAll("Treasuries/getAllTreasury");
            if (res.response == null) _Treasurylist = res.list;
            else _snackbar.Add(res.response.Message, Severity.Error);
            loading = false;
            StateHasChanged();
        }
    }
}