﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول فواتير المشتريات
    /// </summary>
    public class ReturnPurchase : BaseEntity
    {

        public int InvoiceNo { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public DateOnly Date { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
        public decimal Total { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
        public decimal FinalTotal { get; set; }
        public string? Note { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid? ClientId { get; set; }
        public Client? Client { get; set; }
        public ICollection<ReturnPurchaseItem>? ReturnPurchaseItems { get; set; }
        public ICollection<Receipt>? Receipts { get; set; }
    }
}
