using System.Reflection;

namespace PosGTech.ModelsDTO.Authorization;

/// <summary>
/// فئة ثابتة تحتوي على جميع الصلاحيات المطلوبة في النظام
/// هذه الصلاحيات ثابتة ولا تتغير إلا بتحديث الكود
/// </summary>
public static class PermissionConstants
{
    #region Items Management - إدارة الأصناف
    public const string ItemsView = "Items.View";
    public const string ItemsAdd = "Items.Add";
    public const string ItemsEdit = "Items.Edit";
    public const string ItemsDelete = "Items.Delete";
    #endregion

    #region Categories Management - إدارة التصنيفات
    public const string CategoriesView = "Categories.View";
    public const string CategoriesAdd = "Categories.Add";
    public const string CategoriesEdit = "Categories.Edit";
    public const string CategoriesDelete = "Categories.Delete";
    #endregion

    #region Units Management - إدارة الوحدات
    public const string UnitsView = "Units.View";
    public const string UnitsAdd = "Units.Add";
    public const string UnitsEdit = "Units.Edit";
    public const string UnitsDelete = "Units.Delete";
    #endregion

    #region Purchases Management - إدارة المشتريات
    public const string PurchasesView = "Purchases.View";
    public const string PurchasesAdd = "Purchases.Add";
    public const string PurchasesEdit = "Purchases.Edit";
    public const string PurchasesDelete = "Purchases.Delete";
    #endregion

    #region Sales Management - إدارة المبيعات
    public const string SalesView = "Sales.View";
    public const string SalesAdd = "Sales.Add";
    public const string SalesEdit = "Sales.Edit";
    public const string SalesDelete = "Sales.Delete";
    #endregion

    #region Clients Management - إدارة العملاء
    public const string ClientsView = "Clients.View";
    public const string ClientsAdd = "Clients.Add";
    public const string ClientsEdit = "Clients.Edit";
    public const string ClientsDelete = "Clients.Delete";
    #endregion

    #region Employees Management - إدارة الموظفين
    public const string EmployeesView = "Employees.View";
    public const string EmployeesAdd = "Employees.Add";
    public const string EmployeesEdit = "Employees.Edit";
    public const string EmployeesDelete = "Employees.Delete";
    #endregion

    #region Stores Management - إدارة المخازن
    public const string StoresView = "Stores.View";
    public const string StoresAdd = "Stores.Add";
    public const string StoresEdit = "Stores.Edit";
    public const string StoresDelete = "Stores.Delete";
    #endregion

    #region Treasuries Management - إدارة الخزائن
    public const string TreasuriesView = "Treasuries.View";
    public const string TreasuriesAdd = "Treasuries.Add";
    public const string TreasuriesEdit = "Treasuries.Edit";
    public const string TreasuriesDelete = "Treasuries.Delete";
    #endregion

    #region Inventory Management - إدارة الجرد
    public const string InventoryView = "Inventory.View";
    public const string InventoryAdd = "Inventory.Add";
    public const string InventoryEdit = "Inventory.Edit";
    public const string InventoryDelete = "Inventory.Delete";
    #endregion

    #region Receipts Management - إدارة السندات
    public const string ReceiptsView = "Receipts.View";
    public const string ReceiptsAdd = "Receipts.Add";
    public const string ReceiptsEdit = "Receipts.Edit";
    public const string ReceiptsDelete = "Receipts.Delete";
    #endregion

    #region Expenses Management - إدارة المصروفات
    public const string ExpensesView = "Expenses.View";
    public const string ExpensesAdd = "Expenses.Add";
    public const string ExpensesEdit = "Expenses.Edit";
    public const string ExpensesDelete = "Expenses.Delete";
    #endregion

    #region Reports Management - إدارة التقارير
    public const string ReportsView = "Reports.View";
    public const string ReportsGenerate = "Reports.Generate";
    public const string ReportsExport = "Reports.Export";
    public const string ReportsAdvanced = "Reports.Advanced";
    #endregion

    #region Users Management - إدارة المستخدمين
    public const string UsersView = "Users.View";
    public const string UsersAdd = "Users.Add";
    public const string UsersEdit = "Users.Edit";
    public const string UsersDelete = "Users.Delete";
    #endregion

    #region Roles Management - إدارة الأدوار
    public const string RolesView = "Roles.View";
    public const string RolesAdd = "Roles.Add";
    public const string RolesEdit = "Roles.Edit";
    public const string RolesDelete = "Roles.Delete";
    #endregion

    #region System Administration - إدارة النظام
    public const string SystemSettings = "System.Settings";
    public const string SystemBackup = "System.Backup";
    public const string SystemRestore = "System.Restore";
    public const string SystemLogs = "System.Logs";
    #endregion

    /// <summary>
    /// الحصول على جميع الصلاحيات الموجودة في النظام
    /// </summary>
    /// <returns>قائمة بجميع الصلاحيات</returns>
    public static List<string> GetAllPermissions()
    {
        return new List<string>
        {
            // Items Management
            ItemsView, ItemsAdd, ItemsEdit, ItemsDelete,

            // Categories Management
            CategoriesView, CategoriesAdd, CategoriesEdit, CategoriesDelete,

            // Units Management
            UnitsView, UnitsAdd, UnitsEdit, UnitsDelete,

            // Purchases Management
            PurchasesView, PurchasesAdd, PurchasesEdit, PurchasesDelete,

            // Sales Management
            SalesView, SalesAdd, SalesEdit, SalesDelete,

            // Clients Management
            ClientsView, ClientsAdd, ClientsEdit, ClientsDelete,

            // Employees Management
            EmployeesView, EmployeesAdd, EmployeesEdit, EmployeesDelete,

            // Stores Management
            StoresView, StoresAdd, StoresEdit, StoresDelete,

            // Treasuries Management
            TreasuriesView, TreasuriesAdd, TreasuriesEdit, TreasuriesDelete,

            // Inventory Management
            InventoryView, InventoryAdd, InventoryEdit, InventoryDelete,

            // Receipts Management
            ReceiptsView, ReceiptsAdd, ReceiptsEdit, ReceiptsDelete,

            // Expenses Management
            ExpensesView, ExpensesAdd, ExpensesEdit, ExpensesDelete,

            // Reports Management
            ReportsView, ReportsGenerate, ReportsExport, ReportsAdvanced,

            // Users Management
            UsersView, UsersAdd, UsersEdit, UsersDelete,

            // Roles Management
            RolesView, RolesAdd, RolesEdit, RolesDelete,

            // System Administration
            SystemSettings, SystemBackup, SystemRestore, SystemLogs
        };
    }

    /// <summary>
    /// التحقق من وجود صلاحية معينة
    /// </summary>
    /// <param name="permission">الصلاحية المراد التحقق منها</param>
    /// <returns>true إذا كانت الصلاحية موجودة</returns>
    public static bool IsValidPermission(string permission)
    {
        return GetAllPermissions().Contains(permission);
    }
}
