@page "/system-activity"
@using PosGTech.Web.Services.Interfaces
@using MudBlazor
@using Microsoft.AspNetCore.Components
@inject IErrorLoggingService ErrorLoggingService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>نشاط النظام - PosGTech</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <MudPaper Elevation="3" Class="pa-6">
        <div dir="rtl">
            <!-- Header -->
            <div class="d-flex align-center justify-space-between mb-6">
                <div>
                    <MudText Typo="Typo.h4" Color="Color.Primary" Class="mb-2">
                        <MudIcon Icon="Icons.Material.Filled.Timeline" Class="me-2" />
                        نشاط النظام
                    </MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        عرض آخر العمليات والأنشطة في النظام لتحديد مسبب الأخطاء
                    </MudText>
                </div>
                <div>
                    <MudButton Variant="Variant.Filled" 
                              Color="Color.Primary" 
                              StartIcon="Icons.Material.Filled.Refresh"
                              OnClick="LoadSystemActivity"
                              Disabled="IsLoading">
                        تحديث
                    </MudButton>
                </div>
            </div>

            <!-- Loading -->
            @if (IsLoading)
            {
                <div class="text-center py-8">
                    <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    <MudText Typo="Typo.body1" Class="mt-4">جاري تحميل بيانات النشاط...</MudText>
                </div>
            }
            else
            {
                <!-- Activity Timeline -->
                <MudCard Elevation="2">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="Icons.Material.Filled.History" Class="me-2" />
                                آخر الأنشطة والعمليات
                            </MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (SystemActivities.Any())
                        {
                            <MudTimeline TimelineOrientation="TimelineOrientation.Vertical" 
                                        TimelinePosition="TimelinePosition.Alternate">
                                @foreach (var activity in SystemActivities)
                                {
                                    <MudTimelineItem Color="@GetActivityColor(activity.Type)" 
                                                    Size="Size.Small">
                                        <ItemOpposite>
                                            <MudText Typo="Typo.body2" Color="Color.Secondary">
                                                @activity.Timestamp.ToString("HH:mm:ss")
                                            </MudText>
                                        </ItemOpposite>
                                        <ItemContent>
                                            <MudCard Elevation="1" Class="pa-3">
                                                <div class="d-flex align-center mb-2">
                                                    <MudIcon Icon="@GetActivityIcon(activity.Type)" 
                                                            Color="@GetActivityColor(activity.Type)" 
                                                            Class="me-2" />
                                                    <MudText Typo="Typo.subtitle1" Class="font-weight-bold">
                                                        @activity.Title
                                                    </MudText>
                                                </div>
                                                <MudText Typo="Typo.body2" Class="mb-2">
                                                    @activity.Description
                                                </MudText>
                                                <div class="d-flex align-center justify-space-between">
                                                    <MudChip T="string"
                                                            Size="Size.Small"
                                                            Color="@GetActivityColor(activity.Type)">
                                                        @GetActivityTypeText(activity.Type)
                                                    </MudChip>
                                                    <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                        @GetRelativeTime(activity.Timestamp)
                                                    </MudText>
                                                </div>
                                            </MudCard>
                                        </ItemContent>
                                    </MudTimelineItem>
                                }
                            </MudTimeline>
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Info" Class="text-center">
                                <MudIcon Icon="Icons.Material.Filled.Info" Class="me-2" />
                                لا توجد أنشطة مسجلة حديثاً
                            </MudAlert>
                        }
                    </MudCardContent>
                </MudCard>

                <!-- Quick Actions -->
                <MudCard Elevation="2" Class="mt-4">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="Icons.Material.Filled.Settings" Class="me-2" />
                                إجراءات سريعة
                            </MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <div class="d-flex flex-wrap gap-3">
                            <MudButton Variant="Variant.Outlined" 
                                      Color="Color.Primary" 
                                      StartIcon="Icons.Material.Filled.BugReport"
                                      OnClick="NavigateToErrorDiagnostics">
                                تشخيص الأخطاء
                            </MudButton>
                            
                            <MudButton Variant="Variant.Outlined" 
                                      Color="Color.Info" 
                                      StartIcon="Icons.Material.Filled.Refresh"
                                      OnClick="ClearCache">
                                مسح الذاكرة المؤقتة
                            </MudButton>
                            
                            <MudButton Variant="Variant.Outlined" 
                                      Color="Color.Warning" 
                                      StartIcon="Icons.Material.Filled.RestartAlt"
                                      OnClick="RestartApplication">
                                إعادة تشغيل التطبيق
                            </MudButton>
                        </div>
                    </MudCardContent>
                </MudCard>
            }
        </div>
    </MudPaper>
</MudContainer>

@code {
    private List<SystemActivityItem> SystemActivities = new();
    private bool IsLoading = true;

    public class SystemActivityItem
    {
        public DateTime Timestamp { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public ActivityType Type { get; set; }
    }

    public enum ActivityType
    {
        UserAction,
        SystemOperation,
        Error,
        Warning,
        Success
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadSystemActivity();
    }

    private async Task LoadSystemActivity()
    {
        IsLoading = true;
        StateHasChanged();
        
        try
        {
            // محاكاة تحميل آخر الأنشطة من النظام
            await Task.Delay(1000); // محاكاة تأخير التحميل
            
            // الحصول على آخر الأخطاء لإدراجها في النشاط
            var recentErrors = await ErrorLoggingService.GetRecentErrorsAsync(5);
            
            SystemActivities = new List<SystemActivityItem>();

            // إضافة الأخطاء كأنشطة
            foreach (var error in recentErrors)
            {
                SystemActivities.Add(new SystemActivityItem
                {
                    Timestamp = error.Timestamp,
                    Title = $"خطأ: {error.ExceptionType}",
                    Description = $"حدث خطأ في {error.Context}: {error.Message}",
                    Type = ActivityType.Error
                });
            }
            
            // إضافة أنشطة أخرى محاكاة (في التطبيق الحقيقي ستأتي من قاعدة البيانات)
            SystemActivities.AddRange(new[]
            {
                new SystemActivityItem
                {
                    Timestamp = DateTime.Now.AddMinutes(-5),
                    Title = "تسجيل دخول مستخدم",
                    Description = "قام مستخدم بتسجيل الدخول إلى النظام",
                    Type = ActivityType.UserAction
                },
                new SystemActivityItem
                {
                    Timestamp = DateTime.Now.AddMinutes(-10),
                    Title = "عملية بيع جديدة",
                    Description = "تم إنشاء فاتورة بيع جديدة",
                    Type = ActivityType.Success
                },
                new SystemActivityItem
                {
                    Timestamp = DateTime.Now.AddMinutes(-15),
                    Title = "تحديث المخزون",
                    Description = "تم تحديث كميات المخزون",
                    Type = ActivityType.SystemOperation
                },
                new SystemActivityItem
                {
                    Timestamp = DateTime.Now.AddMinutes(-20),
                    Title = "تحذير: مخزون منخفض",
                    Description = "تحذير من انخفاض مخزون بعض الأصناف",
                    Type = ActivityType.Warning
                }
            });
            
            SystemActivities = SystemActivities.OrderByDescending(a => a.Timestamp).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"فشل في تحميل نشاط النظام: {ex.Message}", Severity.Error);
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private Color GetActivityColor(ActivityType type)
    {
        return type switch
        {
            ActivityType.Error => Color.Error,
            ActivityType.Warning => Color.Warning,
            ActivityType.Success => Color.Success,
            ActivityType.UserAction => Color.Info,
            ActivityType.SystemOperation => Color.Primary,
            _ => Color.Default
        };
    }

    private string GetActivityIcon(ActivityType type)
    {
        return type switch
        {
            ActivityType.Error => Icons.Material.Filled.Error,
            ActivityType.Warning => Icons.Material.Filled.Warning,
            ActivityType.Success => Icons.Material.Filled.CheckCircle,
            ActivityType.UserAction => Icons.Material.Filled.Person,
            ActivityType.SystemOperation => Icons.Material.Filled.Settings,
            _ => Icons.Material.Filled.Info
        };
    }

    private string GetActivityTypeText(ActivityType type)
    {
        return type switch
        {
            ActivityType.Error => "خطأ",
            ActivityType.Warning => "تحذير",
            ActivityType.Success => "نجح",
            ActivityType.UserAction => "إجراء مستخدم",
            ActivityType.SystemOperation => "عملية نظام",
            _ => "غير محدد"
        };
    }

    private string GetRelativeTime(DateTime dateTime)
    {
        var timeSpan = DateTime.Now - dateTime;
        
        if (timeSpan.TotalMinutes < 1)
            return "الآن";
        if (timeSpan.TotalMinutes < 60)
            return $"منذ {(int)timeSpan.TotalMinutes} دقيقة";
        if (timeSpan.TotalHours < 24)
            return $"منذ {(int)timeSpan.TotalHours} ساعة";
        if (timeSpan.TotalDays < 7)
            return $"منذ {(int)timeSpan.TotalDays} يوم";
        
        return dateTime.ToString("yyyy-MM-dd");
    }

    private void NavigateToErrorDiagnostics()
    {
        Navigation.NavigateTo("/error-diagnostics");
    }

    private async Task ClearCache()
    {
        try
        {
            // محاكاة مسح الذاكرة المؤقتة
            await Task.Delay(1000);
            Snackbar.Add("تم مسح الذاكرة المؤقتة بنجاح", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"فشل في مسح الذاكرة المؤقتة: {ex.Message}", Severity.Error);
        }
    }

    private async Task RestartApplication()
    {
        try
        {
            Snackbar.Add("جاري إعادة تشغيل التطبيق...", Severity.Info);
            await Task.Delay(2000);
            Navigation.NavigateTo("/", forceLoad: true);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"فشل في إعادة تشغيل التطبيق: {ex.Message}", Severity.Error);
        }
    }
}
