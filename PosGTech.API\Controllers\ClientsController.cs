﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.Finacnial;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ClientsController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {

        [HttpGet("getAllSuppliers")]
        public async Task<IActionResult> GetAllSuppliers()
        {
            var clients = maper.Map<IEnumerable<Client>, IEnumerable<ClientCMDTO>>(await unitOfWork.Client.GetAll(x => x.IsSupplier == true));
            return Ok(clients);
        }
        [HttpGet("getAllCustomers")]
        public async Task<IActionResult> GetAllCustomers()
        {
            var clients = maper.Map<IEnumerable<Client>, IEnumerable<ClientCMDTO>>(await unitOfWork.Client.GetAll(x => x.IsCustomer == true));
            return Ok(clients);
        }
        [HttpGet("getAllClients")]
        public async Task<IActionResult> GetAllClients()
        {
            var clients = maper.Map<IEnumerable<Client>, IEnumerable<ClientDTO>>(await unitOfWork.Client.GetAll());
            return Ok(clients);
        }

        [HttpGet("getClientById/{id:Guid}")]
        public async Task<IActionResult> GetClientById([FromRoute] Guid id)
        {
            var client = maper.Map<Client?, ClientDTO?>(await unitOfWork.Client.GetByIdAsync(id));
            return Ok(client);
        }



        [HttpPost("insertClient")]
        public async Task<IActionResult> InsertClient([FromBody] ClientDTO model)
        {
            var newclient = maper.Map<ClientDTO, Client>(model);
            if (newclient.Balance != 0) await unitOfWork.Receipt.AddReceipt(new() { Client = newclient, Date = DateTime.Now, FinancialId = FinancialId.OpeningBalanceForClient, IsExchange = null, Value = newclient.Balance });
            else unitOfWork.Client.Add(newclient);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpPut("updateClient/{id:Guid}")]
        public async Task<IActionResult> UpdateClient([FromRoute] Guid id, ClientDTO model)
        {
            var oldclient = await unitOfWork.Client.GetByIdAsync(id);
            if (oldclient == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });

            unitOfWork.Client.UpdateClient(oldclient, model);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpDelete("deleteClient/{id:Guid}")]
        public async Task<IActionResult> DeleteClient([FromRoute] Guid id)
        {
            var oldclient = await unitOfWork.Client.GetByIdAsync(id);
            if (oldclient == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            unitOfWork.Client.Remove(oldclient);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }
    }
}
