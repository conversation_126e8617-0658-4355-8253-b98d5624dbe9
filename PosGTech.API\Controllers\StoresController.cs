﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Stores;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class StoresController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("getAllStores")]
        public async Task<IActionResult> GetAllStores()
        {
            var Stores = maper.Map<IEnumerable<Store>, IEnumerable<StoreDTO>>(await unitOfWork.Store.GetAll());
            return Ok(Stores);
        }
        [HttpGet("getAllStoresCMB")]
        public async Task<IActionResult> GetAllStoresCMB()
        {
            var Stores = maper.Map<IEnumerable<Store>, IEnumerable<StoreCMDTO>>(await unitOfWork.Store.GetAll());
            return Ok(Stores);
        }

        [HttpGet("getStoreById/{id:Guid}")]
        public async Task<IActionResult> GetStoreById([FromRoute] Guid id)
        {
            var Store = maper.Map<Store, StoreDTO>(await unitOfWork.Store.GetByIdAsync(id));
            return Ok(Store);
        }

        [HttpPost("insertStore")]
        public async Task<IActionResult> InsertStore([FromBody] StoreDTO model)
        {
            var newStore = maper.Map<StoreDTO, Store>(model);
            unitOfWork.Store.Add(newStore);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpPut("updateStore/{id:Guid}")]
        public async Task<IActionResult> UpdateStore([FromRoute] Guid id, StoreDTO model)
        {
            var oldStore = await unitOfWork.Store.GetByIdAsync(id);
            if (oldStore == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });

            unitOfWork.Store.UpdateStore(oldStore, model);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }



        [HttpDelete("deleteStore/{id:Guid}")]
        public async Task<IActionResult> DeleteStore([FromRoute] Guid id)
        {
            var oldStore = await unitOfWork.Store.GetByIdAsync(id);
            if (oldStore == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });

            // حذف الصورة إذا كانت موجودة
            if (!string.IsNullOrEmpty(oldStore.LogoPath))
            {
                var logoPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", oldStore.LogoPath.TrimStart('/'));
                if (System.IO.File.Exists(logoPath))
                {
                    System.IO.File.Delete(logoPath);
                }
            }

            unitOfWork.Store.Remove(oldStore);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }

        // Store Settings endpoints
        [HttpGet("getAllStoreSettings")]
        public async Task<IActionResult> GetAllStoreSettings()
        {
            var stores = maper.Map<IEnumerable<Store>, IEnumerable<StoreSettingsDTO>>(await unitOfWork.Store.GetAll());
            return Ok(stores);
        }

        [HttpGet("getStoreSettingsById/{id:Guid}")]
        public async Task<IActionResult> GetStoreSettingsById([FromRoute] Guid id)
        {
            var store = maper.Map<Store, StoreSettingsDTO>(await unitOfWork.Store.GetByIdAsync(id));
            return Ok(store);
        }

        [HttpPost("insertStoreSettings")]
        public async Task<IActionResult> InsertStoreSettings([FromBody] StoreSettingsDTO model)
        {
            try
            {
                var newStore = maper.Map<StoreSettingsDTO, Store>(model);
                unitOfWork.Store.Add(newStore);
                var res = await unitOfWork.SaveWithTransaction();
                if (res.State) return Ok(res);
                else return BadRequest(res);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM() { Message = "خطأ في حفظ البيانات: " + ex.Message });
            }
        }

        [HttpPost("insertStoreSettingsWithFile")]
        public async Task<IActionResult> InsertStoreSettingsWithFile([FromForm] string? storeSettings, IFormFile? Img)
        {
            try
            {
                StoreSettingsDTO model;

                // إذا كان هناك بيانات JSON، قم بتحليلها
                if (!string.IsNullOrEmpty(storeSettings))
                {
                    model = System.Text.Json.JsonSerializer.Deserialize<StoreSettingsDTO>(storeSettings);
                }
                else
                {
                    // إذا لم تكن هناك بيانات JSON، استخدم البيانات من النموذج مباشرة
                    model = new StoreSettingsDTO
                    {
                        Name = Request.Form["Name"],
                        CompanyName = Request.Form["CompanyName"],
                        PhoneNumber = Request.Form["PhoneNumber"],
                        Address = Request.Form["Address"]
                    };
                }

                // معالجة رفع الصورة
                if (Img != null && Img.Length > 0)
                {
                    var logoPath = await SaveLogoFile(Img);
                    model.LogoPath = logoPath;
                }

                var newStore = maper.Map<StoreSettingsDTO, Store>(model);
                unitOfWork.Store.Add(newStore);
                var res = await unitOfWork.SaveWithTransaction();
                if (res.State) return Ok(res);
                else return BadRequest(res);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM() { Message = "خطأ في حفظ البيانات: " + ex.Message });
            }
        }

        [HttpPut("updateStoreSettings/{id:Guid}")]
        public async Task<IActionResult> UpdateStoreSettings([FromRoute] Guid id, [FromBody] StoreSettingsDTO model)
        {
            try
            {
                var oldStore = await unitOfWork.Store.GetByIdAsync(id);
                if (oldStore == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });

                // الاحتفاظ بالصورة القديمة إذا لم يتم تحديد صورة جديدة
                if (string.IsNullOrEmpty(model.LogoPath))
                {
                    model.LogoPath = oldStore.LogoPath;
                }

                unitOfWork.Store.UpdateStoreSettings(oldStore, model);
                var res = await unitOfWork.SaveWithTransaction();
                if (res.State) return Ok(res);
                else return BadRequest(res);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM() { Message = "خطأ في تحديث البيانات: " + ex.Message });
            }
        }

        [HttpPut("updateStoreSettingsWithFile/{id:Guid}")]
        public async Task<IActionResult> UpdateStoreSettingsWithFile([FromRoute] Guid id, [FromForm] string? storeSettings, IFormFile? Img)
        {
            try
            {
                var oldStore = await unitOfWork.Store.GetByIdAsync(id);
                if (oldStore == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });

                StoreSettingsDTO model;

                // إذا كان هناك بيانات JSON، قم بتحليلها
                if (!string.IsNullOrEmpty(storeSettings))
                {
                    model = System.Text.Json.JsonSerializer.Deserialize<StoreSettingsDTO>(storeSettings);
                }
                else
                {
                    // إذا لم تكن هناك بيانات JSON، استخدم البيانات من النموذج مباشرة
                    model = new StoreSettingsDTO
                    {
                        Name = Request.Form["Name"],
                        CompanyName = Request.Form["CompanyName"],
                        PhoneNumber = Request.Form["PhoneNumber"],
                        Address = Request.Form["Address"]
                    };
                }

                // معالجة رفع الصورة الجديدة
                if (Img != null && Img.Length > 0)
                {
                    // حذف الصورة القديمة
                    if (!string.IsNullOrEmpty(oldStore.LogoPath))
                    {
                        var oldLogoPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", oldStore.LogoPath.TrimStart('/'));
                        if (System.IO.File.Exists(oldLogoPath))
                        {
                            System.IO.File.Delete(oldLogoPath);
                        }
                    }

                    var logoPath = await SaveLogoFile(Img);
                    model.LogoPath = logoPath;
                }
                else
                {
                    // الاحتفاظ بالصورة القديمة إذا لم يتم رفع صورة جديدة
                    model.LogoPath = oldStore.LogoPath;
                }

                unitOfWork.Store.UpdateStoreSettings(oldStore, model);
                var res = await unitOfWork.SaveWithTransaction();
                if (res.State) return Ok(res);
                else return BadRequest(res);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM() { Message = "خطأ في تحديث البيانات: " + ex.Message });
            }
        }

        private async Task<string> SaveLogoFile(IFormFile logoFile)
        {
            // التحقق من نوع الملف
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
            var fileExtension = Path.GetExtension(logoFile.FileName).ToLowerInvariant();

            if (!allowedExtensions.Contains(fileExtension))
            {
                throw new ArgumentException("نوع الملف غير مدعوم. يرجى استخدام صور بصيغة JPG, PNG, GIF أو BMP");
            }

            // التحقق من حجم الملف (5 ميجابايت كحد أقصى)
            if (logoFile.Length > 5 * 1024 * 1024)
            {
                throw new ArgumentException("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت");
            }

            // إنشاء مجلد الصور إذا لم يكن موجوداً
            var logoDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "logo");
            if (!Directory.Exists(logoDirectory))
            {
                Directory.CreateDirectory(logoDirectory);
            }

            // إنشاء اسم ملف فريد
            var fileName = Guid.NewGuid().ToString() + fileExtension;
            var filePath = Path.Combine(logoDirectory, fileName);

            // حفظ الملف
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await logoFile.CopyToAsync(stream);
            }

            return "/logo/" + fileName;
        }

        /// <summary>
        /// API endpoint لعرض الصور المرفوعة
        /// </summary>
        /// <param name="fileName">اسم الملف</param>
        /// <returns>الصورة</returns>
        [HttpGet("logo/{fileName}")]
        public async Task<IActionResult> GetLogo(string fileName)
        {
            try
            {
                var logoDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "logo");
                var filePath = Path.Combine(logoDirectory, fileName);

                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound("الصورة غير موجودة");
                }

                var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
                var contentType = GetContentType(fileName);

                return File(fileBytes, contentType);
            }
            catch (Exception ex)
            {
                return BadRequest($"خطأ في تحميل الصورة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديد نوع المحتوى بناءً على امتداد الملف
        /// </summary>
        /// <param name="fileName">اسم الملف</param>
        /// <returns>نوع المحتوى</returns>
        private string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                _ => "application/octet-stream"
            };
        }

    }
}
