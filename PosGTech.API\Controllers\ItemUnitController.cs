﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Items;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ItemUnitController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("GetItemUnit/{id}")]
        public async Task<IActionResult> GetById([FromRoute] Guid id)
        {
            var Resuilt = maper.Map<IEnumerable<ItemUnit>, IEnumerable<ItemUnitDTO>>(await unitOfWork.ItemUnit.GetAll(x=>x.ItemId==id, "Unit"));
            return Ok(Resuilt);
        }
    }
}
