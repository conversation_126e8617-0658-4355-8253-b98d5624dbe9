﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول الموظفين
    /// </summary>
    [Index("Name", IsUnique = true)]
    public class Employee : BaseEntity
    {
        public string Name { get; set; }
        public int? Phone { get; set; }
        public string? Job { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal Salary { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
        public decimal Balance { get; set; }
        public ICollection<Salary>? Salaries { get; set; }
        public ICollection<Receipt>? Receipts { get; set; }
    }
}
