﻿namespace PosGTech.Models
{
    /// <summary>
    /// جدول خزينة المستخدم
    /// </summary>
    public class UserTreasury : BaseEntity
    {
        [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
        public decimal Balance { get; set; }
        public Guid? TreasuryId { get; set; }
        public Treasury? Treasury { get; set; }
        public Guid? UserId { get; set; }
        public User? User { get; set; }
        public ICollection<Receipt>? UserTreasuryReceipts { get; set; }
        public ICollection<Receipt>? FromTreasuryReceipts { get; set; }
    }
}
