﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Users;
using PosGTech.ModelsDTO.Roles;
using PosGTech.ModelsDTO.Authorization;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Users
{
    public partial class UpsertUser
    {
        [Parameter]
        public Guid id { get; set; }

        UserDTO userCurrent = new();
        List<RoleDTO> availableRoles = new();

        private List<BreadcrumbItem> _items;
        private bool isLoading = false;
        private bool isSaving = false;

        [Inject]
        IGRepository<UserDTO> _user { get; set; }

        [Inject]
        IGRepository<RoleDTO> _role { get; set; }

        bool IsRequired = true;
        protected override async Task OnInitializedAsync()
        {
            try
            {
                // تحميل الأدوار المتاحة أولاً
                await LoadAvailableRoles();

                string Titel;
                if (id != Guid.Empty)
                {
                    IsRequired=false;
                    var res = await _user.GetByIdAsync("Users/getUserById", id);
                    if (res.response == null)
                    {
                        userCurrent = res.model;

                        // التأكد من وجود القوائم
                        userCurrent.AdditionalPermissions ??= new List<string>();
                        userCurrent.RemovedPermissions ??= new List<string>();
                        userCurrent.EffectivePermissions ??= new List<string>();

                        // حساب الصلاحيات الفعلية
                        await CalculateEffectivePermissions();
                    }
                    else
                    {
                        _snackbar.Add("خطأ في الاتصال", Severity.Error);
                        _navigation.NavigateTo("/listUsers");
                    }

                    Titel = "تعديل مستخدم";
                }
                else
                {
                    IsRequired=true;
                    userCurrent = new UserDTO();

                    // التأكد من وجود القوائم للمستخدم الجديد
                    userCurrent.AdditionalPermissions = new List<string>();
                    userCurrent.RemovedPermissions = new List<string>();
                    userCurrent.EffectivePermissions = new List<string>();

                    Titel = "إضافة مستخدم";
                }

                _items = new List<BreadcrumbItem>()
                {
                    new BreadcrumbItem("الرئيسية", href:"", icon: Icons.Material.Filled.Home),
                    new BreadcrumbItem("مستخدمون", href: "/listUsers", icon: Icons.Material.Filled.AccountBalance),
                    new BreadcrumbItem(Titel, href: null, disabled:true, icon: Icons.Material.Filled.AccountBalance)
                };

                // إجبار تحديث الواجهة
                StateHasChanged();
            }
            catch (Exception ex)
            {
                _snackbar.Add($"خطأ في تحميل البيانات: {ex.Message}", Severity.Error);
            }
        }
        async void Upsert()
        {
            if (isSaving) return; // منع الضغط المتكرر

            try
            {
                isSaving = true;
                StateHasChanged();

                // التحقق من صحة البيانات
                if (string.IsNullOrEmpty(userCurrent.RoleName))
                {
                    _snackbar.Add("يجب اختيار دور للمستخدم", Severity.Error);
                    return;
                }

                // التحقق من كلمة المرور للمستخدم الجديد
                if (id == Guid.Empty && string.IsNullOrWhiteSpace(userCurrent.Password))
                {
                    _snackbar.Add("كلمة المرور مطلوبة للمستخدم الجديد", Severity.Error);
                    return;
                }

                // التحقق من تطابق كلمة المرور إذا تم إدخالها
                if (!string.IsNullOrWhiteSpace(userCurrent.Password) &&
                    userCurrent.Password != userCurrent.ConfirmPassword)
                {
                    _snackbar.Add("كلمة المرور غير متطابقة", Severity.Error);
                    return;
                }

                // إذا كان تعديل وكلمة المرور فارغة، لا نرسلها للخادم
                if (id != Guid.Empty && string.IsNullOrWhiteSpace(userCurrent.Password))
                {
                    userCurrent.Password = null;
                    userCurrent.ConfirmPassword = null;
                }

                // حساب الصلاحيات الفعلية قبل الحفظ
                await CalculateEffectivePermissions();

                ResponseVM response;
                if (id == Guid.Empty)
                    response = await _user.Insert("Users/insertUser", userCurrent);
                else
                    response = await _user.Update("Users/updateUser", userCurrent, id);

                if (response.State)
                {
                    _snackbar.Add(response.Message, Severity.Success);
                    _navigation.NavigateTo("/listUsers");
                }
                else
                {
                    _snackbar.Add(response.Message, Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in Upsert: {ex.Message}");
                _snackbar.Add($"خطأ في حفظ البيانات: {ex.Message}", Severity.Error);
            }
            finally
            {
                isSaving = false;
                StateHasChanged();
            }
        }

        // ==================== Helper Methods ====================

        /// <summary>
        /// تحميل الأدوار المتاحة
        /// </summary>
        private async Task LoadAvailableRoles()
        {
            try
            {
                Console.WriteLine("Loading available roles...");
                var rolesResponse = await _role.GetAll("Roles/getAllRoles");

                if (rolesResponse.response == null)
                {
                    availableRoles = rolesResponse.list?.ToList() ?? new List<RoleDTO>();
                    Console.WriteLine($"Loaded {availableRoles.Count} roles");

                    // طباعة تفاصيل كل دور للتشخيص
                    foreach (var role in availableRoles)
                    {
                        Console.WriteLine($"Role: {role.Name}, Permissions: {role.Permissions?.Count ?? 0}");
                        if (role.Permissions != null && role.Permissions.Any())
                        {
                            Console.WriteLine($"  First few permissions: {string.Join(", ", role.Permissions.Take(3))}");
                        }
                    }
                }
                else
                {
                    Console.WriteLine($"Error loading roles: {rolesResponse.response}");
                    _snackbar.Add("خطأ في تحميل الأدوار", Severity.Error);
                    availableRoles = new List<RoleDTO>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in LoadAvailableRoles: {ex.Message}");
                _snackbar.Add($"خطأ في تحميل الأدوار: {ex.Message}", Severity.Error);
                availableRoles = new List<RoleDTO>();
            }
        }

        /// <summary>
        /// حساب الصلاحيات الفعلية للمستخدم
        /// </summary>
        private async Task CalculateEffectivePermissions()
        {
            try
            {
                Console.WriteLine("=== CalculateEffectivePermissions START ===");

                if (string.IsNullOrEmpty(userCurrent.RoleName))
                {
                    Console.WriteLine("Role name is empty, setting empty permissions");
                    userCurrent.EffectivePermissions = new List<string>();
                    StateHasChanged();
                    return;
                }

                Console.WriteLine($"Calculating permissions for role: {userCurrent.RoleName}");

                // الحصول على صلاحيات الدور
                var rolePermissions = GetRolePermissions(userCurrent.RoleName);
                Console.WriteLine($"Role permissions retrieved: {rolePermissions.Count}");

                // التأكد من وجود القوائم
                userCurrent.AdditionalPermissions ??= new List<string>();
                userCurrent.RemovedPermissions ??= new List<string>();

                Console.WriteLine($"Additional permissions: {userCurrent.AdditionalPermissions.Count}");
                Console.WriteLine($"Removed permissions: {userCurrent.RemovedPermissions.Count}");

                // حساب الصلاحيات الفعلية
                var effectivePermissions = rolePermissions
                    .Union(userCurrent.AdditionalPermissions)
                    .Except(userCurrent.RemovedPermissions)
                    .Distinct()
                    .ToList();

                userCurrent.EffectivePermissions = effectivePermissions;

                Console.WriteLine($"Final effective permissions: {userCurrent.EffectivePermissions.Count}");
                Console.WriteLine($"First 5 permissions: {string.Join(", ", userCurrent.EffectivePermissions.Take(5))}");

                // إجبار تحديث الواجهة
                StateHasChanged();

                Console.WriteLine("=== CalculateEffectivePermissions END ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ERROR in CalculateEffectivePermissions: {ex.Message}");
                _snackbar.Add($"خطأ في حساب الصلاحيات: {ex.Message}", Severity.Error);
                userCurrent.EffectivePermissions = new List<string>();
                StateHasChanged();
            }
        }

        /// <summary>
        /// الحصول على صلاحيات دور معين
        /// </summary>
        private List<string> GetRolePermissions(string roleName)
        {
            Console.WriteLine($"GetRolePermissions called for role: {roleName}");

            if (string.IsNullOrEmpty(roleName))
            {
                Console.WriteLine("Role name is null or empty");
                return new List<string>();
            }

            if (availableRoles == null || !availableRoles.Any())
            {
                Console.WriteLine("Available roles is null or empty");
                return new List<string>();
            }

            var role = availableRoles.FirstOrDefault(r => r.Name == roleName);
            if (role == null)
            {
                Console.WriteLine($"Role '{roleName}' not found in available roles");
                Console.WriteLine($"Available role names: {string.Join(", ", availableRoles.Select(r => r.Name))}");
                return new List<string>();
            }

            var permissions = role.Permissions ?? new List<string>();
            Console.WriteLine($"Found {permissions.Count} permissions for role '{roleName}'");

            return permissions;
        }

        /// <summary>
        /// إعادة تحميل البيانات والصلاحيات بالكامل
        /// </summary>
        private async Task RefreshPermissions()
        {
            try
            {
                Console.WriteLine("RefreshPermissions called");

                // إعادة تحميل الأدوار
                await LoadAvailableRoles();

                // إعادة حساب الصلاحيات
                await CalculateEffectivePermissions();

                // إجبار تحديث الواجهة
                await InvokeAsync(StateHasChanged);

                Console.WriteLine("RefreshPermissions completed");
                _snackbar.Add("تم تحديث الصلاحيات بنجاح", Severity.Success);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in RefreshPermissions: {ex.Message}");
                _snackbar.Add($"خطأ في تحديث الصلاحيات: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// الحصول على عدد صلاحيات دور معين
        /// </summary>
        private int GetRolePermissionsCount(string roleName)
        {
            var permissions = GetRolePermissions(roleName);
            return permissions.Count;
        }



        /// <summary>
        /// الحصول على وصف الدور
        /// </summary>
        private string GetRoleDescription(string roleName)
        {
            return roleName switch
            {
                "SystemAdmin" => "مدير النظام - صلاحيات كاملة",
                "SalesEmployee" => "موظف مبيعات - إدارة المبيعات والعملاء",
                "PurchaseManager" => "مدير المشتريات - إدارة المشتريات والموردين",
                "Accountant" => "محاسب - إدارة الحسابات والتقارير المالية",
                "InventoryManager" => "مدير المخزون - إدارة المخزون والجرد",
                _ => "دور مخصص"
            };
        }

        /// <summary>
        /// الحصول على أيقونة الدور
        /// </summary>
        private string GetRoleIcon(string roleName)
        {
            return roleName switch
            {
                "SystemAdmin" => Icons.Material.Filled.AdminPanelSettings,
                "SalesEmployee" => Icons.Material.Filled.PointOfSale,
                "PurchaseManager" => Icons.Material.Filled.ShoppingCart,
                "Accountant" => Icons.Material.Filled.AccountBalance,
                "InventoryManager" => Icons.Material.Filled.Inventory,
                _ => Icons.Material.Filled.Person
            };
        }

        /// <summary>
        /// الحصول على وصف الصلاحية
        /// </summary>
        private string GetPermissionDescription(string permission)
        {
            return PermissionDescriptions.GetDescription(permission);
        }

        /// <summary>
        /// معالج تغيير الدور - يحدث الصلاحيات تلقائياً
        /// </summary>
        private async Task OnRoleChanged(string newRoleName)
        {
            try
            {
                Console.WriteLine($"OnRoleChanged called with: {newRoleName}");

                userCurrent.RoleName = newRoleName;

                // إعادة تعيين الصلاحيات الإضافية والمحذوفة عند تغيير الدور
                userCurrent.AdditionalPermissions = new List<string>();
                userCurrent.RemovedPermissions = new List<string>();

                // طباعة معلومات التشخيص
                var rolePermissions = GetRolePermissions(newRoleName);
                Console.WriteLine($"Role permissions count: {rolePermissions.Count}");
                Console.WriteLine($"Available roles count: {availableRoles?.Count ?? 0}");

                // حساب الصلاحيات الفعلية الجديدة
                await CalculateEffectivePermissions();

                Console.WriteLine($"Effective permissions count: {userCurrent.EffectivePermissions?.Count ?? 0}");

                // إجبار تحديث الواجهة بطرق متعددة
                await InvokeAsync(StateHasChanged);

                // إضافة تأخير قصير للتأكد من تحديث الواجهة
                await Task.Delay(100);
                await InvokeAsync(StateHasChanged);

                // إعادة تحديث الواجهة مرة أخرى بعد تأخير أطول
                await Task.Delay(200);
                await InvokeAsync(StateHasChanged);

                _snackbar.Add($"تم تحديث الدور إلى: {newRoleName}", Severity.Success);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in OnRoleChanged: {ex.Message}");
                _snackbar.Add($"خطأ في تحديث الدور: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// التحقق من أن الصلاحية مُختارة (فعالة للمستخدم)
        /// </summary>
        private bool IsPermissionChecked(string permission)
        {
            // التحقق السريع من الشروط الأساسية
            if (string.IsNullOrEmpty(permission) ||
                userCurrent?.EffectivePermissions == null)
            {
                return false;
            }

            var isChecked = userCurrent.EffectivePermissions.Contains(permission);

            // طباعة محدودة للتشخيص فقط
            if (permission.Contains("Users.View") || permission.Contains("Roles.View"))
            {
                Console.WriteLine($"IsPermissionChecked: Permission '{permission}' -> {isChecked} (Total effective: {userCurrent.EffectivePermissions.Count})");
            }

            return isChecked;
        }

        /// <summary>
        /// معالج تبديل حالة الصلاحية
        /// </summary>
        private async Task OnPermissionToggle(string permission, bool isChecked)
        {
            try
            {
                // التأكد من وجود القوائم
                userCurrent.AdditionalPermissions ??= new List<string>();
                userCurrent.RemovedPermissions ??= new List<string>();

                var rolePermissions = GetRolePermissions(userCurrent.RoleName);
                var isRolePermission = rolePermissions.Contains(permission);

                if (isChecked)
                {
                    // المستخدم يريد إضافة هذه الصلاحية
                    if (isRolePermission)
                    {
                        // إذا كانت من صلاحيات الدور، أزلها من قائمة المحذوفة
                        userCurrent.RemovedPermissions.Remove(permission);
                    }
                    else
                    {
                        // إذا لم تكن من صلاحيات الدور، أضفها للإضافية
                        if (!userCurrent.AdditionalPermissions.Contains(permission))
                            userCurrent.AdditionalPermissions.Add(permission);
                    }
                }
                else
                {
                    // المستخدم يريد إزالة هذه الصلاحية
                    if (isRolePermission)
                    {
                        // إذا كانت من صلاحيات الدور، أضفها للمحذوفة
                        if (!userCurrent.RemovedPermissions.Contains(permission))
                            userCurrent.RemovedPermissions.Add(permission);
                    }
                    else
                    {
                        // إذا لم تكن من صلاحيات الدور، أزلها من الإضافية
                        userCurrent.AdditionalPermissions.Remove(permission);
                    }
                }

                // إعادة حساب الصلاحيات الفعلية
                await CalculateEffectivePermissions();
            }
            catch (Exception ex)
            {
                _snackbar.Add($"خطأ في تحديث الصلاحية: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// الحصول على لون الصلاحية حسب مصدرها
        /// </summary>
        private Color GetPermissionColor(string permission)
        {
            try
            {
                // التحقق السريع من الشروط الأساسية
                if (string.IsNullOrEmpty(permission) ||
                    userCurrent?.EffectivePermissions == null ||
                    !userCurrent.EffectivePermissions.Contains(permission))
                {
                    return Color.Default;
                }

                // التحقق من مصدر الصلاحية
                var rolePermissions = GetRolePermissions(userCurrent.RoleName);
                var isRolePermission = rolePermissions.Contains(permission);

                // طباعة محدودة للتشخيص
                if (permission.Contains("Users.View") || permission.Contains("Roles.View"))
                {
                    Console.WriteLine($"GetPermissionColor: {permission} -> isRole: {isRolePermission}");
                }

                // تحديد اللون حسب المصدر
                if (isRolePermission && userCurrent.RemovedPermissions?.Contains(permission) != true)
                    return Color.Primary; // من الدور الأساسي

                if (userCurrent.AdditionalPermissions?.Contains(permission) == true)
                    return Color.Success; // صلاحية إضافية

                return Color.Primary;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetPermissionColor for {permission}: {ex.Message}");
                return Color.Default;
            }
        }

        /// <summary>
        /// الحصول على أيقونة المجموعة
        /// </summary>
        private string GetGroupIcon(string groupName)
        {
            return groupName switch
            {
                "إدارة الأصناف والتصنيفات" => Icons.Material.Filled.Category,
                "إدارة المبيعات" => Icons.Material.Filled.PointOfSale,
                "إدارة المشتريات" => Icons.Material.Filled.ShoppingCart,
                "إدارة المخزون" => Icons.Material.Filled.Inventory,
                "الإدارة المالية" => Icons.Material.Filled.AccountBalance,
                "إدارة الموارد البشرية" => Icons.Material.Filled.People,
                "التقارير والإحصائيات" => Icons.Material.Filled.Assessment,
                "إدارة النظام" => Icons.Material.Filled.Settings,
                _ => Icons.Material.Filled.Security
            };
        }
    }
}