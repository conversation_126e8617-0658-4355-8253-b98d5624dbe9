using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using PosGTech.Models;
using PosGTech.ModelsDTO.Users;
using PosGTech.ModelsDTO.Authorization;
using System.Security.Claims;

namespace PosGTech.API.Controllers
{
    /// <summary>
    /// API Controller لإدارة UserClaim للصلاحيات الإضافية والمحذوفة
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserClaimsController : ControllerBase
    {
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<Role> _roleManager;

        public UserClaimsController(UserManager<User> userManager, RoleManager<Role> roleManager)
        {
            _userManager = userManager;
            _roleManager = roleManager;
        }

        /// <summary>
        /// إضافة صلاحية إضافية للمستخدم
        /// </summary>
        [HttpPost("addAdditionalPermission")]
        public async Task<IActionResult> AddAdditionalPermission([FromBody] UserClaimDTO model)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(model.UserId.ToString());
                if (user == null)
                    return NotFound(new { Message = "المستخدم غير موجود" });

                // التحقق من أن الصلاحية موجودة في النظام
                if (!PermissionConstants.GetAllPermissions().Contains(model.ClaimValue))
                    return BadRequest(new { Message = "الصلاحية غير موجودة في النظام" });

                // التحقق من عدم وجود الصلاحية مسبقاً
                var existingClaims = await _userManager.GetClaimsAsync(user);
                if (existingClaims.Any(c => c.Type == "additional_permission" && c.Value == model.ClaimValue))
                    return BadRequest(new { Message = "الصلاحية موجودة مسبقاً كصلاحية إضافية" });

                // إضافة الصلاحية
                var claim = new Claim("additional_permission", model.ClaimValue);
                var result = await _userManager.AddClaimAsync(user, claim);

                if (result.Succeeded)
                    return Ok(new { Message = "تم إضافة الصلاحية الإضافية بنجاح" });
                else
                    return BadRequest(new { Message = "فشل في إضافة الصلاحية", Errors = result.Errors });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في إضافة الصلاحية الإضافية", Error = ex.Message });
            }
        }

        /// <summary>
        /// حذف صلاحية من الدور الأساسي للمستخدم
        /// </summary>
        [HttpPost("removeRolePermission")]
        public async Task<IActionResult> RemoveRolePermission([FromBody] UserClaimDTO model)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(model.UserId.ToString());
                if (user == null)
                    return NotFound(new { Message = "المستخدم غير موجود" });

                // التحقق من أن المستخدم لديه هذه الصلاحية في دوره
                var hasPermissionInRole = await UserHasPermissionInRole(user, model.ClaimValue);
                if (!hasPermissionInRole)
                    return BadRequest(new { Message = "الصلاحية غير موجودة في دور المستخدم" });

                // التحقق من عدم وجود الصلاحية مسبقاً في المحذوفات
                var existingClaims = await _userManager.GetClaimsAsync(user);
                if (existingClaims.Any(c => c.Type == "removed_permission" && c.Value == model.ClaimValue))
                    return BadRequest(new { Message = "الصلاحية محذوفة مسبقاً من الدور" });

                // إضافة الصلاحية كمحذوفة
                var claim = new Claim("removed_permission", model.ClaimValue);
                var result = await _userManager.AddClaimAsync(user, claim);

                if (result.Succeeded)
                    return Ok(new { Message = "تم حذف الصلاحية من الدور بنجاح" });
                else
                    return BadRequest(new { Message = "فشل في حذف الصلاحية", Errors = result.Errors });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في حذف الصلاحية من الدور", Error = ex.Message });
            }
        }

        /// <summary>
        /// إزالة صلاحية إضافية من المستخدم
        /// </summary>
        [HttpDelete("removeAdditionalPermission")]
        public async Task<IActionResult> RemoveAdditionalPermission([FromBody] UserClaimDTO model)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(model.UserId.ToString());
                if (user == null)
                    return NotFound(new { Message = "المستخدم غير موجود" });

                var claim = new Claim("additional_permission", model.ClaimValue);
                var result = await _userManager.RemoveClaimAsync(user, claim);

                if (result.Succeeded)
                    return Ok(new { Message = "تم إزالة الصلاحية الإضافية بنجاح" });
                else
                    return BadRequest(new { Message = "فشل في إزالة الصلاحية", Errors = result.Errors });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في إزالة الصلاحية الإضافية", Error = ex.Message });
            }
        }

        /// <summary>
        /// استعادة صلاحية محذوفة من الدور
        /// </summary>
        [HttpDelete("restoreRolePermission")]
        public async Task<IActionResult> RestoreRolePermission([FromBody] UserClaimDTO model)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(model.UserId.ToString());
                if (user == null)
                    return NotFound(new { Message = "المستخدم غير موجود" });

                var claim = new Claim("removed_permission", model.ClaimValue);
                var result = await _userManager.RemoveClaimAsync(user, claim);

                if (result.Succeeded)
                    return Ok(new { Message = "تم استعادة الصلاحية للدور بنجاح" });
                else
                    return BadRequest(new { Message = "فشل في استعادة الصلاحية", Errors = result.Errors });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في استعادة الصلاحية", Error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على الصلاحيات الإضافية للمستخدم
        /// </summary>
        [HttpGet("getUserAdditionalPermissions/{userId}")]
        public async Task<IActionResult> GetUserAdditionalPermissions([FromRoute] Guid userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                    return NotFound(new { Message = "المستخدم غير موجود" });

                var claims = await _userManager.GetClaimsAsync(user);
                var additionalPermissions = claims
                    .Where(c => c.Type == "additional_permission")
                    .Select(c => new UserClaimDTO
                    {
                        UserId = userId,
                        ClaimType = c.Type,
                        ClaimValue = c.Value,
                        PermissionDescription = PermissionDescriptions.GetDescription(c.Value)
                    })
                    .ToList();

                return Ok(additionalPermissions);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في الحصول على الصلاحيات الإضافية", Error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على الصلاحيات المحذوفة من الدور للمستخدم
        /// </summary>
        [HttpGet("getUserRemovedPermissions/{userId}")]
        public async Task<IActionResult> GetUserRemovedPermissions([FromRoute] Guid userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                    return NotFound(new { Message = "المستخدم غير موجود" });

                var claims = await _userManager.GetClaimsAsync(user);
                var removedPermissions = claims
                    .Where(c => c.Type == "removed_permission")
                    .Select(c => new UserClaimDTO
                    {
                        UserId = userId,
                        ClaimType = c.Type,
                        ClaimValue = c.Value,
                        PermissionDescription = PermissionDescriptions.GetDescription(c.Value)
                    })
                    .ToList();

                return Ok(removedPermissions);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في الحصول على الصلاحيات المحذوفة", Error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على جميع Claims المستخدم
        /// </summary>
        [HttpGet("getAllUserClaims/{userId}")]
        public async Task<IActionResult> GetAllUserClaims([FromRoute] Guid userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                    return NotFound(new { Message = "المستخدم غير موجود" });

                var claims = await _userManager.GetClaimsAsync(user);
                var userClaims = claims.Select(c => new UserClaimDTO
                {
                    UserId = userId,
                    ClaimType = c.Type,
                    ClaimValue = c.Value,
                    PermissionDescription = c.Type == "additional_permission" || c.Type == "removed_permission"
                        ? PermissionDescriptions.GetDescription(c.Value)
                        : c.Value
                }).ToList();

                return Ok(userClaims);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في الحصول على Claims المستخدم", Error = ex.Message });
            }
        }

        /// <summary>
        /// إدارة صلاحيات المستخدم بشكل مجمع
        /// </summary>
        [HttpPost("manageUserPermissions")]
        public async Task<IActionResult> ManageUserPermissions([FromBody] UserPermissionManagementDTO model)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(model.UserId.ToString());
                if (user == null)
                    return NotFound(new { Message = "المستخدم غير موجود" });

                var results = new List<string>();

                // إضافة الصلاحيات الإضافية
                foreach (var permission in model.PermissionsToAdd)
                {
                    if (PermissionConstants.GetAllPermissions().Contains(permission))
                    {
                        var claim = new Claim("additional_permission", permission);
                        var result = await _userManager.AddClaimAsync(user, claim);
                        if (result.Succeeded)
                            results.Add($"تم إضافة الصلاحية: {PermissionDescriptions.GetDescription(permission)}");
                    }
                }

                // إزالة الصلاحيات الإضافية
                foreach (var permission in model.AdditionalPermissionsToRemove)
                {
                    var claim = new Claim("additional_permission", permission);
                    var result = await _userManager.RemoveClaimAsync(user, claim);
                    if (result.Succeeded)
                        results.Add($"تم إزالة الصلاحية الإضافية: {PermissionDescriptions.GetDescription(permission)}");
                }

                // حذف صلاحيات من الدور
                foreach (var permission in model.RolePermissionsToRemove)
                {
                    var claim = new Claim("removed_permission", permission);
                    var result = await _userManager.AddClaimAsync(user, claim);
                    if (result.Succeeded)
                        results.Add($"تم حذف الصلاحية من الدور: {PermissionDescriptions.GetDescription(permission)}");
                }

                // استعادة صلاحيات محذوفة
                foreach (var permission in model.RemovedPermissionsToRestore)
                {
                    var claim = new Claim("removed_permission", permission);
                    var result = await _userManager.RemoveClaimAsync(user, claim);
                    if (result.Succeeded)
                        results.Add($"تم استعادة الصلاحية: {PermissionDescriptions.GetDescription(permission)}");
                }

                return Ok(new { Message = "تم تحديث صلاحيات المستخدم بنجاح", Details = results });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في إدارة صلاحيات المستخدم", Error = ex.Message });
            }
        }

        // ==================== Helper Methods ====================

        /// <summary>
        /// التحقق من أن المستخدم لديه صلاحية في دوره
        /// </summary>
        private async Task<bool> UserHasPermissionInRole(User user, string permission)
        {
            var userRoles = await _userManager.GetRolesAsync(user);
            
            foreach (var roleName in userRoles)
            {
                var role = await _roleManager.FindByNameAsync(roleName);
                if (role != null)
                {
                    var roleClaims = await _roleManager.GetClaimsAsync(role);
                    if (roleClaims.Any(c => c.Type == "permission" && c.Value == permission))
                        return true;
                }
            }

            return false;
        }
    }
}
