@page "/error/network"
@page "/network-error"
@using MudBlazor
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject IJSRuntime JSRuntime

<PageTitle>خطأ في الشبكة</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium" Class="mt-8">
    <MudPaper Elevation="3" Class="pa-8">
        <div dir="rtl" class="text-center">
            <!-- Network Icon -->
            <MudIcon Icon="@Icons.Material.Filled.WifiOff" 
                     Color="Color.Warning" 
                     Size="Size.Large" 
                     Style="font-size: 6rem;" 
                     Class="mb-4" />
            
            <!-- Main Error Message -->
            <MudText Typo="Typo.h4" Class="mb-4">
                مشكلة في الاتصال بالشبكة
            </MudText>
            
            <!-- Description -->
            <MudText Typo="Typo.body1" Color="Color.Secondary" Class="mb-6">
                لا يمكن الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.
            </MudText>
            
            <!-- Connection Status -->
            <MudAlert Severity="@GetConnectionSeverity()" Class="mb-4">
                <div class="d-flex align-center">
                    <MudIcon Icon="@GetConnectionIcon()" Class="ml-2" />
                    <span>حالة الاتصال: @ConnectionStatus</span>
                </div>
            </MudAlert>
            
            <!-- Last Attempt Time -->
            @if (LastAttemptTime.HasValue)
            {
                <MudText Typo="Typo.caption" Color="Color.Secondary" Class="mb-4">
                    آخر محاولة اتصال: @LastAttemptTime.Value.ToString("HH:mm:ss")
                </MudText>
            }
            
            <!-- Action Buttons -->
            <div class="d-flex justify-center flex-wrap gap-3 mb-6">
                <MudButton Variant="Variant.Filled" 
                           Color="Color.Primary" 
                           StartIcon="@Icons.Material.Filled.Refresh"
                           OnClick="CheckConnection"
                           Size="Size.Large"
                           Disabled="@IsChecking">
                    @if (IsChecking)
                    {
                        <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                        <span class="ml-2">جاري فحص الاتصال...</span>
                    }
                    else
                    {
                        <span>فحص الاتصال</span>
                    }
                </MudButton>
                
                <MudButton Variant="Variant.Outlined" 
                           Color="Color.Secondary" 
                           StartIcon="@Icons.Material.Filled.Replay"
                           OnClick="RetryLastAction"
                           Size="Size.Large">
                    إعادة المحاولة
                </MudButton>
                
                <MudButton Variant="Variant.Text" 
                           Color="Color.Info" 
                           StartIcon="@Icons.Material.Filled.Home"
                           OnClick="NavigateToHome"
                           Size="Size.Large">
                    الصفحة الرئيسية
                </MudButton>
            </div>
            
            <!-- Troubleshooting Tips -->
            <MudExpansionPanels Elevation="0" Class="mb-6">
                <MudExpansionPanel Text="نصائح لحل المشكلة">
                    <div dir="rtl" class="text-right">
                        <MudList T="bool">
                            <MudListItem Icon="@Icons.Material.Filled.Wifi">
                                <MudText>تأكد من أن جهازك متصل بالإنترنت</MudText>
                            </MudListItem>
                            <MudListItem Icon="@Icons.Material.Filled.Router">
                                <MudText>تحقق من إعدادات الراوتر أو نقطة الوصول</MudText>
                            </MudListItem>
                            <MudListItem Icon="@Icons.Material.Filled.Security">
                                <MudText>تأكد من أن جدار الحماية لا يحجب الاتصال</MudText>
                            </MudListItem>
                            <MudListItem Icon="@Icons.Material.Filled.Refresh">
                                <MudText>جرب إعادة تشغيل المتصفح أو التطبيق</MudText>
                            </MudListItem>
                            <MudListItem Icon="@Icons.Material.Filled.ContactSupport">
                                <MudText>اتصل بمزود خدمة الإنترنت إذا استمرت المشكلة</MudText>
                            </MudListItem>
                        </MudList>
                    </div>
                </MudExpansionPanel>
            </MudExpansionPanels>
            
            <!-- Auto-retry Settings -->
            <MudDivider Class="my-4" />
            
            <div class="d-flex justify-center align-center gap-4">
                <MudSwitch T="bool" @bind-Checked="AutoRetryEnabled"
                           Label="إعادة المحاولة التلقائية" 
                           Color="Color.Primary" />
                
                @if (AutoRetryEnabled && NextRetryIn > 0)
                {
                    <MudText Typo="Typo.caption" Color="Color.Info">
                        المحاولة التالية خلال: @NextRetryIn ثانية
                    </MudText>
                }
            </div>
            
            <!-- Help Section -->
            <MudDivider Class="my-6" />
            
            <div class="d-flex justify-center flex-wrap gap-2">
                <MudChip T="bool" Icon="@Icons.Material.Filled.ContactSupport"
                         Color="Color.Success" 
                         Variant="Variant.Outlined"
                         OnClick="ContactSupport">
                    الاتصال بالدعم
                </MudChip>
                
                <MudChip T="bool" Icon="@Icons.Material.Filled.Settings"
                         Color="Color.Info" 
                         Variant="Variant.Outlined"
                         OnClick="ShowNetworkSettings">
                    إعدادات الشبكة
                </MudChip>
            </div>
        </div>
    </MudPaper>
</MudContainer>

@code {
    private string ConnectionStatus = "غير متصل";
    private bool IsChecking = false;
    private bool AutoRetryEnabled = true;
    private int NextRetryIn = 0;
    private DateTime? LastAttemptTime;
    private Timer? retryTimer;
    private Timer? countdownTimer;
    
    protected override async Task OnInitializedAsync()
    {
        await CheckConnection();
        
        if (AutoRetryEnabled)
        {
            StartAutoRetry();
        }
    }
    
    private async Task CheckConnection()
    {
        IsChecking = true;
        LastAttemptTime = DateTime.Now;
        StateHasChanged();
        
        try
        {
            // Check if navigator.onLine is available and true
            var isOnline = await JSRuntime.InvokeAsync<bool>("eval", "navigator.onLine");
            
            if (isOnline)
            {
                ConnectionStatus = "متصل";
                Snackbar.Add("تم استعادة الاتصال بالشبكة", Severity.Success);
                
                // Try to navigate back or refresh
                await Task.Delay(1000);
                Navigation.NavigateTo(Navigation.Uri, forceLoad: true);
            }
            else
            {
                ConnectionStatus = "غير متصل";
            }
        }
        catch (Exception ex)
        {
            ConnectionStatus = "خطأ في فحص الاتصال";
            Console.WriteLine($"Connection check failed: {ex.Message}");
        }
        finally
        {
            IsChecking = false;
            StateHasChanged();
        }
    }
    
    private Severity GetConnectionSeverity()
    {
        return ConnectionStatus switch
        {
            "متصل" => Severity.Success,
            "غير متصل" => Severity.Error,
            _ => Severity.Warning
        };
    }
    
    private string GetConnectionIcon()
    {
        return ConnectionStatus switch
        {
            "متصل" => Icons.Material.Filled.Wifi,
            "غير متصل" => Icons.Material.Filled.WifiOff,
            _ => Icons.Material.Filled.Warning
        };
    }
    
    private async Task RetryLastAction()
    {
        await CheckConnection();
    }
    
    private void NavigateToHome()
    {
        try
        {
            Navigation.NavigateTo("/", forceLoad: true);
        }
        catch (Exception ex)
        {
            Snackbar.Add("لا يمكن الانتقال بدون اتصال بالشبكة", Severity.Warning);
        }
    }
    
    private void StartAutoRetry()
    {
        NextRetryIn = 30; // 30 seconds
        
        // Countdown timer
        countdownTimer = new Timer(async _ =>
        {
            NextRetryIn--;
            if (NextRetryIn <= 0)
            {
                await InvokeAsync(async () =>
                {
                    await CheckConnection();
                    if (ConnectionStatus == "غير متصل")
                    {
                        NextRetryIn = 30; // Reset for next retry
                    }
                    else
                    {
                        countdownTimer?.Dispose();
                        retryTimer?.Dispose();
                    }
                    StateHasChanged();
                });
            }
            else
            {
                await InvokeAsync(StateHasChanged);
            }
        }, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
    }
    
    private void ContactSupport()
    {
        Snackbar.Add("يرجى الاتصال بالدعم الفني: 123-456-7890", Severity.Info, config =>
        {
            config.VisibleStateDuration = 8000;
        });
    }
    
    private void ShowNetworkSettings()
    {
        Snackbar.Add("يرجى فحص إعدادات الشبكة في نظام التشغيل", Severity.Info);
    }
    
    public void Dispose()
    {
        retryTimer?.Dispose();
        countdownTimer?.Dispose();
    }
}
