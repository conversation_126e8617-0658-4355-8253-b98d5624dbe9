﻿using AutoMapper;
using PosGTech.Models;
using PosGTech.ModelsDTO.Sells;

namespace PosGTech.DataAccess.Repository.IRepository
{
    public interface ISellRepository : IRepository<Sell>
    {
        Task<Guid> AddSell(Sell sell, string userId, decimal paid = 0, Guid? treasuryId = null);
        Task UpdateSell(Sell oldSell, Sell newSell);
        Task DeleteSell(Sell sell);
        Task<SellCMDTO> ReportSell(Guid id, IMapper mapper);


    }
}
