using Microsoft.EntityFrameworkCore;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository;
using PosGTech.Models;

namespace PosGTech.Tests.Repositories
{
    public class StoreRepositoryTests : IDisposable
    {
        private readonly ApplicationDbContext _context = new ApplicationDbContext(new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase("TestDatabase")
                .Options);

        [Fact]
        public async Task GetAll_ReturnsAllStores()
        {
            // Arrange
            var expectedStores = new List<Store>
            {
                new Store { 
                    Id = Guid.NewGuid(), 
                    Name = "Store1",
                    Address = "Address1"
                },
                new Store { 
                    Id = Guid.NewGuid(), 
                    Name = "Store2",
                    Address = "Address2"
                }
            };
            await _context.Stores.AddRangeAsync(expectedStores);
            await _context.SaveChangesAsync();
            var repository = new StoreRepository(_context);

            // Act
            var result = await repository.GetAll();

            // Assert
            Assert.Equal(expectedStores.Count, result.Count());
        }

        [Fact]
        public async Task Add_SavesStore()
        {
            // Arrange
            var store = new Store { 
                Id = Guid.NewGuid(),
                Name = "NewStore",
                Address = "NewAddress"
            };
            var repository = new StoreRepository(_context);

            // Act
            repository.Add(store);

            // Assert
            var savedStore = await _context.Stores.FindAsync(store.Id);
            Assert.NotNull(savedStore);
            Assert.Equal(store.Name, savedStore.Name);
            Assert.Equal(store.Address, savedStore.Address);
        }

        [Fact]
        public async Task Update_UpdatesStore()
        {
            // Arrange
            var store = new Store { 
                Id = Guid.NewGuid(),
                Name = "OriginalStore",
                Address = "OriginalAddress"
            };
            await _context.Stores.AddAsync(store);
            await _context.SaveChangesAsync();
            var repository = new StoreRepository(_context);

            // Act
            store.Name = "UpdatedStore";
            store.Address = "UpdatedAddress";
            repository.Update(store);

            // Assert
            var updatedStore = await _context.Stores.FindAsync(store.Id);
            Assert.NotNull(updatedStore);
            Assert.Equal("UpdatedStore", updatedStore.Name);
            Assert.Equal("UpdatedAddress", updatedStore.Address);
        }

        [Fact]
        public async Task Delete_RemovesStore()
        {
            // Arrange
            var store = new Store { 
                Id = Guid.NewGuid(),
                Name = "StoreToDelete",
                Address = "AddressToDelete"
            };
            await _context.Stores.AddAsync(store);
            await _context.SaveChangesAsync();
            var repository = new StoreRepository(_context);

            // Act
            repository.Remove(store);
            await _context.SaveChangesAsync();

            // Assert
            var deletedStore = await _context.Stores.FindAsync(store.Id);
            Assert.Null(deletedStore);
        }

        [Fact]
        public async Task GetById_ReturnsStoreById()
        {
            // Arrange
            var store = new Store { 
                Id = Guid.NewGuid(),
                Name = "TestStore",
                Address = "TestAddress"
            };
            await _context.Stores.AddAsync(store);
            await _context.SaveChangesAsync();
            var repository = new StoreRepository(_context);

            // Act
            var result = await repository.GetByIdAsync(store.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(store.Name, result.Name);
            Assert.Equal(store.Address, result.Address);
        }

        public void Dispose()
        {
            _context.Database.EnsureDeleted();
            _context.Dispose();
        }
    }
}
