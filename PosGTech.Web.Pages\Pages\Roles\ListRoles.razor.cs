﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.Roles;
using PosGTech.Models.ViewModels;
using System.Net.Http.Json;

namespace PosGTech.Web.Pages.Pages.Roles;

public partial class ListRoles
{
    [Inject] PosGTech.Web.Services.Interfaces.IGRepository<RoleDTO> _roleService { get; set; }
    [Inject] IDialogService _dialogService { get; set; }

    IEnumerable<RoleDTO> roles = new List<RoleDTO>();
    RoleDTO selectedItem = null;
    bool loading = true;
    string Search = "";
    string NameRoleForDelete;
    MudMessageBox mbox { get; set; }

    protected override async Task OnInitializedAsync() => await LoadingData();

    private bool FilterFunc1(RoleDTO element) => FilterFunc(element, Search);

    private bool FilterFunc(RoleDTO element, string searchString)
    {
        if (string.IsNullOrWhiteSpace(searchString))
            return true;
        if (element.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase))
            return true;
        return false;
    }

    async Task LoadingData()
    {
        loading = true;
        StateHasChanged();

        var res = await _roleService.GetAll("Roles/getAllRoles");
        if (res.response == null)
        {
            roles = res.list;
        }
        else
        {
            _snackbar.Add("خطأ في تحميل الأدوار", Severity.Error);
        }
        loading = false;
        StateHasChanged();
    }

    void NavigateToUpsertRole(Guid id)
    {
        _navigation.NavigateTo($"/upsertRole/{id}");
    }

    async void DeleteRole(RoleDTO obj)
    {
        NameRoleForDelete = obj.Name;
        bool? result = await mbox.ShowAsync();
        if (result == true)
        {
            var response = await _roleService.Delete("Roles/deleteRole", obj.Id!.Value);
            if (response.State)
            {
                _snackbar.Add(response.Message, Severity.Success);
                await LoadingData();
            }
            else _snackbar.Add(response.Message, Severity.Error);
        }
    }

    private string GetRoleIcon(string roleName)
    {
        return roleName switch
        {
            "SystemAdmin" => Icons.Material.Filled.AdminPanelSettings,
            "SalesEmployee" => Icons.Material.Filled.PointOfSale,
            "PurchaseManager" => Icons.Material.Filled.ShoppingCart,
            "Accountant" => Icons.Material.Filled.AccountBalance,
            "InventoryManager" => Icons.Material.Filled.Inventory,
            _ => Icons.Material.Filled.Security
        };
    }

    private Color GetRoleColor(RoleDTO role)
    {
        if (role.IsSystemRole)
            return Color.Success;
        return Color.Primary;
    }

    private string GetGroupIcon(string groupName)
    {
        return groupName switch
        {
            "إدارة المستخدمين" => Icons.Material.Filled.People,
            "إدارة الأصناف" => Icons.Material.Filled.Category,
            "إدارة المبيعات" => Icons.Material.Filled.PointOfSale,
            "إدارة المشتريات" => Icons.Material.Filled.ShoppingCart,
            "إدارة المخزون" => Icons.Material.Filled.Inventory,
            "إدارة الحسابات" => Icons.Material.Filled.AccountBalance,
            "إدارة العملاء" => Icons.Material.Filled.Person,
            "إدارة الموردين" => Icons.Material.Filled.Business,
            "إدارة الخزائن" => Icons.Material.Filled.AccountBalanceWallet,
            "إدارة التقارير" => Icons.Material.Filled.Assessment,
            "إدارة النظام" => Icons.Material.Filled.Settings,
            _ => Icons.Material.Filled.Security
        };
    }
}