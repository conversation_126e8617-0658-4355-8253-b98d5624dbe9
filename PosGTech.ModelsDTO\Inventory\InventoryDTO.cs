﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Inventories;

public class InventoryDTO
{
    public Guid Id { get; set; }
    public int InvoiceNo { get; set; } = 0;
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public DateOnly Date { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
    public decimal Total { get; set; }
    public string? Note { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public Guid? StoreId { get; set; }
    public List<InventoryItemDTO>? InventoryItemDTOs { get; set; } = new List<InventoryItemDTO>();
}
