﻿using Microsoft.AspNetCore.Identity;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;


namespace PosGTech.DataAccess.Repository
{
    public class UnitOfWork : IUnitOfWork
    {
        private ApplicationDbContext _db;
        public ICategoryRepository Category { get; private set; }
        public IClientRepository Client { get; private set; }
        public IConsumedItemRepository ConsumedItem { get; private set; }
        public IConsumedRepository Consumed { get; private set; }
        public IEmployeeRepository Employee { get; private set; }
        public IExpenseRepository Expense { get; private set; }
        public ISellItemRepository SellItem { get; private set; }
        public IItemRepository Item { get; private set; }
        public IPurchaseItemRepository PurchaseItem { get; private set; }
        public IPurchaseRepository Purchase { get; private set; }
        public IReceiptRepository Receipt { get; private set; }
        public ISalaryRepository Salary { get; private set; }
        public ISellRepository Sell { get; private set; }
        public ITreasuryRepository Treasury { get; private set; }
        public IUserRepository User { get; private set; }
        public IUserTreasuryRepository UserTreasury { get; private set; }

        public IFinancialRepository Financial { get; private set; }

        public IInventoryRepository Inventory { get; private set; }

        public IInventoryItemRepository InventoryItem { get; private set; }

        public IItemNumRepository ItemNum { get; private set; }

        public IItemUnitRepository ItemUnit { get; private set; }

        public IStoreItemExpRepository StoreItemExp { get; private set; }

        public IStoreItemRepository StoreItem { get; private set; }

        public IStoreRepository Store { get; private set; }

        public IShopSettingsRepository ShopSettings { get; private set; }

        public ITransferItemRepository TransferItem { get; private set; }

        public ITransferRepository Transfer { get; private set; }

        public IUnitRepository Unit { get; private set; }

        // ==================== Repositories جديدة لإدارة الأدوار والصلاحيات ====================
        public IRoleRepository Role { get; private set; }
        public IUserClaimRepository UserClaim { get; private set; }

        // Managers للـ Identity
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<Models.Role> _roleManager;

        public UnitOfWork(ApplicationDbContext db, UserManager<User> userManager, RoleManager<Models.Role> roleManager)
        {
            _db = db;
            _userManager = userManager;
            _roleManager = roleManager;

            Unit = new UnitRepository(_db);
            InventoryItem = new InventoryItemRepository(_db);
            TransferItem = new TransferItemRepository(_db);
            Transfer = new TransferRepository(_db);
            Store = new StoreRepository(_db);
            ShopSettings = new ShopSettingsRepository(_db);
            ItemNum = new ItemNumRepository(_db);
            ItemUnit = new ItemUnitRepository(_db);
            StoreItemExp = new StoreItemExpRepository(_db, ItemUnit);
            Category = new CategoryRepository(_db);
            Client = new ClientRepository(_db);
            Employee = new EmployeeRepository(_db);
            Expense = new ExpenseRepository(_db);
            SellItem = new SellItemRepository(db);
            Item = new ItemRepository(_db, ItemNum, ItemUnit);
            PurchaseItem = new PurchaseItemRepository(_db);
            Salary = new SalaryRepository(_db);
            Treasury = new TreasuryRepository(_db);
            UserTreasury = new UserTreasuryRepository(_db);
            User = new UserRepository(_db, UserTreasury, _roleManager);
            ConsumedItem = new ConsumedItemRepository(_db);
            Consumed = new ConsumedRepository(_db, Item, ConsumedItem);
            Financial = new FinancialRepository(_db);
            Receipt = new ReceiptRepository(_db, Client, Employee, UserTreasury);
            Sell = new SellRepository(_db, SellItem, Client, Receipt, StoreItemExp);
            StoreItem = new StoreItemRepository(_db, ItemUnit, Item, StoreItemExp);
            Inventory = new InventoryRepository(_db, StoreItem, InventoryItem);
            Purchase = new PurchaseRepository(_db, StoreItem, PurchaseItem, Receipt, Client);

            // ==================== تهيئة Repositories الجديدة ====================
            Role = new RoleRepository(_roleManager, _userManager, _db);
            UserClaim = new UserClaimRepository(_userManager, _roleManager);
        }

        public async Task SaveAsync()
        {
            await _db.SaveChangesAsync();
        }

        public void Dispose()
        {
            _db.Dispose();
        }
        public async Task<ResponseVM> SaveWithTransaction()
        {

            using (var transaction = _db.Database.BeginTransaction())
            {
                try
                {
                    await _db.SaveChangesAsync();
                    transaction.Commit();
                    return new ResponseVM() { State = true, Message = "تم الحفظ" };
                }

                catch (Exception ex)
                {
                    transaction.Rollback();
                    Reverse();
                    return new ResponseVM() { State = false, Message = HandleException(ex) };
                }


            }
        }

        public void Reverse()
        {
            var changedEntries = _db.ChangeTracker.Entries().Where(x => x.State != EntityState.Unchanged).ToList(); // get all the changed entries
            foreach (var entry in changedEntries)
            {
                switch (entry.State)
                {
                    case EntityState.Modified:
                        entry.CurrentValues.SetValues(entry.OriginalValues); // reset the current values to the original values
                        entry.State = EntityState.Unchanged; // mark the entry as unchanged
                        break;
                    case EntityState.Added:
                        entry.State = EntityState.Detached; // detach the entry from the context
                        break;
                    case EntityState.Deleted:
                        entry.State = EntityState.Unchanged; // mark the entry as unchanged
                        break;
                }
            }
        }
        string HandleException(Exception exception)
        {
            if (exception is DbUpdateException dbUpdateEx)
            {
                if (dbUpdateEx.InnerException != null)
                {
                    if (dbUpdateEx.InnerException is SqlException sqlException)
                    {
                        switch (sqlException.Number)
                        {
                            case 547:
                                return "لا يمكن الحذف هذه البيانات مستخدمة";
                            case 2601:
                                return "هذه البيانات موجودة مسبقا";
                            default:
                                return "خطا في الاتصال";
                        }
                    }

                }
            }
            return exception.Message;

        }
    }
}
