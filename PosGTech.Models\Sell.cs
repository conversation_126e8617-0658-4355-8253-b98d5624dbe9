﻿namespace PosGTech.Models;

/// <summary>
/// جدول فواتير المبيعات
/// </summary>
public class Sell : BaseEntity
{
    public int InvoiceNo { get; set; }
    public DateOnly Date { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
    public decimal Total { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
    public decimal DiscountValue { get; set; } = decimal.Zero;
    public bool IsDiscountValue { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
    public decimal FinalTotal { get; set; }
    public Guid? ClientId { get; set; }
    public Client? Client { get; set; }
    public Guid StoreId { get; set; }
    public Store? Store { get; set; }
    public ICollection<SellItem>? SellItems { get; set; }
    public ICollection<Receipt>? Receipts { get; set; }
}
