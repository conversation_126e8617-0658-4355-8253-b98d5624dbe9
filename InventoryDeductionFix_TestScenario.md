# إصلاح مشكلة خصم الكمية من المخزون للوحدات الفرعية

## المشكلة الأصلية:
عند بيع الوحدات الفرعية (Sub-units) مثل الكراتين، كان النظام يخصم كمية خاطئة من المخزون.

### مثال على المشكلة:
- **الصنف**: بسكويت
- **الوحدة الأساسية**: قطعة (IsBasicUnit = true)
- **الوحدة الفرعية**: كرتونة (IsBigger = true, Quantity = 24)
- **المخزون المتاح**: 100 قطعة

#### السيناريو الخاطئ (قبل الإصلاح):
1. المستخدم يبيع 1 كرتونة
2. الواجهة تحفظ `Quantity = 1` في جدول `SellItems`
3. قاعدة البيانات تحول `1 * 24 = 24` مرة أخرى
4. **النتيجة الخاطئة**: يخصم 24 كرتونة (576 قطعة) بدلاً من 1 كرتونة (24 قطعة)

## الحل المطبق:

### 1. تحديد سبب المشكلة:
- **التحويل المضاعف**: كان يحدث تحويل في الواجهة وتحويل آخر في قاعدة البيانات
- **الواجهة**: تحول الكمية بشكل صحيح للعرض
- **قاعدة البيانات**: تحول الكمية مرة أخرى قبل الخصم

### 2. الحل المطبق:

#### أ. إنشاء دالة مساعدة في `SellRepository`:
```csharp
private async Task<List<ItemForChangeQte>> ConvertSellItemsToBasicUnit(IEnumerable<SellItem> sellItems)
{
    var itemsForQuantityChange = new List<ItemForChangeQte>();
    foreach (var sellItem in sellItems)
    {
        var itemUnit = await _db.ItemUnits.FindAsync(sellItem.ItemUnitId);
        if (itemUnit != null)
        {
            // تحويل كمية البيع إلى الوحدة الأساسية
            var basicUnitQuantity = ItemExtensions.GetQuantityUnit(itemUnit, sellItem.Quantity);
            itemsForQuantityChange.Add(new ItemForChangeQte() 
            { 
                ItemId = sellItem.StoreItemExpId, 
                Quantity = basicUnitQuantity, 
                ItemUnitId = sellItem.ItemUnitId 
            });
        }
    }
    return itemsForQuantityChange;
}
```

#### ب. إنشاء دوال جديدة في `StoreItemExpRepository`:
```csharp
// خصم الكمية مباشرة بدون تحويل إضافي
public async Task UpdateReduceQuantityDirectly(IEnumerable<ItemForChangeQte> itemForChangeQte)

// إضافة الكمية مباشرة بدون تحويل إضافي  
public async Task AddQuantityDirectly(IEnumerable<ItemForChangeQte> itemForChangeQte)
```

#### ج. تحديث دوال المبيعات:
- `AddSell`: تحويل الكميات قبل الخصم
- `UpdateSell`: تحويل الكميات للإضافة والخصم
- `DeleteSell`: تحويل الكميات قبل الإضافة

### 3. السيناريو الصحيح (بعد الإصلاح):

#### مثال: بيع 1 كرتونة
1. المستخدم يبيع 1 كرتونة
2. الواجهة تحفظ `Quantity = 1` في جدول `SellItems`
3. `SellRepository` يحول `1 * 24 = 24` قطعة
4. `UpdateReduceQuantityDirectly` يخصم 24 قطعة مباشرة
5. **النتيجة الصحيحة**: يخصم 24 قطعة (1 كرتونة) من المخزون ✅

#### مثال: بيع 2 كرتونة
1. المستخدم يبيع 2 كرتونة
2. الواجهة تحفظ `Quantity = 2` في جدول `SellItems`
3. `SellRepository` يحول `2 * 24 = 48` قطعة
4. `UpdateReduceQuantityDirectly` يخصم 48 قطعة مباشرة
5. **النتيجة الصحيحة**: يخصم 48 قطعة (2 كرتونة) من المخزون ✅

## الملفات المعدلة:

1. **PosGTech.DataAccess/Repository/SellRepository.cs**
   - إضافة دالة `ConvertSellItemsToBasicUnit`
   - تحديث `AddSell`, `UpdateSell`, `DeleteSell`

2. **PosGTech.DataAccess/Repository/StoreItemExpRepository.cs**
   - إضافة `UpdateReduceQuantityDirectly`
   - إضافة `AddQuantityDirectly`

3. **PosGTech.DataAccess/Repository/IRepository/IStoreItemExpRepository.cs**
   - إضافة تعريف الدوال الجديدة

## النتيجة:
- ✅ إصلاح مشكلة الخصم المضاعف للوحدات الفرعية
- ✅ الحفاظ على الوظائف الموجودة للوحدات الأساسية
- ✅ دعم جميع عمليات المبيعات (إضافة، تحديث، حذف)
- ✅ عدم تأثير على الواجهة أو منطق العرض
