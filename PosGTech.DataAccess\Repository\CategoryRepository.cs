﻿using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Categories;

namespace PosGTech.DataAccess.Repository
{
    public class CategoryRepository : Repository<Category>, ICategoryRepository
    {
        public CategoryRepository(ApplicationDbContext db) : base(db)
        {
        }

        public void UpdateCategory(Category oldCategory, CategoryDTO newCategory)
        {
            oldCategory.Name = newCategory.Name;
            oldCategory.UpdatedAt = DateTime.Now;
            Update(oldCategory);
        }
    }
}
