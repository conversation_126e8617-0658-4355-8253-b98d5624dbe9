﻿<CascadingAuthenticationState>
    <CustomErrorBoundary>
        <Router AppAssembly="@typeof(App).Assembly" AdditionalAssemblies="new[]{typeof(PosGTech.Web.Pages.Pages.Home).Assembly}">
            <Found Context="routeData">
                <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
                    <NotAuthorized>
                        @if (context.User.Identity?.IsAuthenticated != true)
                        {
                            <RedirectToLogin />
                        }
                        else
                        {
                            <p role="alert">انت غير مصرح لك بالدخول.</p>
                        }
                    </NotAuthorized>
                </AuthorizeRouteView>
                <FocusOnNavigate RouteData="@routeData" Selector="h1" />
            </Found>
            <NotFound>
                <PageTitle>الصفحة غير موجودة</PageTitle>
                <LayoutView Layout="@typeof(MainLayout)">
                    <PosGTech.Web.Pages.Pages.Error.NotFound />
                </LayoutView>
            </NotFound>
        </Router>
    </CustomErrorBoundary>
</CascadingAuthenticationState>
