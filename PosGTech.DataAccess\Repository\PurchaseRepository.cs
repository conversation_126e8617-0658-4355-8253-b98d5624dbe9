﻿using AutoMapper;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO;
using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.Finacnial;
using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.Purchases;

namespace PosGTech.DataAccess.Repository
{
    public class PurchaseRepository(ApplicationDbContext db, IStoreItemRepository _storeItemRepository,
    IPurchaseItemRepository _purchaseItemRepository,
    IReceiptRepository _receiptRepository,
    IClientRepository _clientRepository) : Repository<Purchase>(db), IPurchaseRepository
    {

        public async Task<Guid> AddPurchase(Purchase purchase, decimal paid = 0, Guid? TreasuryId = null)
        {

            await _storeItemRepository.AddQuantity(purchase.PurchaseItems.Select(x => new ItemForChangeQte() { Quantity = x.Quantity,
                CostPrice = x.PriceAfterDiscount, ItemId = x.ItemId, Exp = x.Exp, ItemUnitId = x.ItemUnitId }).ToList(), purchase.StoreId);
            purchase.InvoiceNo = ((await GetLastOrDefault(order: x => x.CreatedAt, tracked: false))?.InvoiceNo).GetValueOrDefault(0) + 1;
            await _clientRepository.UpdateClientBalanceInc(purchase.ClientId.GetValueOrDefault(), purchase.FinalTotal);
            if (paid == 0) Add(purchase);
            else await _receiptRepository.AddReceipt(new() { Purchase = purchase, ClientId = purchase.ClientId, UserTreasuryId = TreasuryId, Date = DateTime.Now, FinancialId = FinancialId.Purchase, IsExchange = true, Value = paid });
            return purchase.Id;
        }
        public async Task UpdatePurchase(Purchase oldPurchase, Purchase newPurchase)
        {
            await _storeItemRepository.UpdateReduceQuantity(oldPurchase.PurchaseItems.Where(x => x.ReturnQuantity == 0).Select(x => new ItemForChangeQte() { Quantity = x.Quantity, CostPrice = x.PriceAfterDiscount, ItemId = x.ItemId, Exp = x.Exp, ItemUnitId = x.ItemUnitId }).ToList(), oldPurchase.StoreId);
            _purchaseItemRepository.RemoveRange(oldPurchase.PurchaseItems.Where(x => x.ReturnQuantity == 0));
            await _storeItemRepository.AddQuantity(newPurchase.PurchaseItems.Where(x => x.ReturnQuantity == 0).Select(x => new ItemForChangeQte() { Quantity = x.Quantity, CostPrice = x.PriceAfterDiscount, ItemId = x.ItemId, Exp = x.Exp, ItemUnitId = x.ItemUnitId }).ToList(), oldPurchase.StoreId);
            oldPurchase.PurchaseItems = newPurchase.PurchaseItems;
            _purchaseItemRepository.AddRange(newPurchase.PurchaseItems.Where(x => x.ReturnQuantity == 0).ToList());
            await _clientRepository.UpdateClientBalanceDec(oldPurchase.ClientId.GetValueOrDefault(), oldPurchase.FinalTotal);
            await _clientRepository.UpdateClientBalanceInc(newPurchase.ClientId.GetValueOrDefault(), newPurchase.FinalTotal);

            oldPurchase.Total = newPurchase.Total;
            oldPurchase.Note = newPurchase.Note;
            oldPurchase.FinalTotal = newPurchase.FinalTotal;
            oldPurchase.StoreId = newPurchase.StoreId;
            oldPurchase.Date = newPurchase.Date;
            oldPurchase.ClientId = newPurchase.ClientId;
            oldPurchase.UpdatedAt = DateTime.Now;
            Update(oldPurchase);

        }
        public async Task DeletePurchase(Purchase purchase)
        {
            await _storeItemRepository.UpdateReduceQuantity(purchase.PurchaseItems.Select(x => new ItemForChangeQte() { Quantity = x.Quantity, CostPrice = x.Price, ItemId = x.ItemId, Exp = x.Exp, ItemUnitId = x.ItemUnitId }).ToList(), purchase.StoreId);
            await _clientRepository.UpdateClientBalanceDec(purchase.ClientId.GetValueOrDefault(), purchase.FinalTotal);
            Remove(purchase);
        }

        public async Task<IEnumerable<PurchaseDTO>> GetAllPurchase(IMapper mapper)
        {

            return await Select(x => new PurchaseDTO()
            {
                Id = x.Id,
                Date = x.Date,
                Client = mapper.Map<Client, ClientCMDTO>(x.Client),
                InvoiceNo = x.InvoiceNo,
                Note = x.Note,
                FinalTotal = x.Total,
                Paid = x.Receipts.Sum(z => z.Value)
            });


        }
        public async Task<PurchaseCMDTO> ReportPurchase(Guid id, IMapper mapper)
        {

            return (await Select(purchase => new PurchaseCMDTO()
            {
                Total = purchase.Total,
                DiscountValue = purchase.DiscountValue,
                Date = purchase.Date,
                FinalTotal = purchase.FinalTotal,
                InvoiceNo = purchase.InvoiceNo,
                ClientName = purchase.Client.Name,
                PurchaseItemDTOs = mapper.Map<IEnumerable<PurchaseItemDTO>>(purchase.PurchaseItems).ToList()
            }, x => x.Id == id, includeProperties: "PurchaseItems,PurchaseItems.Item,PurchaseItems.ItemUnit.Unit,Client")).First();


        }

        public async Task<PurchaseDTO> GetPurchaseById(IMapper mapper, Guid id)
        {
            return (await Select(x => new PurchaseDTO()
            {
                Id = x.Id,
                Date = x.Date,
                Client = mapper.Map<Client, ClientCMDTO>(x.Client),
                InvoiceNo = x.InvoiceNo,
                PurchaseItemDTOs = mapper.Map<ICollection<PurchaseItem>, List<PurchaseItemDTO>>(x.PurchaseItems),
                Note = x.Note,
                FinalTotal = x.FinalTotal,
                StoreId = x.StoreId,
                Total = x.Total,
                Paid = x.Receipts.Sum(z => z.Value)
            }, x => x.Id == id, includeProperties: "Client,PurchaseItems.Item,PurchaseItems.ItemUnit.Unit")).First();
        }

        public async Task<PurchaseDTO?> GetPurchaseByNum(IMapper mapper, int num)
        {
            return (await Select(x => new PurchaseDTO()
            {
                Id = x.Id,
                Client = mapper.Map<Client, ClientCMDTO>(x.Client),
                InvoiceNo = x.InvoiceNo,
                Note = x.Note,
                FinalTotal = x.FinalTotal,
                StoreId = x.StoreId,
                Total = x.Total,
                Paid = x.Receipts.Sum(z => z.Value)
            }, x => x.InvoiceNo == num)).FirstOrDefault();
        }



    }
}
