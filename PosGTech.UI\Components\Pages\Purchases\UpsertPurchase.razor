﻿@page "/upsertPurchase/{id:guid}"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]


<MudPaper Class="d-flex flex-column  desktop rounded-0" Elevation="0">


    <EditForm Model="@purchase" id="my-form" @onkeydown="@keydownForm" @ref=Form>
        <DataAnnotationsValidator />
        <MudPaper Class="d-flex flex-column desktop px-2">

            <MudPaper Height="5%" Elevation="0" Class="d-flex align-content-end justify-end">
                <MudButton Color="@Color.Secondary" Variant="@Variant.Outlined" OnClick="Back" EndIcon="@Icons.Material.Filled.ArrowBack" Class="px-6">
                    <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">رجوع</MudText>
                </MudButton>
            </MudPaper>

            <MudPaper Height="45%" Elevation="0">

                <MudGrid>
                    <MudItem xs="3">
                        <MudSelect T="Guid?" @bind-Value="purchase.StoreId" Label="المخازن" Disabled="purchase.Id!=Guid.Empty" AdornmentIcon="@Icons.Material.Filled.OpenWith" For="@(() =>purchase.StoreId)">
                            <MudSelectItem T="Guid?" Value="null">اختيار </MudSelectItem>
                            @foreach (var store in stores)
                            {

                                <MudSelectItem T="Guid?" Value="store.Id"> @store.Name </MudSelectItem>

                            }
                        </MudSelect>
                    </MudItem>
                    <MudItem xs="3">
                        <MudTextField T="int" Value="purchase.InvoiceNo" @onkeydown="@keydown" Disabled=true Label="رقم الفاتورة" For="@(() => purchase.InvoiceNo)"
                                      Class="invoice-number-field"
                                      Variant="Variant.Outlined"
                                      Style="min-width: 180px;" />
                    </MudItem>
                    <MudItem xs="3">
                        <MudDatePicker PickerVariant="PickerVariant.Dialog" @onkeydown="@keydown" tabindex=0 Label="التاريخ" Editable="true"
                                       @bind-Date="_datePurchase" Mask="@(new DateMask("0000-00-00"))"
                                       DateFormat="yyyy-MM-dd" />
                    </MudItem>
                    <MudItem xs="2">
                        <MudAutocomplete T="ClientCMDTO" Label="المورد" tabindex=1 @onkeydown="@keydown" Value="purchase.Client" ValueChanged="ChangeClient"
                                         ResetValueOnEmptyText="true"
                                         CoerceText="true" CoerceValue="true"
                                         SearchFunc="@SearchClient" ToStringFunc="@(e=> e==null?null : $"{e.Name}")" For="@(() => purchase.Client)" Disabled="purchase.Id!=Guid.Empty"/>
                    </MudItem>

                    <MudItem md="1" Class="d-flex align-end">
                        <MudFab Style="height:36px;width:36px;align-self:end" StartIcon="@Icons.Material.Filled.Add" @onclick="AddNewClient" Color="Color.Success" Class="ml-auto" />
                    </MudItem>

                  
                    <MudItem xs="3">
                        <MudAutocomplete T="ItemCMDTO" @ref="ItemForAdd" Label="الصنف" @onkeydown="@SelectItem" tabindex=3 Value="selectedPurchaseItem.Item" ValueChanged="ChangeItem"
                                         ResetValueOnEmptyText="true"
                                         CoerceText="true" CoerceValue="true"
                                         SearchFunc="@SearchItem" ToStringFunc="@(e=> e==null?null : $"{e.Name}")" />
                    </MudItem>
                    <MudItem md="1" Class="d-flex align-end">
                        <MudFab Style="height:36px;width:36px;align-self:end" StartIcon="@Icons.Material.Filled.Add" @onclick="AddNewItem" Color="Color.Success" Class="ml-auto" />
                    </MudItem>
                    @if (selectedPurchaseItem.Item is not null)
                    {
                        <MudItem xs="3">
                            <MudSelect T="ItemUnitDTO" Value="selectedPurchaseItem.ItemUnit" @onkeydown="@keydown" ValueChanged="ChangeItemUnit" tabindex=4 Label="الوحدة" AdornmentIcon="@Icons.Material.Filled.OpenWith" For="@(() =>selectedPurchaseItem.ItemUnit)">
                                @foreach (var unit in items.First(x => x.Id == selectedPurchaseItem.Item.Id).ItemUnits)
                                {
                                    <MudSelectItem T="ItemUnitDTO" Value="unit"> @unit.Unit.Name </MudSelectItem>
                                }
                            </MudSelect>
                        </MudItem>
                   
                            <MudItem xs="3">
                            <MudDatePicker PickerVariant="PickerVariant.Dialog"  @onkeydown="@keydown" tabindex=5 Label="تاريخ الصلاحية" Disabled="@(!items.First(x => x.Id == selectedPurchaseItem.Item.Id).IsHaveExp)"
                                           @bind-Date=_dateExp Mask="@(new DateMask("0000-00-00"))"
                                           DateFormat="yyyy-MM-dd" For="@(() =>_dateExp)" />
                            </MudItem>
                    }

                    <MudItem xs="3">
                        <MudNumericField T="decimal" HideSpinButtons="true" Min="0" @onkeydown="@keydown" tabindex=6 Disabled="selectedPurchaseItem.Item==null" Value="selectedPurchaseItem.Quantity" ValueChanged="ChangeQte" Label="الكمية" />
                    </MudItem>
                    <MudItem xs="3">
                        <MudNumericField T="decimal" HideSpinButtons="true" Min="0" @onkeydown="@keydown" tabindex=7 Immediate=true Disabled="selectedPurchaseItem.Item==null" @bind-Value="selectedPurchaseItem.Price" Label="السعر" />
                    </MudItem>
                    <MudItem md="2" Class="d-flex align-end">
                        <MudFab Style="height:36px;width:36px;align-self:end" tabindex=8 StartIcon="@Icons.Material.Filled.Add" @onclick="AddItemPurchase" Color="Color.Success" Class="ml-auto" />
                    </MudItem>

                </MudGrid>
            </MudPaper>

            <MudPaper Height="40%" Elevation="0" Style="overflow:hidden;overflow-y:scroll">
                <MudTable Height="100%" Items="@purchase.PurchaseItemDTOs" Dense="true" Hover="true" Bordered="true" FixedHeader=true Striped="true" T="PurchaseItemDTO">


                    <HeaderContent>
                        <MudTh Class="rounded-0">ت</MudTh>
                        <MudTh><MudTableSortLabel SortBy="new Func<PurchaseItemDTO, object>(x=>x.Item.Name)">اسم الصنف</MudTableSortLabel></MudTh>
                        <MudTh><MudTableSortLabel SortBy="new Func<PurchaseItemDTO, object>(x=>x.ItemUnit.Unit.Name)">الوحدة</MudTableSortLabel></MudTh>
                        <MudTh><MudTableSortLabel SortBy="new Func<PurchaseItemDTO, object>(x=>x.Quantity)">الكمية</MudTableSortLabel></MudTh>
                        <MudTh><MudTableSortLabel SortBy="new Func<PurchaseItemDTO, object>(x=>x.Exp)">الصلاحية</MudTableSortLabel></MudTh>
                        <MudTh><MudTableSortLabel SortBy="new Func<PurchaseItemDTO, object>(x=>x.Price)">السعر</MudTableSortLabel></MudTh>
                        <MudTh>الإجمالي</MudTh>
                        <MudTh Class="rounded-0">حذف</MudTh>
                    </HeaderContent>
                    <RowTemplate Context="tab">
                        <MudTd DataLabel="ت">@(purchase.PurchaseItemDTOs.ToList().IndexOf(tab) + 1)</MudTd>
                        <MudTd DataLabel="اسم الصنف">@tab.Item.Name</MudTd>
                        <MudTd DataLabel="الوحدة">@tab.ItemUnit.Unit.Name</MudTd>
                        <MudTd DataLabel="الكمية">@tab.Quantity</MudTd>
                        <MudTd DataLabel="الصلاحية">@(tab.Exp is null ? "لا يوجد" : tab.Exp.Value.ToShortDateString())</MudTd>
                        <MudTd DataLabel="السعر">@tab.Price</MudTd>
                        <MudTd DataLabel="الإجمالي">@(tab.Price * tab.Quantity)</MudTd>
                        <MudTd DataLabel="حذف">
                            <MudIconButton Style="margin-right:-16px;" Icon="@Icons.Material.Filled.Delete" Color="Color.Error" OnClick="()=>DeleteItem(tab)" />
                        </MudTd>
                    </RowTemplate>

                </MudTable>
            </MudPaper>

            <MudPaper Height="5%" Elevation="0" Class="d-flex align-content-end justify-end">
                @if (id == Guid.Empty)
                {
                    <MudItem xs="3">
                        <MudSelect T="Guid?" @bind-Value="purchase.TreasuryId" Label="الخزينة" AdornmentIcon="@Icons.Material.Filled.OpenWith" For="@(() =>purchase.TreasuryId)">
                            <MudSelectItem T="Guid?" Value="null">اختيار </MudSelectItem>
                            @foreach (var userTreasury in userTreasuries)
                            {

                                <MudSelectItem T="Guid?" Value="userTreasury.Id"> @userTreasury.Treasury.Name </MudSelectItem>

                            }
                        </MudSelect>
                    </MudItem>
                }
                <MudItem xs="2">
                    <MudTextField T="decimal" Disabled=true Value="purchase.FinalTotal" Label="إجمالي الفاتورة" For="@(() => purchase.FinalTotal)" />
                </MudItem>

                <MudItem xs="2">
                    <MudNumericField T="decimal" HideSpinButtons="true" @onkeydown="@keydown" tabindex=2 Min="0" Max="purchase.FinalTotal" Immediate=true Disabled="@(id!=Guid.Empty)" @bind-Value="purchase.Paid" Label="المدفوع" />
                </MudItem>

                <MudItem xs="2">
                    <MudTextField T="decimal" Disabled=true Value="@(purchase.FinalTotal-purchase.Paid)" Label="المتبقي" />
                </MudItem>

                @if (id != Guid.Empty)
                {
                    <MudItem xs="3" Class="d-flex">
                        <MudButton Style="align-self: end;" Variant="Variant.Filled" Color="Color.Primary" OnClick="@(()=>AddReceipt(new Guid("058813dc-c668-4483-811f-8a228941b662")))" EndIcon="@Icons.Material.Filled.Save" Class="px-6 ma-2">
                            <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">اضافة ايصال</MudText>
                        </MudButton>
                    </MudItem>
                }
                <MudButton Style="align-self: end;" Variant="Variant.Filled" Color="Color.Primary" OnClick="@Upsert" EndIcon="@Icons.Material.Filled.Save" Class="px-6">
                    <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6"> حفظ F2</MudText>
                </MudButton>
            </MudPaper>

        </MudPaper>
    </EditForm>
</MudPaper>
<MudMessageBox @ref="mbox" Title="تنبيه" CancelText="إلغاء">
    <MessageContent>
       @_message
    </MessageContent>
    <YesButton>
        <MudButton Variant="Variant.Filled" Color="Color.Success" StartIcon="@Icons.Material.Filled.Add">اضافة</MudButton>
    </YesButton>
</MudMessageBox>

<style>

    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }

    /* تحسين حقل رقم الفاتورة لعرض 10 أرقام بوضوح */
    .invoice-number-field {
        direction: ltr;
        text-align: center;
    }

    .invoice-number-field .mud-input-root {
        min-width: 180px;
        font-size: 1.1rem;
        font-weight: 500;
        letter-spacing: 1px;
    }

    .invoice-number-field .mud-input {
        text-align: center;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        padding: 12px 16px;
    }

    .invoice-number-field .mud-input-label {
        text-align: right;
        direction: rtl;
    }

    /* تحسين التجاوب للشاشات المختلفة */
    @media (max-width: 1280px) {
        .invoice-number-field .mud-input-root {
            min-width: 160px;
            font-size: 1rem;
        }
    }

    @media (min-width: 1920px) {
        .invoice-number-field .mud-input-root {
            min-width: 200px;
            font-size: 1.2rem;
        }
    }
</style>