﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.Clients;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Clients
{
    public partial class ListClients
    {
        [Inject]
        IDialogService DialogService { get; set; }
        [Inject]
        IGRepository<ClientDTO> _clients { get; set; }
        IEnumerable<ClientDTO> clients = new List<ClientDTO>();
        ClientDTO selectedItem = null;
        bool loading = true;
        string Search = "";
        string NameClientForDelete;
        MudMessageBox mbox { get; set; }

        protected override async Task OnInitializedAsync()
        {
            await LoadingData();
        }
        private bool FilterFunc1(ClientDTO element) => FilterFunc(element, Search);

        private bool FilterFunc(ClientDTO element, string searchString)
        {
            if (string.IsNullOrWhiteSpace(searchString))
                return true;
            if (element.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            return false;
        }
        async void Upsert(Guid id)
        {
            var parameters = new DialogParameters<UpsertClient>();
            parameters.Add(x => x.id, id);
            var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
            var result = await DialogService.Show<UpsertClient>(id != Guid.Empty ? "تعديل العميل" : "إضافة العميل", parameters, options).Result;
            if ((string?)result.Data != null) await LoadingData();
        }
        async void Delete(ClientDTO obj)
        {
            NameClientForDelete = obj.Name;
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                var response = await _clients.Delete("Clients/deleteClient", obj.Id);
                if (response.State)
                {
                    _snackbar.Add(response.Message, Severity.Success);
                    await LoadingData();
                }
                else _snackbar.Add(response.Message, Severity.Error);
            }
        }
        async Task LoadingData()
        {
            loading = true;
            StateHasChanged();
            var res = await _clients.GetAll("Clients/getAllClients");
            if (res.response == null) clients = res.list;
            else _snackbar.Add(res.response.Message, Severity.Error);
            loading = false;
            StateHasChanged();
        }
    }
}