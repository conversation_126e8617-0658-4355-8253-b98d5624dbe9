﻿@page "/upsertReceipt/{id:guid}"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]
@inject NavigationManager Navigation

<MudDialog>
    <DialogContent>
        <EditForm id="_formReceipt" Model="@receipt" OnValidSubmit="@Upsert">
            <DataAnnotationsValidator />
            <MudPaper Elevation="0" Class="d-flex flex-column pa-4">
                <MudGrid Spacing="2">
                    <MudItem xs="12" sm="4" md="3">
                        <MudTextField T="int"
                                      Value="receipt.ReceiptNo"
                                      Disabled="true"
                                      Label="رقم الإيصال"
                                      For="@(() => receipt.ReceiptNo)"
                                      Variant="Variant.Outlined" />
                    </MudItem>

                    <MudItem xs="12" sm="4" md="3">
                        <MudDatePicker PickerVariant="PickerVariant.Dialog"
                                       Label="التاريخ"
                                       Editable="true"
                                       @bind-Date="receipt.Date"
                                       Mask="@(new DateMask("0000-00-00"))"
                                       DateFormat="yyyy-MM-dd"
                                       For="@(()=> receipt.Date)"
                                       Variant="Variant.Outlined" />
                    </MudItem>

                    <MudItem xs="12" sm="4" md="3">
                        <MudAutocomplete T="FinancialDTO"
                                         Label="نوع العملية"
                                         Disabled="financialId != null || id != Guid.Empty"
                                         Value="receipt.Financial"
                                         ValueChanged="OnFinChanged"
                                         ResetValueOnEmptyText="true"
                                         CoerceText="true"
                                         CoerceValue="true"
                                         SearchFunc="@SearchFinancial"
                                         ToStringFunc="@(e=> e==null?null : $"{e.Name}")"
                                         For="@(()=>receipt.Financial)"
                                         Variant="Variant.Outlined" />
                    </MudItem>

                    @if (Financials != null)
                    {
                        @switch (receipt.Financial?.Name)
                        {
                            case "مصروفات":
                                <UpsertReceiptExpenses Receipt="receipt" UserTreasury="UserTreasury" Users="Users" />
                                break;
                            case "موظف":
                                <UpsertReceiptEmployees Receipt="receipt" UserTreasury="UserTreasury" Users="Users" />
                                break;
                            case "صرف مرتب":
                                <UpsertReceiptSalaryEmployees Receipt="receipt" UserTreasury="UserTreasury" Users="Users" />
                                break;
                            case "عميل":
                                <UpsertReceiptClients Receipt="receipt" UserTreasury="UserTreasury" Users="Users" />
                                break;
                            case "فاتورة مشتريات":
                                <UpsertReceiptPurchases Receipt="receipt" UserTreasury="UserTreasury" Users="Users" />
                                break;
                            case "فاتورة مبيعات":
                                <UpsertReceiptSells Receipt="receipt" UserTreasury="UserTreasury" Users="Users" />
                                break;
                            case "تحويل":
                                <UpsertReceiptTreasury Receipt="receipt" UserTreasury="UserTreasury" Users="Users" FromUsers="Users" />
                                break;
                            case "إيداع":
                                <UpsertWithdrawDepositReceiptTreasury Receipt="receipt" UserTreasury="UserTreasury" Users="Users" IsDeposit="true" />
                                break;
                            case "سحب":
                                <UpsertWithdrawDepositReceiptTreasury Receipt="receipt" UserTreasury="UserTreasury" Users="Users" IsDeposit="false" />
                                break;
                            case "رصيد افتتاحي للعميل":
                                <UpsertFirstReceiptClients Receipt="receipt" />
                                break;
                            case "رصيد افتتاحي للموظف":
                                <UpsertFirstReceiptEmployees Receipt="receipt" />
                                break;
                        }
                    }

                    <MudItem xs="12">
                        <MudTextField T="string"
                                      @bind-Value="receipt.Statement"
                                      Label="بيان الإيصال"
                                      For="@(() => receipt.Statement)"
                                      Variant="Variant.Outlined"
                                      Lines="2" />
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </EditForm>
    </DialogContent>

    <DialogActions>
        <MudButton Variant="Variant.Outlined"
                   Color="Color.Secondary"
                   OnClick="@Cancel"
                   StartIcon="@Icons.Material.Filled.Cancel"
                   Class="mx-2">
            إلغاء
        </MudButton>

        @if (id != Guid.Empty)
        {
            <!-- زر معاينة الإيصال -->
            <MudButton Variant="Variant.Text"
                       Color="Color.Info"
                       OnClick="@PreviewReceipt"
                       Disabled="@_isPrinting"
                       StartIcon="@Icons.Material.Filled.Preview"
                       Class="mx-1">
                طباعة 
            </MudButton>

        }

        <MudButton Variant="Variant.Filled"
                   Color="Color.Primary"
                   form="_formReceipt"
                   ButtonType="ButtonType.Submit"
                   StartIcon="@Icons.Material.Filled.Save"
                   Class="mx-2">
            حفظ F2
        </MudButton>
    </DialogActions>
</MudDialog>

<style>
    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }

    /* تنسيق الدايلوج ليبدو مثل نظام ويندوز */
    .mud-dialog {
        background-color: #ffffff !important;
        border: 1px solid #d0d7de !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
    }

    .mud-dialog-title {
        background-color: #f6f8fa !important;
        color: #24292f !important;
        border-bottom: 1px solid #d0d7de !important;
        padding: 16px 24px !important;
        margin: 0 !important;
        font-weight: 600 !important;
    }

    .mud-dialog-content {
        background-color: #ffffff !important;
        color: #24292f !important;
        padding: 24px !important;
    }

    .mud-dialog-actions {
        background-color: #f6f8fa !important;
        border-top: 1px solid #d0d7de !important;
        padding: 16px 24px !important;
    }

    /* تنسيق الوضع الداكن */
    @@media (prefers-color-scheme: dark) {
        .mud-dialog {
            background-color: #0d1117 !important;
            border: 1px solid #30363d !important;
        }

        .mud-dialog-title {
            background-color: #161b22 !important;
            color: #f0f6fc !important;
            border-bottom: 1px solid #30363d !important;
        }

        .mud-dialog-content {
            background-color: #0d1117 !important;
            color: #f0f6fc !important;
        }

        .mud-dialog-actions {
            background-color: #161b22 !important;
            border-top: 1px solid #30363d !important;
        }
    }
</style>


