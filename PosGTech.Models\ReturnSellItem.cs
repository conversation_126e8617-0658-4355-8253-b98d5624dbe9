﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول المواد المستهكلة لكل فاتورة
    /// </summary>
    public class ReturnSellItem : BaseEntity
    {
        public Guid? ReturnSellId { get; set; }
        public ReturnSell? ReturnSell { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal Quantity { get; set; } = 1;
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal CostPrice { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal ReturnPrice { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid SellItemId { get; set; }
        public SellItem? SellItem { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid ItemUnitId { get; set; }
        public ItemUnit? ItemUnit { get; set; }
    }
}
