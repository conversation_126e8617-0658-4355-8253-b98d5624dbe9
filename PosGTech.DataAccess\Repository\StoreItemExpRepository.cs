﻿using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Items;

namespace PosGTech.DataAccess.Repository
{
    public class StoreItemExpRepository(ApplicationDbContext db, IItemUnitRepository _itemUnitRepository) : Repository<StoreItemExp>(db), IStoreItemExpRepository
    {
        public bool CheckQuantityItemsBelowZero() => GetAll().Result.Any(x => x.Quantity < 0);
        public async Task UpdateReduceQuantity(IEnumerable<ItemForChangeQte> itemForChangeQte)
        {
            itemForChangeQte = await _itemUnitRepository.GetQteForBasicUnit(itemForChangeQte);
            foreach (var item in itemForChangeQte)
            {
                var oldItem = await GetByIdAsync(item.ItemId);
                oldItem.Quantity -= item.Quantity;
                oldItem.UpdatedAt = DateTime.Now;
                Update(oldItem);
            }


        }
        public async Task AddQuantity(IEnumerable<ItemForChangeQte> itemForChangeQte)
        {
            itemForChangeQte = await _itemUnitRepository.GetQteForBasicUnit(itemForChangeQte);
            foreach (var item in itemForChangeQte)
            {
                var oldItem = await GetByIdAsync(item.ItemId);
                oldItem.Quantity += item.Quantity;
                oldItem.UpdatedAt = DateTime.Now;
                Update(oldItem);
            }


        }

        /// <summary>
        /// Reduce quantity directly without unit conversion (quantities are already in basic unit)
        /// </summary>
        /// <param name="itemForChangeQte">Items with quantities already converted to basic unit</param>
        public async Task UpdateReduceQuantityDirectly(IEnumerable<ItemForChangeQte> itemForChangeQte)
        {
            // Skip unit conversion as quantities are already in basic unit
            foreach (var item in itemForChangeQte)
            {
                var oldItem = await GetByIdAsync(item.ItemId);
                oldItem.Quantity -= item.Quantity;
                oldItem.UpdatedAt = DateTime.Now;
                Update(oldItem);
            }
        }

        /// <summary>
        /// Add quantity directly without unit conversion (quantities are already in basic unit)
        /// </summary>
        /// <param name="itemForChangeQte">Items with quantities already converted to basic unit</param>
        public async Task AddQuantityDirectly(IEnumerable<ItemForChangeQte> itemForChangeQte)
        {
            // Skip unit conversion as quantities are already in basic unit
            foreach (var item in itemForChangeQte)
            {
                var oldItem = await GetByIdAsync(item.ItemId);
                oldItem.Quantity += item.Quantity;
                oldItem.UpdatedAt = DateTime.Now;
                Update(oldItem);
            }
        }
    }
}
