﻿@page "/upsertInventory/{id:guid}"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<MudPaper Class="small-TextField" Elevation="0">
    <MudGrid Spacing="0">
        <!-- رأس الصفحة -->
        <MudItem xs="12">
            <MudPaper Elevation="1" Class="px-4 py-2 d-flex align-center">
                <MudText Typo="Typo.h5" Class="primary-text font-weight-bold">إدارة المخزون</MudText>
                <MudSpacer />
                <MudButton OnClick="Back" Color="Color.Default" Variant="Variant.Text"
                           EndIcon="@Icons.Material.Filled.ArrowBack">
                    رجوع
                </MudButton>
            </MudPaper>
        </MudItem>
 
        <!-- محتوى الصفحة الرئيسي -->
        <MudItem xs="12">
            <EditForm Model="@inventory" id="my-form" @onkeydown="@keydownForm" @ref=Form>
                <DataAnnotationsValidator />

                <MudGrid Spacing="2">
                    <!-- قسم معلومات الفاتورة -->
                    <MudItem xs="12" md="7" lg="8">
                        <MudPaper Elevation="0" Class="pa-3 rounded-lg mb-3 form-section1 ">
                            <MudGrid>
                                <MudItem xs="12" sm="6" md="4">
                                    <MudSelect T="Guid?" @bind-Value="inventory.StoreId"
                                               Label="المخازن"
                                               Disabled="inventory.Id!=Guid.Empty"
                                               AdornmentIcon="@Icons.Material.Filled.Warehouse"
                                               Adornment="Adornment.End"
                                               Variant="Variant.Outlined"
                                               For="@(() =>inventory.StoreId)">
                                        <MudSelectItem T="Guid?" Value="null">اختيار </MudSelectItem>
                                        @foreach (var store in stores)
                                        {
                                            <MudSelectItem T="Guid?" Value="store.Id"> @store.Name </MudSelectItem>
                                        }
                                    </MudSelect>
                                </MudItem>

                                <MudItem xs="12" sm="6" md="4">
                                    <MudTextField T="int" Value="inventory.InvoiceNo"
                                                  @onkeydown="@keydown"
                                                  Disabled=true
                                                  Label="رقم الفاتورة"
                                                  Variant="Variant.Outlined"
                                                  Adornment="Adornment.End"
                                                  AdornmentIcon="@Icons.Material.Filled.ReceiptLong"
                                                  For="@(() => inventory.InvoiceNo)" />
                                </MudItem>

                                <MudItem xs="12" sm="6" md="4">
                                    <MudDatePicker PickerVariant="PickerVariant.Dialog"
                                                   @onkeydown="@keydown"
                                                   tabindex=0
                                                   Label="التاريخ"
                                                   Editable="true"
                                                   @bind-Date="_dateInventory"
                                                   Mask="@(new DateMask("0000-00-00"))"
                                                   DateFormat="yyyy-MM-dd"
                                                   Variant="Variant.Outlined"
                                                   Adornment="Adornment.End"
                                                   AdornmentIcon="@Icons.Material.Filled.CalendarMonth" />
                                </MudItem>
                            </MudGrid>
                        </MudPaper>

                        <!-- جدول الأصناف -->
                        <MudPaper  Elevation="0">
                            <MudText Typo="Typo.subtitle1" Class="px-3 pt-3 font-weight-bold">الأصناف المضافة</MudText>
                            <MudDivider Class="my-2" />

                            <div class="table-wrapper ">
                                <MudTable Items="@inventory.InventoryItemDTOs"
                                          Dense="true"
                                          Hover="true"
                                          Bordered="false"
                                          FixedHeader=true
                                          Striped="true"
                                          Elevation="0"
                                        
                                          T="InventoryItemDTO">
                                    <HeaderContent>
                                        <MudTh Style="width: 40px">ت</MudTh>
                                        <MudTh><MudTableSortLabel SortBy="new Func<InventoryItemDTO, object>(x=>x.Item.Name)">اسم الصنف</MudTableSortLabel></MudTh>
                                        <MudTh><MudTableSortLabel SortBy="new Func<InventoryItemDTO, object>(x=>x.ItemUnit.Unit.Name)">الوحدة</MudTableSortLabel></MudTh>
                                        <MudTh><MudTableSortLabel SortBy="new Func<InventoryItemDTO, object>(x=>x.Quantity)">الكمية</MudTableSortLabel></MudTh>
                                        <MudTh><MudTableSortLabel SortBy="new Func<InventoryItemDTO, object>(x=>x.Exp)">الصلاحية</MudTableSortLabel></MudTh>
                                        <MudTh><MudTableSortLabel SortBy="new Func<InventoryItemDTO, object>(x=>x.Price)">السعر</MudTableSortLabel></MudTh>
                                        <MudTh>الإجمالي</MudTh>
                                        <MudTh Style="width: 60px">حذف</MudTh>
                                    </HeaderContent>
                                    <RowTemplate Context="tab">
                                        <MudTd DataLabel="ت">@(inventory.InventoryItemDTOs.ToList().IndexOf(tab) + 1)</MudTd>
                                        <MudTd DataLabel="اسم الصنف">@tab.Item.Name</MudTd>
                                        <MudTd DataLabel="الوحدة">@tab.ItemUnit.Unit.Name</MudTd>
                                        <MudTd DataLabel="الكمية">@tab.Quantity</MudTd>
                                        <MudTd DataLabel="الصلاحية">@(tab.Exp is null ? "لا يوجد" : tab.Exp.Value)</MudTd>
                                        <MudTd DataLabel="السعر">@tab.Price</MudTd>
                                        <MudTd DataLabel="الإجمالي">@(tab.Price * tab.Quantity)</MudTd>
                                        <MudTd DataLabel="حذف">
                                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                           Size="Size.Small"
                                                           Color="Color.Error"
                                                           OnClick="()=>DeleteItem(tab)" />
                                        </MudTd>
                                    </RowTemplate>
                                    <PagerContent>
                                        <MudTablePager />
                                    </PagerContent>
                                </MudTable>
                            </div>

                            <MudDivider Class="my-2" />
                            <MudPaper Elevation="0" Class="d-flex justify-end pa-3">
                                <MudItem xs="12" sm="6" md="4" lg="3">
                                    <MudTextField T="decimal"
                                                  Disabled=true
                                                  Value="inventory.Total"
                                                  Label="إجمالي الفاتورة"
                                                  Variant="Variant.Outlined"
                                                  Adornment="Adornment.End"
                                                  AdornmentIcon="@Icons.Material.Filled.Payments"
                                                  For="@(() => inventory.Total)" />
                                </MudItem>
                            </MudPaper>
                        </MudPaper>
                    </MudItem>

                    <!-- قسم إضافة الصنف -->
                    <MudItem xs="12" md="5" lg="4">
                        <MudPaper Class="form-sectionItim" Elevation="0">
                            <MudText Typo="Typo.subtitle1" Class="mb-3 font-weight-bold">إضافة صنف جديد</MudText>
                            <MudDivider Class="mb-3" />

                            <MudAutocomplete T="ItemCMDTO"
                                             @ref="ItemForAdd"
                                             Label="اختر الصنف"
                                             @onkeydown="@SelectItem"
                                             tabindex=3
                                             Value="selectedItemInventory.Item"
                                             ValueChanged="ChangeItem"
                                             ResetValueOnEmptyText="true"
                                             CoerceText="true"
                                             CoerceValue="true"
                                             Variant="Variant.Outlined"
                                             Adornment="Adornment.End"
                                             AdornmentIcon="@Icons.Material.Filled.Inventory2"
                                             Class="mb-3"
                                             SearchFunc="@SearchItem"
                                             ToStringFunc="@(e=> e==null?null : $"{e.Name}")" />

                            @if (selectedItemInventory.Item is not null)
                            {
                                <MudSelect T="ItemUnitDTO"
                                           Value="selectedItemInventory.ItemUnit"
                                           @onkeydown="@keydown"
                                           ValueChanged="ChangeItemUnit"
                                           tabindex=4
                                           Label="الوحدة"
                                           Variant="Variant.Outlined"
                                           Adornment="Adornment.End"
                                           AdornmentIcon="@Icons.Material.Filled.SquareFoot"
                                           Class="mb-3"
                                           For="@(() =>selectedItemInventory.ItemUnit)">
                                    @foreach (var unit in items.First(x => x.Id == selectedItemInventory.Item.Id).ItemUnits)
                                    {
                                        <MudSelectItem T="ItemUnitDTO" Value="unit"> @unit.Unit.Name </MudSelectItem>
                                    }
                                </MudSelect>

                                <MudDatePicker PickerVariant="PickerVariant.Dialog"
                                               @onkeydown="@keydown"
                                               tabindex=5
                                               Label="تاريخ الصلاحية"
                                               Disabled="@(!items.First(x => x.Id == selectedItemInventory.Item.Id).IsHaveExp)"
                                               @bind-Date=_dateExp
                                               Mask="@(new DateMask("0000-00-00"))"
                                               DateFormat="yyyy-MM-dd"
                                               Variant="Variant.Outlined"
                                               Adornment="Adornment.End"
                                               AdornmentIcon="@Icons.Material.Filled.Event"
                                               Class="mb-3"
                                               For="@(() =>_dateExp)" />
                            }

                            <div class="d-flex">
                                <MudNumericField T="decimal"
                                                 HideSpinButtons="true"
                                                 Min="0"
                                                 @onkeydown="@keydown"
                                                 tabindex=6
                                                 Disabled="selectedItemInventory.Item==null"
                                                 Value="selectedItemInventory.Quantity"
                                                 ValueChanged="ChangeQte"
                                                 Label="الكمية"
                                                 Class="mr-2 flex-grow-1"
                                                 Variant="Variant.Outlined"
                                                 Adornment="Adornment.End"
                                                 AdornmentIcon="@Icons.Material.Filled.Numbers" />

                                <MudNumericField T="decimal"
                                                 HideSpinButtons="true"
                                                 Min="0"
                                                 @onkeydown="@keydown"
                                                 tabindex=7
                                                 Immediate=true
                                                 Disabled="selectedItemInventory.Item==null"
                                                 @bind-Value="selectedItemInventory.Price"
                                                 Label="السعر"
                                                 Class="flex-grow-1"
                                                 Variant="Variant.Outlined"
                                                 Adornment="Adornment.End"
                                                 AdornmentIcon="@Icons.Material.Filled.PriceChange" />
                            </div>

                            <MudPaper Elevation="0" Class="d-flex flex-column justify-center mt-6 ">
                                <MudButton OnClick="AddNewItem"
                                           Variant="Variant.Outlined"
                                           Color="Color.Primary"
                                           StartIcon="@Icons.Material.Filled.Add"
                                           Class="mb-2 font-weight-bold">
                                    إضافة صنف جديد للنظام
                                </MudButton>

                                <MudButton OnClick="AddItemInventory"
                                           Variant="Variant.Filled"
                                           Color="Color.Success"
                                           Size="Size.Large"
                                           StartIcon="@Icons.Material.Filled.AddShoppingCart"
                                           Class="font-weight-bold mb-4"
                                           tabindex=8>
                                    إضافة للفاتورة
                                </MudButton>
                            </MudPaper>
                        </MudPaper>
                    </MudItem>
                </MudGrid>

                <!-- أزرار الحفظ أسفل الصفحة -->
                <MudPaper Class="form-sectionButton" Elevation="1">
                    <div class="d-flex justify-end pa-3">
                        <MudButton Variant="Variant.Text"
                                   Color="Color.Default"
                                   Class="mx-2">
                            إلغاء
                        </MudButton>
                        <MudButton Variant="Variant.Filled"
                                   OnClick="@Upsert"
                                   Color="Color.Primary"
                                   StartIcon="@Icons.Material.Filled.Save"
                                   Size="Size.Large"
                                   Class="px-6 font-weight-bold">
                            حفظ البيانات (F2)
                        </MudButton>
                    </div>
                </MudPaper>
            </EditForm>
        </MudItem>
    </MudGrid>
</MudPaper>

<MudMessageBox @ref="mbox" Title="تنبيه" CancelText="إلغاء">
    <MessageContent>
        @_message
    </MessageContent>
    <YesButton>
        <MudButton Variant="Variant.Filled" Color="Color.Success" StartIcon="@Icons.Material.Filled.Add">اضافة</MudButton>
    </YesButton>
</MudMessageBox>


<UpsertInventoryCSS />

@*
<MudPaper Class="d-flex flex-column  desktop rounded-0" Elevation="0">


    <EditForm Model="@inventory" id="my-form" @onkeydown="@keydownForm" @ref=Form>
        <DataAnnotationsValidator />
        <MudPaper Class="d-flex flex-column desktop px-2">

            <MudPaper Height="5%" Elevation="0" Class="d-flex align-content-end justify-end">
                <MudButton Color="@Color.Secondary" Variant="@Variant.Outlined" OnClick="Back" EndIcon="@Icons.Material.Filled.ArrowBack" Class="px-6">
                    <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">رجوع</MudText>
                </MudButton>
            </MudPaper>

            <MudPaper Height="45%" Elevation="0">

                <MudGrid>
                    <MudItem xs="3">
                        <MudSelect T="Guid?" @bind-Value="inventory.StoreId" Label="المخازن" Disabled="inventory.Id!=Guid.Empty" AdornmentIcon="@Icons.Material.Filled.OpenWith" For="@(() =>inventory.StoreId)">
                            <MudSelectItem T="Guid?" Value="null">اختيار </MudSelectItem>
                            @foreach (var store in stores)
                            {

                                <MudSelectItem T="Guid?" Value="store.Id"> @store.Name </MudSelectItem>

                            }
                        </MudSelect>
                    </MudItem>
                    <MudItem xs="2">
                        <MudTextField T="int" Value="inventory.InvoiceNo" @onkeydown="@keydown" Disabled=true Label="رقم الفاتورة" For="@(() => inventory.InvoiceNo)" />
                    </MudItem>
                    <MudItem xs="3">
                        <MudDatePicker PickerVariant="PickerVariant.Dialog" @onkeydown="@keydown" tabindex=0 Label="التاريخ" Editable="true"
                                       @bind-Date="_dateInventory" Mask="@(new DateMask("0000-00-00"))"
                                       DateFormat="yyyy-MM-dd" />
                    </MudItem>
                    <MudItem xs="3">
                        <MudAutocomplete T="ItemCMDTO" @ref="ItemForAdd" Label="الصنف" @onkeydown="@SelectItem" tabindex=3 Value="selectedItemInventory.Item" ValueChanged="ChangeItem"
                                         ResetValueOnEmptyText="true"
                                         CoerceText="true" CoerceValue="true"
                                         SearchFunc="@SearchItem" ToStringFunc="@(e=> e==null?null : $"{e.Name}")" />
                    </MudItem>
                    <MudItem md="1" Class="d-flex align-end">
                        <MudFab Style="height:36px;width:36px;align-self:end" StartIcon="@Icons.Material.Filled.Add" @onclick="AddNewItem" Color="Color.Success" Class="ml-auto" />
                    </MudItem>
                    @if (selectedItemInventory.Item is not null)
                    {
                        <MudItem xs="3">
                            <MudSelect T="ItemUnitDTO" Value="selectedItemInventory.ItemUnit" @onkeydown="@keydown" ValueChanged="ChangeItemUnit" tabindex=4 Label="الوحدة" AdornmentIcon="@Icons.Material.Filled.OpenWith" For="@(() =>selectedItemInventory.ItemUnit)">
                                @foreach (var unit in items.First(x => x.Id == selectedItemInventory.Item.Id).ItemUnits)
                                {
                                    <MudSelectItem T="ItemUnitDTO" Value="unit"> @unit.Unit.Name </MudSelectItem>
                                }
                            </MudSelect>
                        </MudItem>
                   
                            <MudItem xs="3">
                            <MudDatePicker PickerVariant="PickerVariant.Dialog" @onkeydown="@keydown" tabindex=5 Label="تاريخ الصلاحية" Disabled="@(!items.First(x => x.Id == selectedItemInventory.Item.Id).IsHaveExp)"
                                           @bind-Date=_dateExp Mask="@(new DateMask("0000-00-00"))"
                                           DateFormat="yyyy-MM-dd" For="@(() =>_dateExp)" />
                            </MudItem>
                    }

                    <MudItem xs="3">
                        <MudNumericField T="decimal" HideSpinButtons="true" Min="0" @onkeydown="@keydown" tabindex=6 Disabled="selectedItemInventory.Item==null" Value="selectedItemInventory.Quantity" ValueChanged="ChangeQte" Label="الكمية" />
                    </MudItem>
                    <MudItem xs="3">
                        <MudNumericField T="decimal" HideSpinButtons="true" Min="0" @onkeydown="@keydown" tabindex=7 Immediate=true Disabled="selectedItemInventory.Item==null" @bind-Value="selectedItemInventory.Price" Label="السعر" />
                    </MudItem>
                    <MudItem md="2" Class="d-flex align-end">
                        <MudFab Style="height:36px;width:36px;align-self:end" tabindex=8 StartIcon="@Icons.Material.Filled.Add" @onclick="AddItemInventory" Color="Color.Success" Class="ml-auto" />
                    </MudItem>

                </MudGrid>
            </MudPaper>

            <MudPaper Height="40%" Elevation="0" Style="overflow:hidden;overflow-y:scroll">
                <MudTable Height="100%" Items="@inventory.InventoryItemDTOs" Dense="true" Hover="true" Bordered="true" FixedHeader=true Striped="true" T="InventoryItemDTO">


                    <HeaderContent>
                        <MudTh Class="rounded-0">ت</MudTh>
                        <MudTh><MudTableSortLabel SortBy="new Func<InventoryItemDTO, object>(x=>x.Item.Name)">اسم الصنف</MudTableSortLabel></MudTh>
                        <MudTh><MudTableSortLabel SortBy="new Func<InventoryItemDTO, object>(x=>x.ItemUnit.Unit.Name)">الوحدة</MudTableSortLabel></MudTh>
                        <MudTh><MudTableSortLabel SortBy="new Func<InventoryItemDTO, object>(x=>x.Quantity)">الكمية</MudTableSortLabel></MudTh>
                        <MudTh><MudTableSortLabel SortBy="new Func<InventoryItemDTO, object>(x=>x.Exp)">الصلاحية</MudTableSortLabel></MudTh>
                        <MudTh><MudTableSortLabel SortBy="new Func<InventoryItemDTO, object>(x=>x.Price)">السعر</MudTableSortLabel></MudTh>
                        <MudTh>الإجمالي</MudTh>
                        <MudTh Class="rounded-0">حذف</MudTh>
                    </HeaderContent>
                    <RowTemplate Context="tab">
                        <MudTd DataLabel="ت">@(inventory.InventoryItemDTOs.ToList().IndexOf(tab) + 1)</MudTd>
                        <MudTd DataLabel="اسم الصنف">@tab.Item.Name</MudTd>
                        <MudTd DataLabel="الوحدة">@tab.ItemUnit.Unit.Name</MudTd>
                        <MudTd DataLabel="الكمية">@tab.Quantity</MudTd>
                        <MudTd DataLabel="الصلاحية">@(tab.Exp is null ? "لا يوجد" : tab.Exp.Value)</MudTd>
                        <MudTd DataLabel="السعر">@tab.Price</MudTd>
                        <MudTd DataLabel="الإجمالي">@(tab.Price * tab.Quantity)</MudTd>
                        <MudTd DataLabel="حذف">
                            <MudIconButton Style="margin-right:-16px;" Icon="@Icons.Material.Filled.Delete" Color="Color.Error" OnClick="()=>DeleteItem(tab)" />
                        </MudTd>
                    </RowTemplate>

                </MudTable>
            </MudPaper>

            <MudPaper Height="5%" Elevation="0" Class="d-flex align-content-end justify-end">
                
                <MudItem xs="2">
                    <MudTextField T="decimal" Disabled=true Value="inventory.Total" Label="إجمالي الفاتورة" For="@(() => inventory.Total)" />
                </MudItem>
                <MudButton Style="align-self: end;" Variant="Variant.Filled" Color="Color.Primary" OnClick="@Upsert" EndIcon="@Icons.Material.Filled.Save" Class="px-6">
                    <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6"> حفظ F2</MudText>
                </MudButton>
            </MudPaper>

        </MudPaper>
    </EditForm>
</MudPaper>
<MudMessageBox @ref="mbox" Title="تنبيه" CancelText="إلغاء">
    <MessageContent>
       @_message
    </MessageContent>
    <YesButton>
        <MudButton Variant="Variant.Filled" Color="Color.Success" StartIcon="@Icons.Material.Filled.Add">اضافة</MudButton>
    </YesButton>
</MudMessageBox>

<style>

    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }
</style> *@