﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.Stores;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Stores
{
    public partial class ListStores
    {
        [Inject]
        IGRepository<StoreDTO> _Store { get; set; }
        IEnumerable<StoreDTO> Stores = new List<StoreDTO>();
        [Inject]
        IDialogService DialogService { get; set; }
        StoreDTO selectedItem = null;
        bool loading = true;
        string Search = "";
        string NameStoreForDelete;
        MudMessageBox mbox { get; set; }

        protected override async Task OnInitializedAsync() => await LoadingData();
        private bool FilterFunc1(StoreDTO element) => FilterFunc(element, Search);

        private bool FilterFunc(StoreDTO element, string searchString)
        {
            if (string.IsNullOrWhiteSpace(searchString))
                return true;
            if (element.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            return false;
        }
        async void Upsert(Guid id)
        {
            var parameters = new DialogParameters<UpsertStore>();
            parameters.Add(x => x.id, id);
            var options = new DialogOptions() { CloseButton = false, MaxWidth = MaxWidth.Medium, FullWidth = true };
            var result = await DialogService.Show<UpsertStore>(id != Guid.Empty ? "تعديل مخزن" : "إضافة مخزن", parameters, options).Result;
            if ((bool?)result.Data == true) await LoadingData();
        }
        async void Delete(StoreDTO obj)
        {
            NameStoreForDelete = obj.Name;
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                var response = await _Store.Delete("Stores/deleteStore", obj.Id);
                if (response.State)
                {
                    _snackbar.Add(response.Message, Severity.Success);
                    await LoadingData();
                }
                else _snackbar.Add(response.Message, Severity.Error);
            }
        }
        async Task LoadingData()
        {
            loading = true;
            StateHasChanged();
            var res = await _Store.GetAll("Stores/getAllStores");
            if (res.response == null) Stores = res.list;
            else _snackbar.Add(res.response.Message, Severity.Error);
            loading = false;
            StateHasChanged();
        }
    }
}