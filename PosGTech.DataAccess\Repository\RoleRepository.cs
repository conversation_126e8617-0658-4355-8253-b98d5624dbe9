using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Roles;
using PosGTech.ModelsDTO.Authorization;
using System.Security.Claims;

namespace PosGTech.DataAccess.Repository
{
    /// <summary>
    /// Repository لإدارة الأدوار والصلاحيات
    /// </summary>
    public class RoleRepository : IRoleRepository
    {
        private readonly RoleManager<Role> _roleManager;
        private readonly UserManager<User> _userManager;
        private readonly ApplicationDbContext _context;

        public RoleRepository(RoleManager<Role> roleManager, UserManager<User> userManager, ApplicationDbContext context)
        {
            _roleManager = roleManager;
            _userManager = userManager;
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع الأدوار مع صلاحياتها
        /// </summary>
        public async Task<IEnumerable<RoleDTO>> GetAllRoles()
        {
            var roles = _roleManager.Roles.ToList();
            var roleDTOs = new List<RoleDTO>();

            foreach (var role in roles)
            {
                var claims = await _roleManager.GetClaimsAsync(role);
                var permissions = claims.Where(c => c.Type == "permission").Select(c => c.Value).ToList();
                var usersCount = await GetRoleUsersCount(role.Id);

                roleDTOs.Add(new RoleDTO
                {
                    Id = role.Id,
                    Name = role.Name,
                    Permissions = permissions,
                    UsersCount = usersCount,
                    IsSystemRole = IsSystemRole(role.Name),
                    CreatedDate = DateTime.UtcNow // يمكن تحسينه لاحقاً
                });
            }

            return roleDTOs;
        }

        /// <summary>
        /// الحصول على دور محدد بمعرفه
        /// </summary>
        public async Task<RoleDTO> GetRoleById(Guid roleId)
        {
            var role = await _roleManager.FindByIdAsync(roleId.ToString());
            if (role == null) return null;

            var claims = await _roleManager.GetClaimsAsync(role);
            var permissions = claims.Where(c => c.Type == "permission").Select(c => c.Value).ToList();
            var description = claims.FirstOrDefault(c => c.Type == "description")?.Value;
            var usersCount = await GetRoleUsersCount(role.Id);

            return new RoleDTO
            {
                Id = role.Id,
                Name = role.Name,
                Description = description,
                Permissions = permissions,
                UsersCount = usersCount,
                IsSystemRole = IsSystemRole(role.Name),
                CreatedDate = DateTime.UtcNow
            };
        }

        /// <summary>
        /// الحصول على دور محدد باسمه
        /// </summary>
        public async Task<Role> GetRoleByName(string roleName)
        {
            return await _roleManager.FindByNameAsync(roleName);
        }

        /// <summary>
        /// إنشاء دور جديد مع صلاحياته
        /// </summary>
        public async Task<ResponseVM> InsertRole(RoleDTO roleDto)
        {
            try
            {
                // التحقق من صحة البيانات المدخلة
                if (roleDto == null)
                    return new ResponseVM { State = false, Message = "بيانات الدور مطلوبة" };

                if (string.IsNullOrWhiteSpace(roleDto.Name))
                    return new ResponseVM { State = false, Message = "اسم الدور مطلوب" };

                if (roleDto.Permissions == null || roleDto.Permissions.Count == 0)
                    return new ResponseVM { State = false, Message = "يجب اختيار صلاحية واحدة على الأقل" };

                // التحقق من عدم وجود دور بنفس الاسم
                var existingRole = await _roleManager.FindByNameAsync(roleDto.Name);
                if (existingRole != null)
                    return new ResponseVM { State = false, Message = "يوجد دور بنفس الاسم مسبقاً" };

                // إنشاء الدور
                var role = new Role { Id = Guid.NewGuid(), Name = roleDto.Name };
                var result = await _roleManager.CreateAsync(role);

                if (result.Succeeded)
                {
                    // إضافة الوصف كـ Claim إذا كان موجوداً
                    if (!string.IsNullOrWhiteSpace(roleDto.Description))
                    {
                        await _roleManager.AddClaimAsync(role, new Claim("description", roleDto.Description));
                    }

                    // إضافة الصلاحيات كـ Claims
                    foreach (var permission in roleDto.Permissions)
                    {
                        if (PermissionConstants.GetAllPermissions().Contains(permission))
                        {
                            await _roleManager.AddClaimAsync(role, new Claim("permission", permission));
                        }
                    }

                    return new ResponseVM { State = true, Message = "تم إنشاء الدور بنجاح", Data = role.Id.ToString() };
                }
                else
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return new ResponseVM { State = false, Message = $"فشل في إنشاء الدور: {errors}" };
                }
            }
            catch (Exception ex)
            {
                return new ResponseVM { State = false, Message = $"خطأ في إنشاء الدور: {ex.Message}" };
            }
        }

        /// <summary>
        /// تحديث دور موجود
        /// </summary>
        public async Task<ResponseVM> UpdateRole(Guid roleId, RoleDTO roleDto)
        {
            try
            {
                // التحقق من صحة البيانات المدخلة
                if (roleId == Guid.Empty)
                    return new ResponseVM { State = false, Message = "معرف الدور مطلوب" };

                if (roleDto == null)
                    return new ResponseVM { State = false, Message = "بيانات الدور مطلوبة" };

                if (string.IsNullOrWhiteSpace(roleDto.Name))
                    return new ResponseVM { State = false, Message = "اسم الدور مطلوب" };

                var role = await _roleManager.FindByIdAsync(roleId.ToString());
                if (role == null)
                    return new ResponseVM { State = false, Message = "البيانات غير موجودة" };

                // التحقق من أن الدور ليس دور أساسي إذا كان يتم تغيير الاسم
                if (IsSystemRole(role.Name) && role.Name != roleDto.Name)
                    return new ResponseVM { State = false, Message = "لا يمكن تعديل اسم الأدوار الأساسية" };

                // تحديث اسم الدور
                if (role.Name != roleDto.Name)
                {
                    var existingRole = await _roleManager.FindByNameAsync(roleDto.Name);
                    if (existingRole != null && existingRole.Id != roleId)
                        return new ResponseVM { State = false, Message = "يوجد دور بنفس الاسم مسبقاً" };

                    role.Name = roleDto.Name;
                    await _roleManager.UpdateAsync(role);
                }

                // تحديث الوصف
                await UpdateRoleDescription(role, roleDto.Description);

                // تحديث الصلاحيات
                var permissionResult = await UpdateRolePermissions(roleId, roleDto.Permissions);
                if (!permissionResult.State)
                    return permissionResult;

                return new ResponseVM { State = true, Message = "تم الحفظ" };
            }
            catch (Exception ex)
            {
                return new ResponseVM { State = false, Message = $"خطأ في تحديث الدور: {ex.Message}" };
            }
        }

        /// <summary>
        /// حذف دور
        /// </summary>
        public async Task<ResponseVM> DeleteRole(Guid roleId)
        {
            try
            {
                var role = await _roleManager.FindByIdAsync(roleId.ToString());
                if (role == null)
                    return new ResponseVM { State = false, Message = "البيانات غير موجودة" };

                // التحقق من أن الدور ليس دور أساسي
                if (IsSystemRole(role.Name))
                    return new ResponseVM { State = false, Message = "لا يمكن حذف الأدوار الأساسية" };

                // التحقق من عدم وجود مستخدمين مخصصين لهذا الدور
                var usersInRole = await _userManager.GetUsersInRoleAsync(role.Name);
                if (usersInRole.Count > 0)
                    return new ResponseVM { State = false, Message = "لا يمكن الحذف هذه البيانات مستخدمة" };

                // حذف جميع الصلاحيات (Claims) المرتبطة بالدور أولاً
                var roleClaims = await _roleManager.GetClaimsAsync(role);
                foreach (var claim in roleClaims)
                {
                    await _roleManager.RemoveClaimAsync(role, claim);
                }

                // الآن يمكن حذف الدور بأمان
                var result = await _roleManager.DeleteAsync(role);

                if (result.Succeeded)
                    return new ResponseVM { State = true, Message = "تم حذف الدور بنجاح" };
                else
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return new ResponseVM { State = false, Message = $"فشل في حذف الدور: {errors}" };
                }
            }
            catch (Exception ex)
            {
                return new ResponseVM { State = false, Message = $"خطأ في حذف الدور: {ex.Message}" };
            }
        }

        /// <summary>
        /// الحصول على صلاحيات دور محدد
        /// </summary>
        public async Task<List<string>> GetRolePermissions(Guid roleId)
        {
            var role = await _roleManager.FindByIdAsync(roleId.ToString());
            if (role == null) return new List<string>();

            var claims = await _roleManager.GetClaimsAsync(role);
            return claims.Where(c => c.Type == "permission").Select(c => c.Value).ToList();
        }

        /// <summary>
        /// تحديث وصف الدور
        /// </summary>
        private async Task UpdateRoleDescription(Role role, string description)
        {
            // إزالة الوصف الحالي
            var currentClaims = await _roleManager.GetClaimsAsync(role);
            var descriptionClaim = currentClaims.FirstOrDefault(c => c.Type == "description");

            if (descriptionClaim != null)
            {
                await _roleManager.RemoveClaimAsync(role, descriptionClaim);
            }

            // إضافة الوصف الجديد إذا كان موجوداً
            if (!string.IsNullOrWhiteSpace(description))
            {
                await _roleManager.AddClaimAsync(role, new Claim("description", description));
            }
        }

        /// <summary>
        /// تحديث صلاحيات دور محدد
        /// </summary>
        public async Task<ResponseVM> UpdateRolePermissions(Guid roleId, List<string> permissions)
        {
            try
            {
                var role = await _roleManager.FindByIdAsync(roleId.ToString());
                if (role == null)
                    return new ResponseVM { State = false, Message = "الدور غير موجود" };

                // إزالة جميع الصلاحيات الحالية
                var currentClaims = await _roleManager.GetClaimsAsync(role);
                var permissionClaims = currentClaims.Where(c => c.Type == "permission").ToList();

                foreach (var claim in permissionClaims)
                {
                    await _roleManager.RemoveClaimAsync(role, claim);
                }

                // إضافة الصلاحيات الجديدة
                foreach (var permission in permissions)
                {
                    if (PermissionConstants.GetAllPermissions().Contains(permission))
                    {
                        await _roleManager.AddClaimAsync(role, new Claim("permission", permission));
                    }
                }

                return new ResponseVM { State = true, Message = "تم الحفظ" };
            }
            catch (Exception ex)
            {
                return new ResponseVM { State = false, Message = $"خطأ في تحديث صلاحيات الدور: {ex.Message}" };
            }
        }

        /// <summary>
        /// الحصول على عدد المستخدمين في الدور
        /// </summary>
        public async Task<int> GetRoleUsersCount(Guid roleId)
        {
            var role = await _roleManager.FindByIdAsync(roleId.ToString());
            if (role == null) return 0;

            var usersInRole = await _userManager.GetUsersInRoleAsync(role.Name);
            return usersInRole.Count;
        }

        /// <summary>
        /// الحصول على المستخدمين المخصصين لدور محدد
        /// </summary>
        public async Task<List<RoleUserDTO>> GetRoleUsers(Guid roleId)
        {
            var role = await _roleManager.FindByIdAsync(roleId.ToString());
            if (role == null) return new List<RoleUserDTO>();

            var usersInRole = await _userManager.GetUsersInRoleAsync(role.Name);
            var roleUsers = new List<RoleUserDTO>();

            foreach (var user in usersInRole)
            {
                var userClaims = await _userManager.GetClaimsAsync(user);
                var additionalPermissionsCount = userClaims.Count(c => c.Type == "additional_permission");
                var removedPermissionsCount = userClaims.Count(c => c.Type == "removed_permission");

                roleUsers.Add(new RoleUserDTO
                {
                    UserId = user.Id,
                    UserName = user.UserName,
                    FullName = user.Name,
                    IsActive = user.LockoutEnabled && (user.LockoutEnd == null || user.LockoutEnd <= DateTimeOffset.UtcNow),
                    AssignedDate = DateTime.UtcNow, // يمكن تحسينه لاحقاً
                    AdditionalPermissionsCount = additionalPermissionsCount,
                    RemovedPermissionsCount = removedPermissionsCount
                });
            }

            return roleUsers;
        }

        /// <summary>
        /// التحقق من أن الدور هو دور أساسي
        /// </summary>
        public bool IsSystemRole(string roleName)
        {
            var systemRoles = new[] { "SystemAdmin", "SalesEmployee", "PurchaseManager", "Accountant", "InventoryManager" };
            return systemRoles.Contains(roleName);
        }

        /// <summary>
        /// الحصول على إحصائيات الأدوار
        /// </summary>
        public async Task<RoleStatisticsDTO> GetRoleStatistics()
        {
            var allRoles = _roleManager.Roles.ToList();
            var totalUsers = _userManager.Users.Count();

            var statistics = new RoleStatisticsDTO
            {
                TotalRoles = allRoles.Count,
                ActiveRoles = allRoles.Count, // جميع الأدوار نشطة افتراضياً
                SystemRoles = allRoles.Count(r => IsSystemRole(r.Name)),
                CustomRoles = allRoles.Count(r => !IsSystemRole(r.Name)),
                TotalUsers = totalUsers
            };

            // الأدوار الأكثر استخداماً
            var mostUsedRoles = new List<RoleUsageDTO>();
            foreach (var role in allRoles)
            {
                var usersCount = await GetRoleUsersCount(role.Id);
                var percentage = totalUsers > 0 ? (decimal)usersCount / totalUsers * 100 : 0;

                mostUsedRoles.Add(new RoleUsageDTO
                {
                    RoleName = role.Name,
                    UsersCount = usersCount,
                    Percentage = Math.Round(percentage, 2)
                });
            }

            statistics.MostUsedRoles = mostUsedRoles.OrderByDescending(r => r.UsersCount).Take(5).ToList();

            return statistics;
        }

        /// <summary>
        /// الحصول على تفاصيل الدور مع المستخدمين
        /// </summary>
        public async Task<RoleDetailsDTO> GetRoleDetails(Guid roleId)
        {
            var roleDto = await GetRoleById(roleId);
            if (roleDto == null) return null;

            var users = await GetRoleUsers(roleId);

            // تجميع الصلاحيات حسب المجموعات
            var permissionGroups = new Dictionary<string, List<string>>();
            foreach (var group in PermissionGroups.Groups)
            {
                var rolePermissionsInGroup = roleDto.Permissions.Intersect(group.Value).ToList();
                if (rolePermissionsInGroup.Any())
                {
                    permissionGroups[group.Key] = rolePermissionsInGroup;
                }
            }

            return new RoleDetailsDTO
            {
                Role = roleDto,
                Users = users,
                PermissionGroups = permissionGroups
            };
        }
    }
}
