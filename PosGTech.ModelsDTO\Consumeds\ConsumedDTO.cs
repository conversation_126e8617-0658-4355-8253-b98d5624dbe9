﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Consumeds;

public class ConsumedDTO
{
    public Guid Id { get; set; }
    public int InvoiceNo { get; set; } = 0;
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public DateOnly Date { get; set; }
    public decimal TotalPrice { get; set; }
    public List<ConsumedItemDTO>? ConsumedItemDTOs { get; set; } = new List<ConsumedItemDTO>();
}
