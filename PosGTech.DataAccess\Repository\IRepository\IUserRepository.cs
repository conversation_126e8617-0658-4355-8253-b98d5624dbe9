﻿using Microsoft.AspNetCore.Identity;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Authentication;
using PosGTech.ModelsDTO.Users;

namespace PosGTech.DataAccess.Repository.IRepository
{
    public interface IUserRepository : IRepository<User>
    {
        Task<ResponseVM> AddUser(UserDTO model, List<Guid> Treasury, UserManager<User> _userManager);
        Task<ResponseVM> UpdateUser(Guid id, UserDTO model, UserManager<User> _userManager);
        Task<ResponseVM> DeleteUser(Guid id, UserManager<User> _userManager, Guid? currentUserId = null);
        Task<IEnumerable<UserDTO>> GetAllUser(UserManager<User> _userManager);
        Task<IEnumerable<UserDTO>> GetAllUserIncludingHidden(UserManager<User> _userManager);
        Task<ResponseVM> RestoreUser(Guid id, UserManager<User> _userManager);
        Task<bool> IsMainAdmin(Guid userId, UserManager<User> _userManager);
        Task<UserDTO> GetUserById(Guid id, UserManager<User> _userManager);
        Task<bool> HasUserActivity(Guid userId);
        Task<ResponseVM> PermanentDeleteUser(Guid id, UserManager<User> _userManager);
        Task<AuthUser> LoginAsync(LoginUser model, UserManager<User> _userManager, Jwt _jwt);
        Task<AuthUser> RefreshTokenAsync(string token, UserManager<User> _userManager, Jwt _jwt);
        Task<bool> RevokeTokenAsync(string token, UserManager<User> _userManager);
        Task<IEnumerable<UserCMDTO>> GetAllUserCM(UserManager<User> _userManager);

        // ==================== Methods جديدة لإدارة الصلاحيات ====================
        Task<List<string>> GetUserEffectivePermissions(Guid userId, UserManager<User> _userManager);
        Task<ResponseVM> ManageUserPermissions(UserPermissionManagementDTO model, UserManager<User> _userManager);
    }
}
