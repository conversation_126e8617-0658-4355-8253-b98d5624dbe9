﻿using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Stores;

namespace PosGTech.DataAccess.Repository
{
    public class StoreRepository : Repository<Store>, IStoreRepository
    {
        public StoreRepository(ApplicationDbContext db) : base(db)
        {
        }

        public void UpdateStore(Store oldStore, StoreDTO newStore)
        {
            oldStore.Name = newStore.Name;
            oldStore.CompanyName = newStore.CompanyName;
            oldStore.PhoneNumber = newStore.PhoneNumber;
            oldStore.Address = newStore.Address;
            oldStore.LogoPath = newStore.LogoPath;
            oldStore.UpdatedAt = DateTime.Now;
            Update(oldStore);
        }

        public void UpdateStoreSettings(Store oldStore, StoreSettingsDTO newStoreSettings)
        {
            oldStore.Name = newStoreSettings.Name;
            oldStore.CompanyName = newStoreSettings.CompanyName;
            oldStore.PhoneNumber = newStoreSettings.PhoneNumber;
            oldStore.Address = newStoreSettings.Address;
            oldStore.LogoPath = newStoreSettings.LogoPath;
            oldStore.UpdatedAt = DateTime.Now;
            Update(oldStore);
        }
    }
}
