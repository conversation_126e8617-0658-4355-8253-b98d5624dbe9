﻿@page "/upsertExpense/{id:guid}"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<MudDialog>
    <DialogContent>
        <EditForm id="_form" Model="@Expense" OnValidSubmit="@Upsert">
            <DataAnnotationsValidator />
            <MudPaper Class="expense-form-container" Elevation="3">
                <div class="form-header">
                    <MudText Typo="Typo.h5" Class="mb-4 primary-text">
                        @(id == Guid.Empty ? "إضافة مصروف جديد" : "تعديل المصروف")
                    </MudText>
                </div>

                <MudDivider Class="mb-4" />

                <MudGrid Class="pa-4">
                    <MudItem xs="12">
                        <MudTextField T="string"
                                      @bind-Value="Expense.Name"
                                      Label="اسم المصروف"
                                      For="@(() => Expense.Name)"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.End"
                                      AdornmentIcon="@Icons.Material.Filled.Receipt"
                                      Class="mud-input-elegant" />
                    </MudItem>

                    <MudItem xs="12" Class="mt-4">
                        <MudAlert Severity="Severity.Info" Variant="Variant.Filled" Dense="true" Class="mt-2">
                            المصروفات تساعد في تتبع نفقات العمل وإدارة الميزانية بشكل فعال
                        </MudAlert>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </EditForm>
    </DialogContent>

    <DialogActions >
        <MudButton Variant="Variant.Outlined"
                   OnClick="@Cancel"
                   Color="Color.Error"
                   StartIcon="@Icons.Material.Filled.Cancel"
                   Class="cancel-button">
            إلغاء
        </MudButton>

        <MudButton form="_form"
                   Variant="Variant.Filled"
                   ButtonType="ButtonType.Submit"
                   Color="Color.Success"
                   StartIcon="@Icons.Material.Filled.Save"
                   Class="save-button">
            حفظ
        </MudButton>
    </DialogActions>
</MudDialog>

<style>
    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }

    .expense-form-container {
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
        min-width: 320px;
    }

    .form-header {
        padding: 16px;
        /* background-color: var(--mud-palette-primary-lighten); */
    }

    .primary-text {
        color: var(--mud-palette-primary-darken);
        font-weight: 500;
    }

    .mud-input-elegant {
        margin-bottom: 8px;
    }

    .save-button, .cancel-button {
        border-radius: 4px;
        font-weight: 500;
        min-width: 100px;
        transition: all 0.2s ease;
    }

        .save-button:hover, .cancel-button:hover {
            transform: translateY(-2px);
        }
</style>



@*

<MudDialog>
    <DialogContent>
        <EditForm id="_form" Model="@Expense" OnValidSubmit="@Upsert">
            <DataAnnotationsValidator />
            <MudPaper Class="d-flex" Elevation="0">
                <MudGrid Class="pa-4">
                    <MudItem xs="12">
                        <MudTextField T="string" @bind-Value="Expense.Name" Label="اسم المصروف" For="@(() => Expense.Name)" />
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </EditForm>
    </DialogContent>
    <DialogActions>
        <MudButton Style="align-self: end;" Variant="Variant.Filled" OnClick="@Cancel" Color="Color.Error" EndIcon="@Icons.Material.Filled.Cancel" Class="px-6 ma-2">
            <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">الغاء</MudText>
        </MudButton>
        <MudButton Style="align-self: end;" form="_form" Variant="Variant.Filled" ButtonType="ButtonType.Submit" Color="Color.Primary" EndIcon="@Icons.Material.Filled.Save" Class="px-6 ma-2">
            <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">حفظ</MudText>
        </MudButton>
    </DialogActions>
</MudDialog>


<style>

    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }
</style> *@