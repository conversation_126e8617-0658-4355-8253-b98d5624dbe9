﻿@page "/Sell/{id:guid}"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]
<MudDialog Style="min-width: 100vw; height: 100vh;">
    <TitleContent>
        <div class="d-flex align-center justify-between w-100">
            <div class="d-flex align-center gap-3">
                <MudIcon Icon="@Icons.Material.Filled.Receipt" Color="Color.Primary" Size="Size.Large" />
                <div>
                    <MudText Typo="Typo.h5" Color="Color.Primary">@(id == Guid.Empty ? "فاتورة بيع جديدة" : "تعديل فاتورة بيع")</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">@DateTime.Now.ToString("dd-MM-yyyy")</MudText>
                </div>
            </div>
            @* زر رجوع *@
            <MudButton Variant="Variant.Filled" Class="button-small"
                       Color="Color.Error"
                       OnClick="Back"
                       StartIcon="@Icons.Material.Filled.ArrowBack"
                       Disabled="@_isLoading">
                رجوع
            </MudButton>
        </div>
    </TitleContent>

    <DialogContent>
        <EditForm Model="@_Sell" @onkeydown="keydownForm" OnValidSubmit="Upsert">
            <DataAnnotationsValidator />
            <!-- التصميم ثنائي اللوحة الرئيسي -->
            <div class="dual-panel-container">

                <!-- اللوحة اليمنى - نموذج الإدخال -->
                <div class="right-panel">
                    <MudPaper Elevation="2" Class="pa-4 h-100">
                        <MudText Typo="Typo.h6" Color="Color.Primary" Class="mb-3">
                            <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-2" />
                            بيانات الفاتورة
                        </MudText>

                        <!-- Header Information -->
                        <MudGrid Class="mb-4">
                            <MudItem xs="12" sm="6" Class="small-TextField">
                                <div class="d-flex align-center gap-2 invoice-navigation">
                                    <!-- زر التالي (→) -->
                                    <MudIconButton Icon="@Icons.Material.Filled.ArrowForward"
                                                   Color="Color.Primary"
                                                   Size="Size.Medium"
                                                   Variant="Variant.Outlined"
                                                   OnClick="NavigateToNextInvoice"
                                                   Title="الفاتورة التالية (→)"
                                                   Class="navigation-btn flex-shrink-0"
                                                   Disabled="@(!SellsNum.Any() || (id != Guid.Empty && SellsNum.FindIndex(x => x.Id == id) >= SellsNum.Count - 1))" />

                                    <!-- حقل رقم الفاتورة -->
                                    <MudTextField @ref=InvoiceNo Value="_Sell.InvoiceNo" Label="رقم الفاتورة"
                                                  OnKeyDown="KeyDownInvoice"
                                                  ValueChanged="@((int value) => { _Sell.InvoiceNo = value; ValidateInvoiceNumber(); })"
                                                  Min="1"
                                                  Variant="Variant.Outlined"
                                                  Class="invoice-field flex-grow-1" />

                                    <!-- زر السابق (←) -->
                                    <MudIconButton Icon="@Icons.Material.Filled.ArrowBack"
                                                   Color="Color.Primary"
                                                   Size="Size.Medium"
                                                   Variant="Variant.Outlined"
                                                   OnClick="NavigateToPreviousInvoice"
                                                   Title="الفاتورة السابقة (←)"
                                                   Class="navigation-btn flex-shrink-0"
                                                   Disabled="@(!SellsNum.Any() || (id != Guid.Empty && SellsNum.FindIndex(x => x.Id == id) <= 0))" />
                                </div>
                            </MudItem>

                            <MudItem xs="12" sm="6" Class="small-TextField">
                                <MudDatePicker @bind-Date="_dateSell" Label="التاريخ" Variant="Variant.Outlined"
                                               DateFormat="yyyy-MM-dd" />
                            </MudItem>
                        </MudGrid>

                        <!-- Basic Information Section -->
                        <MudText Typo="Typo.subtitle1" Color="Color.Secondary" Class="mb-2">المعلومات الأساسية</MudText>
                        <MudGrid Class="mb-4">
                            <MudItem xs="12" sm="6" Class="small-TextField">
                                <MudSelect T="Guid?" Value="_Sell.StoreId"
                                           ValueChanged="ChangeStore" Label="المخازن" Disabled="_Sell.Id!=Guid.Empty"
                                           AdornmentIcon="@Icons.Material.Filled.Home"
                                           Variant="Variant.Outlined"
                                           Adornment="Adornment.End"
                                           For="@(() =>_Sell.StoreId)">
                                    <MudSelectItem T="Guid?" Value="null">اختيار المخزن </MudSelectItem>
                                    @foreach (var store in stores)
                                    {
                                        <MudSelectItem T="Guid?" Value="store.Id"> @store.Name </MudSelectItem>
                                    }
                                </MudSelect>
                            </MudItem>
                            <MudItem xs="12" sm="6" Class="small-TextField">
                                <div class="d-flex gap-2">
                                    <MudComboBox @bind-Value="_Sell.Client"
                                                 Label="العميل" Editable="true"
                                                 SearchFunc="@SearchClientFunc"
                                                 ToStringFunc="@(e => e?.Name ?? string.Empty)"
                                                 Variant="Variant.Outlined"
                                                 AdornmentIcon="@Icons.Material.Filled.Person"
                                                 Adornment="Adornment.End" For="@(() => _Sell.Client)"
                                                 Class="flex-grow-1">
                                        @foreach (var client in clients)
                                        {
                                            <MudComboBoxItem Value="@client" Text="@client.Name">@client.Name</MudComboBoxItem>
                                        }
                                    </MudComboBox>

                                    <MudFab Style="height:36px;width:36px;align-self:end" tabindex=8
                                            StartIcon="@Icons.Material.Filled.Person3"
                                            OnClick="AddNewClient" Color="Color.Info"
                                            Class="ml-auto" />
                                </div>
                            </MudItem>
                        </MudGrid>

                        <!-- Product Selection Section -->
                        <MudText Typo="Typo.subtitle1" Color="Color.Secondary" Class="mb-2">اختيار المنتجات</MudText>
                        <MudGrid Class="mb-4">
                            <MudItem xs="12" sm="6" Class="small-TextField">
                                <MudComboBox T="StoreItemForSellDTO"
                                             Editable="true"
                                             Value="selectedStoreItem?.StoreItemExp?.StoreItem" @ref="ItemForAdd"
                                             ValueChanged="ChangeItem"
                                             Label="الصنف"
                                             OpenMenuAfterClear="true"
                                             Immediate="true"
                                             SearchFunc="@SearchItemFunc" ToStringFunc="@(e=> e==null?null : $"{e.Item.Name}")"
                                             OnKeyDown="SelectItem"
                                             Variant="Variant.Outlined"
                                             AdornmentIcon="@Icons.Material.Filled.ProductionQuantityLimits"
                                             Adornment="Adornment.End">
                                    @foreach (var item in items.Select(x => new StoreItemForSellDTO() { Item = new ItemCMDTO() { Id = x.Item.Id, Name = x.Item.Name }, StoreId = x.StoreId }))
                                    {
                                        <MudComboBoxItem Value="@item" Text="@item.Item.Name">@item.Item.Name</MudComboBoxItem>
                                    }
                                </MudComboBox>
                            </MudItem>

                            <MudItem xs="12" sm="6" Class="small-TextField">
                                <MudSelect T="Guid" Variant="Variant.Outlined"
                                           Class="small-TextField"
                                           Value="selectedStoreItem?.StoreItemExp?.Id ?? Guid.Empty"
                                           ValueChanged="ChangeExp"
                                           Label="تاريخ الصلاحية"
                                           AdornmentIcon="@Icons.Material.Filled.OpenWith"
                                           ToStringFunc="@(expId => GetExpDisplayText(expId))">
                                    <MudSelectItem T="Guid" Value="Guid.Empty">اختر صلاحية</MudSelectItem>
                                    @if (selectedStoreItem?.StoreItemExp?.StoreItem?.Item is not null)
                                    {
                                        var selectedItem = items.FirstOrDefault(x => x.Item.Id == selectedStoreItem.StoreItemExp.StoreItem.Item.Id);
                                        if (selectedItem is not null)
                                        {
                                            if (!selectedItem.Item.IsHaveExp)
                                            {
                                                if (selectedItem.StoreItemExps.Any())
                                                {
                                                    <MudSelectItem T="Guid" Value="selectedItem.StoreItemExps.First().Id"> @(selectedItem.StoreItemExps.First().Exp) </MudSelectItem>
                                                }
                                            }
                                            @foreach (var exp in selectedItem.StoreItemExps)
                                            {
                                                <MudSelectItem T="Guid" Value="exp.Id">@exp.Exp</MudSelectItem>
                                            }
                                        }
                                    }
                                </MudSelect>
                            </MudItem>

                            <MudItem xs="12" sm="6" Class="small-TextField">
                                <MudSelect T="ItemUnitDTO" Variant="Variant.Outlined"
                                           Value="selectedStoreItem.ItemUnit"
                                           ValueChanged="ChangeItemUnit" Label="الوحدة"
                                           Class="small-TextField"
                                           AdornmentIcon="@Icons.Material.Filled.OpenWith"
                                           For="@(() =>selectedStoreItem.ItemUnit)"
                                           ToStringFunc="@(unit => unit?.Unit?.Name ?? "اختر وحدة")">
                                    <MudSelectItem T="ItemUnitDTO" Value="null">اختر وحدة</MudSelectItem>
                                    @if (selectedStoreItem?.StoreItemExp?.StoreItem?.Item is not null)
                                    {
                                        var selectedItemForUnits = items.FirstOrDefault(x => x.Item.Id == selectedStoreItem.StoreItemExp.StoreItem.Item.Id);
                                        if (selectedItemForUnits is not null)
                                        {
                                            @foreach (var unit in selectedItemForUnits.Item.ItemUnits)
                                            {
                                                <MudSelectItem T="ItemUnitDTO" Value="unit"> @unit.Unit.Name </MudSelectItem>
                                            }
                                        }
                                    }
                                </MudSelect>
                            </MudItem>

                            <MudItem xs="12" sm="6" Class="small-TextField">
                                <MudTextField T="string"
                                              Value="@AvailableQuantityDisplay"
                                              ReadOnly="true"
                                              Variant="Variant.Outlined"
                                              Label="الكمية المتاحة"
                                              AdornmentIcon="@Icons.Material.Filled.Inventory"
                                              Adornment="Adornment.End"
                                              Class="@(AvailableQuantityDisplay == "غير متاح" ? "mud-input-text-disabled" : "")"
                                              Style="@(AvailableQuantityDisplay == "غير متاح" ? "color: #999;" : AvailableQuantityDisplay.Contains("خطأ") ? "color: #f44336;" : "color: #4caf50; font-weight: 500;")" />
                            </MudItem>

                            <MudItem xs="12" sm="4" Class="small-TextField">
                                <MudNumericField T="decimal" HideSpinButtons="true"
                                                 Min="0" Disabled="selectedStoreItem?.StoreItemExp==null"
                                                 Value="selectedStoreItem.Quantity" ValueChanged="ChangeQte"
                                                 AdornmentIcon="@Icons.Material.Filled.Numbers"
                                                 Adornment="Adornment.End"
                                                 Variant="Variant.Outlined"
                                                 Label="الكمية" />
                            </MudItem>

                            <MudItem xs="12" sm="4" Class="small-TextField">
                                <MudNumericField T="decimal" HideSpinButtons="true"
                                                 Min="0" Disabled="selectedStoreItem?.StoreItemExp==null"
                                                 @bind-Value="selectedStoreItem.SalePrice"
                                                 AdornmentIcon="@Icons.Material.Filled.Numbers"
                                                 Adornment="Adornment.End"
                                                 Variant="Variant.Outlined"
                                                 Label="السعر" />
                            </MudItem>

                            <MudItem xs="12" sm="4" Class="d-flex align-center justify-center">
                                <MudButton Variant="Variant.Filled" Color="Color.Success"
                                           StartIcon="@Icons.Material.Filled.Add"
                                           OnClick="AddSellItem"
                                           FullWidth="true"
                                           Class="add-item-btn">
                                    إضافة المنتج
                                </MudButton>
                            </MudItem>
                        </MudGrid>


                        <!-- Treasury and Payment Section -->
                        @if (id == Guid.Empty)
                        {
                            <MudText Typo="Typo.subtitle1" Color="Color.Secondary" Class="mb-2">الخزينة</MudText>
                            <MudGrid Class="mb-4">
                                <MudItem xs="12" Class="small-TextField">
                                    <MudSelect T="Guid?" @bind-Value="_Sell.TreasuryId"
                                               Label="الخزينة" Variant="Variant.Outlined" Size="Size.Small">
                                        <MudSelectItem T="Guid?" Value="null">اختيار الخزينة</MudSelectItem>
                                        @foreach (var userTreasury in userTreasuries)
                                        {
                                            <MudSelectItem T="Guid?" Value="userTreasury.Id">
                                                @userTreasury.Treasury.Name
                                            </MudSelectItem>
                                        }
                                    </MudSelect>
                                </MudItem>
                            </MudGrid>
                        }

                        <!-- Action Buttons -->
                        <MudText Typo="Typo.subtitle1" Color="Color.Secondary" Class="mb-2">الإجراءات</MudText>
                        <MudGrid Class="action-buttons-grid">
                            <MudItem xs="6" sm="4">
                                <MudButton Variant="Variant.Filled" Class="button-small"
                                           Color="Color.Success"
                                           OnClick="NewSell"
                                           StartIcon="@(_isNewLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Add)"
                                           Disabled="@_isLoading"
                                           FullWidth="true">
                                    @if (_isNewLoading)
                                    {
                                        <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                        <span style="margin-right: 8px;">جاري الإنشاء...</span>
                                    }
                                    else
                                    {
                                        <span>جديد (F8)</span>
                                    }
                                </MudButton>
                            </MudItem>

                            <MudItem xs="6" sm="4">
                                <MudButton Variant="Variant.Filled" Class="button-small"
                                           Color="Color.Primary"
                                           OnClick="Upsert"
                                           StartIcon="@(_isSaveLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Save)"
                                           Disabled="@_isLoading"
                                           FullWidth="true">
                                    @if (_isSaveLoading)
                                    {
                                        <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                        <span style="margin-right: 8px;">جاري الحفظ...</span>
                                    }
                                    else
                                    {
                                        <span>حفظ (F2)</span>
                                    }
                                </MudButton>
                            </MudItem>

                            <MudItem xs="6" sm="4">
                                <MudButton Variant="Variant.Filled" Class="button-small"
                                           Color="Color.Error"
                                           OnClick="@(() => Delete(_Sell))"
                                           StartIcon="@(_isDeleteLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Delete)"
                                           Disabled="@_isLoading"
                                           FullWidth="true">
                                    @if (_isDeleteLoading)
                                    {
                                        <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                        <span style="margin-right: 8px;">جاري الحذف...</span>
                                    }
                                    else
                                    {
                                        <span>حذف (Del)</span>
                                    }
                                </MudButton>
                            </MudItem>

                            @if (id != Guid.Empty)
                            {
                                <MudItem xs="6" sm="4">
                                    <MudButton Variant="Variant.Filled" Class="button-small"
                                               Color="Color.Primary"
                                               OnClick="@(()=>AddReceipt(FinancialId.Sale))"
                                               EndIcon="@(_isReceiptLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Receipt)"
                                               Disabled="@_isLoading"
                                               FullWidth="true">
                                        @if (_isReceiptLoading)
                                        {
                                            <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                            <span style="margin-right: 8px;">جاري الإضافة...</span>
                                        }
                                        else
                                        {
                                            <span>اضافة ايصال</span>
                                        }
                                    </MudButton>
                                </MudItem>

                                <MudItem xs="6" sm="4">
                                    <MudButton Variant="Variant.Text"
                                               Color="Color.Info"
                                               OnClick="@PreviewSell"
                                               Disabled="@_isPrinting"
                                               StartIcon="@Icons.Material.Filled.Preview"
                                               FullWidth="true">
                                        معاينة الفاتورة
                                    </MudButton>
                                </MudItem>
                            }

                            <MudItem xs="6" sm="4">
                                <MudButton Variant="Variant.Filled" Class="button-small"
                                           Color="Color.Surface" OnClick="@Print"
                                           StartIcon="@(_isPrintLoading ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Print)"
                                           Disabled="@_isLoading"
                                           FullWidth="true">
                                    @if (_isPrintLoading)
                                    {
                                        <MudProgressCircular Color="Color.Default" Size="Size.Small" Indeterminate="true" />
                                        <span style="margin-right: 8px;">جاري الطباعة...</span>
                                    }
                                    else
                                    {
                                        <span>طباعة</span>
                                    }
                                </MudButton>
                            </MudItem>
                        </MudGrid>
                    </MudPaper>
                </div>

                <!-- اللوحة اليسرى - عرض الفاتورة -->
                <div class="left-panel">
                    <MudPaper Elevation="2" Class="pa-4 h-100">
                        <MudText Typo="Typo.h6" Color="Color.Primary" Class="mb-3">
                            <MudIcon Icon="@Icons.Material.Filled.Receipt" Class="mr-2" />
                            عناصر الفاتورة
                        </MudText>

                        <!-- Invoice Items Table -->
                        <div class="invoice-items-container">
                            @if (_Sell.SellItemDTOs?.Any() == true)
                            {
                                <MudTable Items="@_Sell.SellItemDTOs" Dense="true" Hover="true" Bordered="true"
                                          Class="invoice-items-table" FixedHeader="true" Height="400px">
                                    <HeaderContent>
                                        <MudTh Style="text-align: center;">#</MudTh>
                                        <MudTh>الصنف</MudTh>
                                        <MudTh Style="text-align: center;">الوحدة</MudTh>
                                        <MudTh Style="text-align: center; width: 120px;">الكمية</MudTh>
                                        <MudTh Style="text-align: center;">السعر</MudTh>
                                        <MudTh Style="text-align: center;">بعد التخفيض</MudTh>
                                        <MudTh Style="text-align: center;">الإجمالي</MudTh>
                                        <MudTh Style="text-align: center; width: 80px;">إجراءات</MudTh>
                                    </HeaderContent>
                                    <RowTemplate Context="item">
                                        <MudTd Style="text-align: center;">@(_Sell.SellItemDTOs.ToList().IndexOf(item) + 1)</MudTd>
                                        <MudTd>
                                            <div class="item-name-cell">
                                                <MudText Typo="Typo.body2" Class="font-weight-medium">
                                                    @item.StoreItemExp.StoreItem.Item.Name
                                                </MudText>
                                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                    انتهاء: @(item.StoreItemExp.Exp?.ToString("yyyy-MM-dd") ?? "غير محدد")
                                                </MudText>
                                            </div>
                                        </MudTd>
                                        <MudTd Style="text-align: center;">@item.ItemUnit.Unit.Name</MudTd>
                                        <MudTd Style="text-align: center;">
                                            <div class="d-flex align-center justify-center gap-1">
                                                <MudIconButton Size="Size.Small" Icon="@Icons.Material.Filled.Remove"
                                                               OnClick="()=>RemoveQuantity(item)"
                                                               Color="Color.Error"
                                                               Class="quantity-btn" />
                                                <MudText Class="quantity-display">@item.Quantity.ToString("N2")</MudText>
                                                <MudIconButton Size="Size.Small" Icon="@Icons.Material.Filled.Add"
                                                               OnClick="()=>AddQuantity(item)"
                                                               Color="Color.Success"
                                                               Class="quantity-btn" />
                                            </div>
                                        </MudTd>
                                        <MudTd Style="text-align: center;">@item.SalePrice.ToString("N2")</MudTd>
                                        <MudTd Style="text-align: center;">@item.SalePriceAfterDiscount.ToString("N2")</MudTd>
                                        <MudTd Style="text-align: center;" Class="total-cell">
                                            <MudText Class="font-weight-bold">
                                                @((item.Quantity * item.SalePriceAfterDiscount).ToString("N2"))
                                            </MudText>
                                        </MudTd>
                                        <MudTd Style="text-align: center;">
                                            <MudIconButton Size="Size.Small" Icon="@Icons.Material.Filled.Delete"
                                                           Color="Color.Error" OnClick="@(() => DeleteItem(item))"
                                                           Class="delete-btn" />
                                        </MudTd>
                                    </RowTemplate>
                                </MudTable>
                            }
                            else
                            {
                                <div class="empty-invoice-state">
                                    <MudIcon Icon="@Icons.Material.Filled.ShoppingCart" Size="Size.Large" Color="Color.Secondary" />
                                    <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mt-2">
                                        لا توجد عناصر في الفاتورة
                                    </MudText>
                                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                                        ابدأ بإضافة المنتجات من اللوحة اليمنى
                                    </MudText>
                                </div>
                            }
                        </div>

                        <!-- Invoice Summary -->
                        <div class="invoice-summary">
                            <MudDivider Class="my-3" />

                            <MudGrid>
                                <MudItem xs="12" sm="6" Class="small-TextField">
                                    <MudTextField @bind-Value="_Sell.Total"
                                                  Label="الإجمالي"
                                                  Variant="Variant.Outlined"
                                                  ReadOnly="true"
                                                  AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                                                  Adornment="Adornment.End" />
                                </MudItem>

                                <MudItem xs="12" sm="6" Class="small-TextField">
                                    <MudTextField T="decimal" Value="_Sell.DiscountValue" ValueChanged="ChangeDiscountValue"
                                                  Label="التخفيض"
                                                  Variant="Variant.Outlined"
                                                  AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                                                  Adornment="Adornment.End" />
                                </MudItem>

                                <MudItem xs="12" Class="small-radio-group">
                                    <MudRadioGroup T="bool" Value="_Sell.IsDiscountValue" ValueChanged="ChangeIsDiscount" Class="d-flex gap-4 justify-center">
                                        <MudRadio Value="true" Color="Color.Success" Dense="true">
                                            <div class="d-flex gap-2 align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.EventAvailable" Size="Size.Small" />
                                                <span>بالقيمة</span>
                                            </div>
                                        </MudRadio>
                                        <MudRadio Value="false" Color="Color.Error" Dense="true">
                                            <div class="d-flex gap-2 align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.EventBusy" Size="Size.Small" />
                                                <span>بالنسبة</span>
                                            </div>
                                        </MudRadio>
                                    </MudRadioGroup>
                                </MudItem>

                                <MudItem xs="12" sm="6" Class="small-TextField">
                                    <MudTextField @bind-Value="_Sell.FinalTotal"
                                                  Label="الإجمالي بعد التخفيض" ReadOnly="true"
                                                  Variant="Variant.Outlined"
                                                  AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                                                  Adornment="Adornment.End"
                                                  Class="final-total-field" />
                                </MudItem>

                                <MudItem xs="12" sm="6" Class="small-TextField">
                                    <MudNumericField @bind-Value="_Sell.Paid" Label="المدفوع"
                                                     Min="0" Max="_Sell.FinalTotal" Disabled="@(id!=Guid.Empty)"
                                                     Variant="Variant.Outlined" Size="Size.Small" />
                                </MudItem>

                                <MudItem xs="12" Class="small-TextField">
                                    <MudTextField Value="@(_Sell.FinalTotal - _Sell.Paid)" Label="المتبقي"
                                                  Variant="Variant.Outlined" Size="Size.Small" ReadOnly="true"
                                                  Class="remaining-amount-field" />
                                </MudItem>
                            </MudGrid>
                        </div>
                    </MudPaper>
                </div>
            </div>
            <MudMessageBox @ref="mbox"
                           CancelText="إلغاء"
                           Class="rounded-lg">
                <MessageContent>
                    <div class="d-flex flex-column gap-4">
                        <MudIcon Icon="@(_isDeleteMessage? Icons.Material.Filled.Delete:Icons.Material.Filled.Warning)"
                                 Color="@(_isDeleteMessage? Color.Error:Color.Warning)"
                                 Size="Size.Large"
                                 Class="mx-auto" />
                        <MudText Align="Align.Center">
                            @_message
                        </MudText>
                    </div>
                </MessageContent>
                <YesButton>
                    <MudButton Variant="Variant.Filled"
                               Color="@(_isDeleteMessage? Color.Error:Color.Warning)"
                               StartIcon="@(_isDeleteMessage? Icons.Material.Filled.DeleteForever:Icons.Material.Filled.Info)"
                               Size="Size.Large">
                        تأكيد
                    </MudButton>
                </YesButton>
            </MudMessageBox>
        </EditForm>


    </DialogContent>


</MudDialog>



<style>
    /* التصميم ثنائي اللوحة الرئيسي */
    .dual-panel-container {
        display: flex;
        height: calc(100vh - 120px);
        gap: 16px;
        direction: rtl;
        overflow: hidden;
    }

    /* اللوحة اليمنى - نموذج الإدخال */
    .right-panel {
        flex: 0 0 45%;
        min-width: 500px;
        max-width: 600px;
        overflow-y: auto;
        padding-right: 8px;
    }

    /* اللوحة اليسرى - عرض الفاتورة */
    .left-panel {
        flex: 1;
        min-width: 400px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    /* حاوي عناصر الفاتورة */
    .invoice-items-container {
        flex: 1;
        overflow: hidden;
        margin-bottom: 16px;
    }

    /* جدول عناصر الفاتورة */
    .invoice-items-table {
        height: 100%;
    }

    .invoice-items-table .mud-table-container {
        max-height: 400px;
        overflow-y: auto;
    }

    /* خلية اسم المنتج */
    .item-name-cell {
        max-width: 200px;
    }

    /* أزرار الكمية */
    .quantity-btn {
        width: 24px;
        height: 24px;
        min-height: 24px;
    }

    .quantity-display {
        min-width: 50px;
        text-align: center;
        font-weight: 500;
    }

    /* خلية الإجمالي */
    .total-cell {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.08);
    }

    /* زر الحذف */
    .delete-btn {
        width: 28px;
        height: 28px;
        min-height: 28px;
    }

    /* حالة الفاتورة الفارغة */
    .empty-invoice-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 300px;
        text-align: center;
    }

    /* ملخص الفاتورة */
    .invoice-summary {
        background-color: rgba(var(--mud-palette-surface-rgb), 0.5);
        border-radius: 8px;
        padding: 16px;
        margin-top: auto;
    }

    /* حقل الإجمالي النهائي */
    .final-total-field .mud-input-control {
        background-color: rgba(var(--mud-palette-success-rgb), 0.1);
        font-weight: 600;
    }

    /* حقل المبلغ المتبقي */
    .remaining-amount-field .mud-input-control {
        background-color: rgba(var(--mud-palette-warning-rgb), 0.1);
        font-weight: 600;
    }

    /* مجموعة أزرار الراديو */
    .small-radio-group .mud-radio {
        margin: 0 8px;
    }

    /* شبكة أزرار الإجراءات */
    .action-buttons-grid {
        gap: 8px;
    }

    .action-buttons-grid .mud-button {
        height: 40px;
        font-size: 0.875rem;
    }

    /* زر إضافة المنتج */
    .add-item-btn {
        height: 45px;
        font-weight: 600;
    }

    /* تحسينات للتخطيط العربي RTL */
    .invoice-navigation {
        direction: rtl;
    }

    .invoice-navigation .d-flex {
        flex-direction: row-reverse;
    }

    /* تحسين أزرار التنقل */
    .navigation-btn {
        min-width: 40px;
        height: 40px;
        border-radius: 8px;
        transition: all 0.2s ease;
    }

    .navigation-btn:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .navigation-btn:active {
        transform: scale(0.95);
    }

    /* تحسين حقل رقم الفاتورة */
    .invoice-field {
        margin: 0 8px;
    }

    /* تصميم متجاوب */
    @@media (max-width: 1280px) {
        .dual-panel-container {
            flex-direction: column;
            height: auto;
        }

        .right-panel {
            flex: none;
            min-width: auto;
            max-width: none;
            margin-bottom: 16px;
        }

        .left-panel {
            flex: none;
            min-width: auto;
        }

        .invoice-items-container {
            height: 400px;
        }
    }

    @@media (max-width: 768px) {
        .dual-panel-container {
            gap: 8px;
            padding: 8px;
        }

        .right-panel, .left-panel {
            padding: 8px;
        }

        .invoice-items-table .mud-table-container {
            max-height: 300px;
        }
    }

   

    /* تحسينات إضافية للنص العربي */
    .mud-input-control, .mud-select, .mud-autocomplete {
        direction: rtl;
        text-align: right;
    }

    .mud-input-control input {
        text-align: right;
    }

    .mud-table-cell {
        text-align: right;
    }

    .mud-table-cell[style*="text-align: center"] {
        text-align: center !important;
    }
</style>

