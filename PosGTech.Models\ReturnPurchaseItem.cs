﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول مواد المشتريات
    /// </summary>
    public class ReturnPurchaseItem : BaseEntity
    {
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal Quantity { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal Price { get; set; }
        public DateOnly? Exp { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid? PurchaseItemId { get; set; }
        public PurchaseItem? PurchaseItem { get; set; }
        public Guid? ReturnPurchaseId { get; set; }
        public ReturnPurchase? ReturnPurchase { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid ItemUnitId { get; set; }
        public ItemUnit? ItemUnit { get; set; }
    }
}
