using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace PosGTech.ModelsDTO.Authorization;

/// <summary>
/// معالج التفويض المبني على الصلاحيات للـ Blazor WebAssembly
/// يتحقق من وجود الصلاحية المطلوبة في Claims المستخدم فقط
/// </summary>
public class PermissionHandlerWebAssembly : AuthorizationHandler<PermissionRequirement>
{
    /// <summary>
    /// التحقق من متطلب الصلاحية
    /// </summary>
    /// <param name="context">سياق التفويض</param>
    /// <param name="requirement">متطلب الصلاحية</param>
    /// <returns>مهمة غير متزامنة</returns>
    protected override Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        PermissionRequirement requirement)
    {
        // التحقق من أن المستخدم مُصادق عليه
        if (!context.User.Identity?.IsAuthenticated == true)
        {
            context.Fail();
            return Task.CompletedTask;
        }

        // إذا كان المطلوب هو "any" فقط تحقق من أن المستخدم مصادق عليه
        if (requirement.Permission == "any")
        {
            context.Succeed(requirement);
            return Task.CompletedTask;
        }

        // التحقق من وجود الصلاحية في Claims المستخدم
        if (context.User.HasClaim("permission", requirement.Permission))
        {
            context.Succeed(requirement);
            return Task.CompletedTask;
        }

        // التحقق من Claims أخرى قد تحتوي على الصلاحية
        var userClaims = context.User.Claims;
        
        // البحث في جميع claims من نوع "permission"
        var permissionClaims = userClaims.Where(c => c.Type == "permission").Select(c => c.Value);
        if (permissionClaims.Contains(requirement.Permission))
        {
            context.Succeed(requirement);
            return Task.CompletedTask;
        }

        // إذا لم توجد الصلاحية، فشل التفويض
        context.Fail();
        return Task.CompletedTask;
    }
}
