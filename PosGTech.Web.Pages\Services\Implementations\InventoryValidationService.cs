using Microsoft.Extensions.Logging;
using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.Sells;
using PosGTech.ModelsDTO.StoreItem;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// Implementation of inventory validation service
    /// </summary>
    public class InventoryValidationService : IInventoryValidationService
    {
        private readonly ILogger<InventoryValidationService> _logger;
        private readonly IErrorLoggingService _errorLoggingService;
        private const decimal LOW_STOCK_THRESHOLD = 5m;

        public InventoryValidationService(
            ILogger<InventoryValidationService> logger,
            IErrorLoggingService errorLoggingService)
        {
            _logger = logger;
            _errorLoggingService = errorLoggingService;
        }

        public async Task<InventoryValidationResult> ValidateInventoryAsync(
            IEnumerable<SellItemDTO> sellItems, 
            IEnumerable<StoreItemDTO> storeItems)
        {
            var result = new InventoryValidationResult { IsValid = true };

            try
            {
                foreach (var sellItem in sellItems)
                {
                    var singleValidation = await ValidateSingleItemAsync(sellItem, storeItems);
                    
                    if (!singleValidation.IsValid)
                    {
                        result.IsValid = false;
                        result.ErrorMessages.Add(singleValidation.ErrorMessage);
                        
                        var storeItem = storeItems.FirstOrDefault(x => x.Item.Id == sellItem.StoreItemExp.StoreItem.Item.Id);
                        
                        result.ValidationErrors.Add(new InventoryValidationError
                        {
                            ItemName = sellItem.StoreItemExp.StoreItem.Item.Name,
                            RequestedQuantity = singleValidation.RequestedQuantity,
                            AvailableQuantity = singleValidation.AvailableQuantity,
                            UnitName = sellItem.ItemUnit?.Unit?.Name ?? "غير محدد",
                            ExpirationDate = sellItem.StoreItemExp?.Exp?.ToDateTime(TimeOnly.MinValue),
                            ErrorMessage = singleValidation.ErrorMessage
                        });
                    }
                }

                if (!result.IsValid)
                {
                    await LogInventoryValidationErrorAsync(result);
                }
            }
            catch (Exception ex)
            {
                await _errorLoggingService.LogExceptionAsync(ex, "InventoryValidationService.ValidateInventoryAsync");
                result.IsValid = false;
                result.ErrorMessages.Add("حدث خطأ أثناء التحقق من المخزون");
            }

            return result;
        }

        public async Task<LowStockWarningResult> CheckLowStockAsync(StoreItemDTO storeItem, Guid expId)
        {
            var result = new LowStockWarningResult();

            try
            {
                if (storeItem?.Item == null)
                {
                    return result;
                }

                var storeItemExp = storeItem.StoreItemExps?.FirstOrDefault(x => x.Id == expId);
                if (storeItemExp == null)
                {
                    return result;
                }

                result.ItemName = storeItem.Item.Name;
                result.AvailableQuantity = storeItemExp.Quantity;

                if (storeItemExp.Quantity < LOW_STOCK_THRESHOLD)
                {
                    result.HasLowStock = true;
                    result.WarningMessage = $"تحذير: الصنف '{storeItem.Item.Name}' يحتوي على كمية قليلة في المخزون. الكمية المتاحة: {storeItemExp.Quantity:N2}";
                }
            }
            catch (Exception ex)
            {
                await _errorLoggingService.LogExceptionAsync(ex, "InventoryValidationService.CheckLowStockAsync", 
                    new Dictionary<string, object>
                    {
                        ["ItemId"] = storeItem?.Item?.Id.ToString() ?? "Unknown",
                        ["ExpId"] = expId.ToString()
                    });
            }

            return result;
        }

        public async Task<SingleItemValidationResult> ValidateSingleItemAsync(
            SellItemDTO sellItem, 
            IEnumerable<StoreItemDTO> storeItems)
        {
            var result = new SingleItemValidationResult { IsValid = true };

            try
            {
                if (sellItem?.StoreItemExp?.StoreItem?.Item == null || sellItem.ItemUnit == null)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "بيانات الصنف غير مكتملة";
                    return result;
                }

                var storeItem = storeItems.FirstOrDefault(x => x.Item.Id == sellItem.StoreItemExp.StoreItem.Item.Id);
                if (storeItem == null)
                {
                    result.IsValid = false;
                    result.ErrorMessage = $"الصنف '{sellItem.StoreItemExp.StoreItem.Item.Name}' غير موجود في المخزن";
                    return result;
                }

                var storeItemExp = storeItem.StoreItemExps?.FirstOrDefault(x => x.Id == sellItem.StoreItemExp.Id);
                if (storeItemExp == null)
                {
                    result.IsValid = false;
                    result.ErrorMessage = $"تاريخ الصلاحية المحدد للصنف '{sellItem.StoreItemExp.StoreItem.Item.Name}' غير موجود";
                    return result;
                }

                // Convert requested quantity to basic unit for comparison
                var requestedQuantityInBasicUnit = ItemExtensions.GetQuantityUnitDTO(sellItem.ItemUnit, sellItem.Quantity);
                var availableQuantity = storeItemExp.Quantity;

                result.RequestedQuantity = requestedQuantityInBasicUnit;
                result.AvailableQuantity = availableQuantity;

                if (requestedQuantityInBasicUnit > availableQuantity)
                {
                    result.IsValid = false;
                    result.ErrorMessage = $"الكمية المطلوبة للصنف '{sellItem.StoreItemExp.StoreItem.Item.Name}' ({requestedQuantityInBasicUnit:N2}) تتجاوز الكمية المتاحة في المخزون ({availableQuantity:N2})";
                }
            }
            catch (Exception ex)
            {
                await _errorLoggingService.LogExceptionAsync(ex, "InventoryValidationService.ValidateSingleItemAsync",
                    new Dictionary<string, object>
                    {
                        ["ItemId"] = sellItem?.StoreItemExp?.StoreItem?.Item?.Id.ToString() ?? "Unknown",
                        ["RequestedQuantity"] = sellItem?.Quantity.ToString() ?? "Unknown"
                    });
                
                result.IsValid = false;
                result.ErrorMessage = "حدث خطأ أثناء التحقق من الصنف";
            }

            return result;
        }

        private async Task LogInventoryValidationErrorAsync(InventoryValidationResult validationResult)
        {
            try
            {
                var errorMessage = "فشل في التحقق من المخزون: " + string.Join(", ", validationResult.ErrorMessages);
                var additionalData = new Dictionary<string, object>
                {
                    ["ValidationErrors"] = validationResult.ValidationErrors.Select(e => new
                    {
                        e.ItemName,
                        e.RequestedQuantity,
                        e.AvailableQuantity,
                        e.UnitName,
                        e.ErrorMessage
                    }).ToList()
                };

                await _errorLoggingService.LogExceptionAsync(
                    new InvalidOperationException(errorMessage),
                    "InventoryValidation",
                    additionalData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log inventory validation error");
            }
        }
    }
}
