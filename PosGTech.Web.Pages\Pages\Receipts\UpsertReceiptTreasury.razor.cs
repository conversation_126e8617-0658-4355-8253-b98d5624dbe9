﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.Receipts;
using PosGTech.ModelsDTO.Treasury;
using PosGTech.ModelsDTO.Users;

namespace PosGTech.Web.Pages.Pages.Receipts;

public partial class UpsertReceiptTreasury
{
    [Parameter]
    public IEnumerable<UserTreasuryCMDTO> UserTreasury { get; set; } = new List<UserTreasuryCMDTO>();
    [Parameter]
    public IEnumerable<UserCMDTO> Users { get; set; } = new List<UserCMDTO>();
    [Parameter]
    public IEnumerable<UserCMDTO> FromUsers { get; set; } = new List<UserCMDTO>();
    UserCMDTO _crruntUser, _crruntFromUser;
    [Parameter]
    public EventCallback<ReceiptDTO> ReceiptChanged { get; set; }
    [Parameter]
    public ReceiptDTO Receipt
    {
        get => _receipt;
        set
        {
            if (value == _receipt)
                return;

            _receipt = value;
            if (ReceiptChanged.HasDelegate)
            {
                ReceiptChanged.InvokeAsync(_receipt);
            }
        }
    }
    private ReceiptDTO _receipt;
    private decimal _lastValue = 0;
    protected override void OnInitialized()
    {
        if (Receipt.Id == Guid.Empty)
        {
            Receipt.IsExchange = null; _lastValue = 0;
        }
        else
        {
            _crruntUser = Receipt.UserTreasury.User;
            _crruntFromUser = Receipt.ToTreasury.User;
            _lastValue = Receipt.Value;
            StateHasChanged();
        }
    }
    private async Task<IEnumerable<UserCMDTO>> SearchUser(string value, CancellationToken token)
    {
        if (string.IsNullOrEmpty(value))
            return Users;
        return Users.Where(x => x.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase));
    }
    private async Task<IEnumerable<UserTreasuryCMDTO>> SearchUserTreasury(string value, CancellationToken token)
    {
        if (string.IsNullOrEmpty(value))
            return UserTreasury.Where(x => x.User.Id == _crruntUser?.Id);
        return UserTreasury.Where(x => x.User.Id == _crruntUser?.Id).Where(x => x.Treasury.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase));
    }
    private async Task<IEnumerable<UserTreasuryCMDTO>> SearchToUserTreasury(string value, CancellationToken token)
    {
        if (string.IsNullOrEmpty(value))
            return UserTreasury.Where(x => x.User.Id == _crruntFromUser?.Id);
        return UserTreasury.Where(x => x.User.Id == _crruntFromUser?.Id).Where(x => x.Treasury.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase));
    }
    void ValueChange(decimal value)
    {
        Receipt.Value = value;
        if (value < 0)
        {
            Receipt.Value = _lastValue;
            _snackbar.Add("الرجاء ادخال قيمة صحيحة", Severity.Error);
            return;
        }
        if (Receipt.UserTreasury == null)
        {
            Receipt.Value = _lastValue;
            _snackbar.Add("الرجاء اختيار خزينة", Severity.Error);
            return;
        }
        if (Receipt.UserTreasury.Balance + _lastValue < value)
        {
            Receipt.Value = Receipt.UserTreasury.Balance + _lastValue;
            _snackbar.Add("تجاوزت القيمة الموجودة في الخزينة", Severity.Error);
            return;
        }

    }
}