﻿using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Finacnial;

namespace PosGTech.DataAccess.Repository
{
    public class ReceiptRepository(ApplicationDbContext db, IClientRepository _clientRepository, IEmployeeRepository _employeeRepository, IUserTreasuryRepository _userTreasuryRepository) : Repository<Receipt>(db), IReceiptRepository
    {

        public async Task AddReceipt(Receipt receipt)
        {
            await ChangeBalanceForAdd(receipt);
            receipt.ReceiptNo = ((await GetLastOrDefault(order: x => x.CreatedAt, tracked: false))?.ReceiptNo).GetValueOrDefault(0) + 1;
            receipt.Financial = null;
            Add(receipt);
        }
        public async Task UpdateReceipt(Receipt oldReceipt, Receipt newReceipt)
        {
            await ChangeBalanceForDelete(oldReceipt);
            await ChangeBalanceForAdd(newReceipt);
            oldReceipt.Statement = newReceipt.Statement;
            oldReceipt.IsExchange = newReceipt.IsExchange;
            oldReceipt.Date = newReceipt.Date;
            oldReceipt.Value = newReceipt.Value;
            oldReceipt.ClientId = newReceipt.ClientId;
            oldReceipt.EmployeeId = newReceipt.EmployeeId;
            oldReceipt.ExpenseId = newReceipt.ExpenseId;
            oldReceipt.PurchaseId = newReceipt.PurchaseId;
            oldReceipt.SellId = newReceipt.SellId;
            oldReceipt.FinancialId = newReceipt.FinancialId;
            oldReceipt.UserTreasuryId = newReceipt.UserTreasuryId;
            oldReceipt.ToTreasuryId = newReceipt.ToTreasuryId;
            oldReceipt.UpdatedAt = DateTime.Now;
            Update(oldReceipt);
        }
        public async Task DeleteReceipt(Receipt receipt)
        {
            await ChangeBalanceForDelete(receipt);
            Remove(receipt);
        }
        async Task ChangeBalanceForAdd(Receipt receipt)
        {
            if (receipt.ClientId != null)
            {
                if (receipt.IsExchange == true) await _clientRepository.UpdateClientBalanceDec((Guid)receipt.ClientId, receipt.Value);
                else await _clientRepository.UpdateClientBalanceInc((Guid)receipt.ClientId, receipt.Value);
            }
            if (receipt.EmployeeId != null)
            {
                if (receipt.IsExchange == true || receipt.FinancialId == FinancialId.SalaryPayment)
                    await _employeeRepository.UpdateEmployeeBalanceDec((Guid)receipt.EmployeeId, receipt.Value);
                else
                    await _employeeRepository.UpdateEmployeeBalanceInc((Guid)receipt.EmployeeId, receipt.Value);
            }
            if (receipt.UserTreasuryId != null)
            {
                if (receipt.IsExchange == true || receipt.FinancialId == FinancialId.Withdrawal ||
                    receipt.FinancialId == FinancialId.Transfer || receipt.FinancialId == FinancialId.SalaryPayment)
                    await _userTreasuryRepository.UpdateUserTreasuryBalanceDec((Guid)receipt.UserTreasuryId, receipt.Value);
                else
                    await _userTreasuryRepository.UpdateUserTreasuryBalanceInc((Guid)receipt.UserTreasuryId, receipt.Value);
            }
            if (receipt.ToTreasuryId != null) await _userTreasuryRepository.UpdateUserTreasuryBalanceInc((Guid)receipt.ToTreasuryId, receipt.Value);
        }
        async Task ChangeBalanceForDelete(Receipt receipt)
        {
            if (receipt.ClientId != null)
            {
                if (receipt.IsExchange == true) await _clientRepository.UpdateClientBalanceInc((Guid)receipt.ClientId, receipt.Value);
                else await _clientRepository.UpdateClientBalanceDec((Guid)receipt.ClientId, receipt.Value);
            }
            if (receipt.EmployeeId != null)
            {
                if (receipt.IsExchange == true || receipt.FinancialId == FinancialId.SalaryPayment)
                    await _employeeRepository.UpdateEmployeeBalanceInc((Guid)receipt.EmployeeId, receipt.Value);
                else
                    await _employeeRepository.UpdateEmployeeBalanceDec((Guid)receipt.EmployeeId, receipt.Value);
            }
            if (receipt.UserTreasuryId != null)
            {
                if (receipt.IsExchange == true || receipt.FinancialId == FinancialId.Withdrawal ||
                    receipt.FinancialId == FinancialId.Transfer || receipt.FinancialId == FinancialId.SalaryPayment)
                    await _userTreasuryRepository.UpdateUserTreasuryBalanceInc((Guid)receipt.UserTreasuryId, receipt.Value);
                else
                    await _userTreasuryRepository.UpdateUserTreasuryBalanceDec((Guid)receipt.UserTreasuryId, receipt.Value);
            }
            if (receipt.ToTreasuryId != null) await _userTreasuryRepository.UpdateUserTreasuryBalanceDec((Guid)receipt.ToTreasuryId, receipt.Value);
        }
    }
}
