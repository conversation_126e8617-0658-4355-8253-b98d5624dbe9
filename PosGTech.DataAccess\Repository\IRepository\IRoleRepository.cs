using Microsoft.AspNetCore.Identity;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Roles;

namespace PosGTech.DataAccess.Repository.IRepository
{
    /// <summary>
    /// Interface لإدارة الأدوار والصلاحيات
    /// يتبع النمط المعياري المستخدم في باقي المشروع
    /// </summary>
    public interface IRoleRepository
    {
        // ==================== CRUD Operations - النمط المعياري ====================

        /// <summary>
        /// الحصول على جميع الأدوار مع صلاحياتها
        /// </summary>
        Task<IEnumerable<RoleDTO>> GetAllRoles();

        /// <summary>
        /// الحصول على دور محدد بمعرفه
        /// </summary>
        Task<RoleDTO> GetRoleById(Guid roleId);

        /// <summary>
        /// إنشاء دور جديد مع صلاحياته
        /// </summary>
        Task<ResponseVM> InsertRole(RoleDTO roleDto);

        /// <summary>
        /// تحديث دور موجود
        /// </summary>
        Task<ResponseVM> UpdateRole(Guid roleId, RoleDTO roleDto);

        /// <summary>
        /// حذف دور
        /// </summary>
        Task<ResponseVM> DeleteRole(Guid roleId);

        // ==================== Specialized Operations ====================

        /// <summary>
        /// الحصول على المستخدمين المخصصين لدور محدد
        /// </summary>
        Task<List<RoleUserDTO>> GetRoleUsers(Guid roleId);

        /// <summary>
        /// الحصول على تفاصيل الدور مع المستخدمين
        /// </summary>
        Task<RoleDetailsDTO> GetRoleDetails(Guid roleId);

        /// <summary>
        /// الحصول على إحصائيات الأدوار
        /// </summary>
        Task<RoleStatisticsDTO> GetRoleStatistics();

        /// <summary>
        /// تحديث صلاحيات دور محدد
        /// </summary>
        Task<ResponseVM> UpdateRolePermissions(Guid roleId, List<string> permissions);

        // ==================== Helper Methods ====================

        /// <summary>
        /// الحصول على دور محدد باسمه
        /// </summary>
        Task<Role> GetRoleByName(string roleName);

        /// <summary>
        /// الحصول على صلاحيات دور محدد
        /// </summary>
        Task<List<string>> GetRolePermissions(Guid roleId);

        /// <summary>
        /// الحصول على عدد المستخدمين في الدور
        /// </summary>
        Task<int> GetRoleUsersCount(Guid roleId);

        /// <summary>
        /// التحقق من أن الدور هو دور أساسي
        /// </summary>
        bool IsSystemRole(string roleName);
    }
}
