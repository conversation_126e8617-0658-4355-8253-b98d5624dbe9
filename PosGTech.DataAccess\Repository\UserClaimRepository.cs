using Microsoft.AspNetCore.Identity;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Users;
using PosGTech.ModelsDTO.Authorization;
using System.Security.Claims;

namespace PosGTech.DataAccess.Repository
{
    /// <summary>
    /// Repository لإدارة UserClaim للصلاحيات الإضافية والمحذوفة
    /// </summary>
    public class UserClaimRepository : IUserClaimRepository
    {
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<Role> _roleManager;

        public UserClaimRepository(UserManager<User> userManager, RoleManager<Role> roleManager)
        {
            _userManager = userManager;
            _roleManager = roleManager;
        }

        /// <summary>
        /// إضافة صلاحية إضافية للمستخدم
        /// </summary>
        public async Task<ResponseVM> AddAdditionalPermission(Guid userId, string permission)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                    return new ResponseVM { State = false, Message = "المستخدم غير موجود" };

                // التحقق من أن الصلاحية موجودة في النظام
                if (!PermissionConstants.GetAllPermissions().Contains(permission))
                    return new ResponseVM { State = false, Message = "الصلاحية غير موجودة في النظام" };

                // التحقق من عدم وجود الصلاحية مسبقاً
                var existingClaims = await _userManager.GetClaimsAsync(user);
                if (existingClaims.Any(c => c.Type == "additional_permission" && c.Value == permission))
                    return new ResponseVM { State = false, Message = "الصلاحية موجودة مسبقاً كصلاحية إضافية" };

                var claim = new Claim("additional_permission", permission);
                var result = await _userManager.AddClaimAsync(user, claim);

                return new ResponseVM
                {
                    State = result.Succeeded,
                    Message = result.Succeeded ? "تم إضافة الصلاحية الإضافية بنجاح" : string.Join(", ", result.Errors.Select(e => e.Description))
                };
            }
            catch (Exception ex)
            {
                return new ResponseVM { State = false, Message = $"خطأ في إضافة الصلاحية الإضافية: {ex.Message}" };
            }
        }

        /// <summary>
        /// إزالة صلاحية إضافية من المستخدم
        /// </summary>
        public async Task<ResponseVM> RemoveAdditionalPermission(Guid userId, string permission)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                    return new ResponseVM { State = false, Message = "المستخدم غير موجود" };

                var claim = new Claim("additional_permission", permission);
                var result = await _userManager.RemoveClaimAsync(user, claim);

                return new ResponseVM
                {
                    State = result.Succeeded,
                    Message = result.Succeeded ? "تم إزالة الصلاحية الإضافية بنجاح" : string.Join(", ", result.Errors.Select(e => e.Description))
                };
            }
            catch (Exception ex)
            {
                return new ResponseVM { State = false, Message = $"خطأ في إزالة الصلاحية الإضافية: {ex.Message}" };
            }
        }

        /// <summary>
        /// إضافة صلاحية محذوفة من الدور
        /// </summary>
        public async Task<ResponseVM> AddRemovedPermission(Guid userId, string permission)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                    return new ResponseVM { State = false, Message = "المستخدم غير موجود" };

                // التحقق من أن المستخدم لديه هذه الصلاحية في دوره
                var hasPermissionInRole = await UserHasPermissionInRole(user, permission);
                if (!hasPermissionInRole)
                    return new ResponseVM { State = false, Message = "الصلاحية غير موجودة في دور المستخدم" };

                // التحقق من عدم وجود الصلاحية مسبقاً في المحذوفات
                var existingClaims = await _userManager.GetClaimsAsync(user);
                if (existingClaims.Any(c => c.Type == "removed_permission" && c.Value == permission))
                    return new ResponseVM { State = false, Message = "الصلاحية محذوفة مسبقاً من الدور" };

                var claim = new Claim("removed_permission", permission);
                var result = await _userManager.AddClaimAsync(user, claim);

                return new ResponseVM
                {
                    State = result.Succeeded,
                    Message = result.Succeeded ? "تم حذف الصلاحية من الدور بنجاح" : string.Join(", ", result.Errors.Select(e => e.Description))
                };
            }
            catch (Exception ex)
            {
                return new ResponseVM { State = false, Message = $"خطأ في حذف الصلاحية من الدور: {ex.Message}" };
            }
        }

        /// <summary>
        /// إزالة صلاحية محذوفة (استعادة الصلاحية للدور)
        /// </summary>
        public async Task<ResponseVM> RemoveRemovedPermission(Guid userId, string permission)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId.ToString());
                if (user == null)
                    return new ResponseVM { State = false, Message = "المستخدم غير موجود" };

                var claim = new Claim("removed_permission", permission);
                var result = await _userManager.RemoveClaimAsync(user, claim);

                return new ResponseVM
                {
                    State = result.Succeeded,
                    Message = result.Succeeded ? "تم استعادة الصلاحية للدور بنجاح" : string.Join(", ", result.Errors.Select(e => e.Description))
                };
            }
            catch (Exception ex)
            {
                return new ResponseVM { State = false, Message = $"خطأ في استعادة الصلاحية: {ex.Message}" };
            }
        }

        /// <summary>
        /// الحصول على الصلاحيات الإضافية للمستخدم
        /// </summary>
        public async Task<List<UserClaimDTO>> GetUserAdditionalPermissions(Guid userId)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null) return new List<UserClaimDTO>();

            var claims = await _userManager.GetClaimsAsync(user);
            return claims
                .Where(c => c.Type == "additional_permission")
                .Select(c => new UserClaimDTO
                {
                    UserId = userId,
                    ClaimType = c.Type,
                    ClaimValue = c.Value,
                    PermissionDescription = PermissionDescriptions.GetDescription(c.Value),
                    AssignedDate = DateTime.UtcNow // يمكن تحسينه لاحقاً
                })
                .ToList();
        }

        /// <summary>
        /// الحصول على الصلاحيات المحذوفة من الدور للمستخدم
        /// </summary>
        public async Task<List<UserClaimDTO>> GetUserRemovedPermissions(Guid userId)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null) return new List<UserClaimDTO>();

            var claims = await _userManager.GetClaimsAsync(user);
            return claims
                .Where(c => c.Type == "removed_permission")
                .Select(c => new UserClaimDTO
                {
                    UserId = userId,
                    ClaimType = c.Type,
                    ClaimValue = c.Value,
                    PermissionDescription = PermissionDescriptions.GetDescription(c.Value),
                    AssignedDate = DateTime.UtcNow // يمكن تحسينه لاحقاً
                })
                .ToList();
        }

        /// <summary>
        /// الحصول على جميع Claims المستخدم
        /// </summary>
        public async Task<List<UserClaimDTO>> GetAllUserClaims(Guid userId)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null) return new List<UserClaimDTO>();

            var claims = await _userManager.GetClaimsAsync(user);
            return claims.Select(c => new UserClaimDTO
            {
                UserId = userId,
                ClaimType = c.Type,
                ClaimValue = c.Value,
                PermissionDescription = c.Type == "additional_permission" || c.Type == "removed_permission"
                    ? PermissionDescriptions.GetDescription(c.Value)
                    : c.Value,
                AssignedDate = DateTime.UtcNow // يمكن تحسينه لاحقاً
            }).ToList();
        }

        /// <summary>
        /// إدارة صلاحيات المستخدم بشكل مجمع
        /// </summary>
        public async Task<ResponseVM> ManageUserPermissions(UserPermissionManagementDTO model)
        {
            var user = await _userManager.FindByIdAsync(model.UserId.ToString());
            if (user == null)
                return new ResponseVM { State = false, Message = "المستخدم غير موجود" };

            var results = new List<string>();

            try
            {
                // إضافة الصلاحيات الإضافية
                foreach (var permission in model.PermissionsToAdd)
                {
                    var result = await AddAdditionalPermission(model.UserId, permission);
                    if (result.State)
                        results.Add($"تم إضافة الصلاحية: {PermissionDescriptions.GetDescription(permission)}");
                }

                // إزالة الصلاحيات الإضافية
                foreach (var permission in model.AdditionalPermissionsToRemove)
                {
                    var result = await RemoveAdditionalPermission(model.UserId, permission);
                    if (result.State)
                        results.Add($"تم إزالة الصلاحية الإضافية: {PermissionDescriptions.GetDescription(permission)}");
                }

                // حذف صلاحيات من الدور
                foreach (var permission in model.RolePermissionsToRemove)
                {
                    var result = await AddRemovedPermission(model.UserId, permission);
                    if (result.State)
                        results.Add($"تم حذف الصلاحية من الدور: {PermissionDescriptions.GetDescription(permission)}");
                }

                // استعادة صلاحيات محذوفة
                foreach (var permission in model.RemovedPermissionsToRestore)
                {
                    var result = await RemoveRemovedPermission(model.UserId, permission);
                    if (result.State)
                        results.Add($"تم استعادة الصلاحية: {PermissionDescriptions.GetDescription(permission)}");
                }

                return new ResponseVM
                {
                    State = true,
                    Message = "تم تحديث صلاحيات المستخدم بنجاح",
                    Data = string.Join(", ", results)
                };
            }
            catch (Exception ex)
            {
                return new ResponseVM { State = false, Message = $"خطأ في إدارة صلاحيات المستخدم: {ex.Message}" };
            }
        }

        /// <summary>
        /// التحقق من وجود صلاحية معينة للمستخدم
        /// </summary>
        public async Task<bool> UserHasPermission(Guid userId, string permission)
        {
            var effectivePermissions = await GetUserEffectivePermissions(userId);
            return effectivePermissions.EffectivePermissions.Contains(permission);
        }

        /// <summary>
        /// الحصول على تاريخ تغييرات صلاحيات المستخدم
        /// </summary>
        public async Task<List<UserPermissionHistoryDTO>> GetUserPermissionHistory(Guid userId)
        {
            // هذا يحتاج إلى تطبيق نظام تتبع التغييرات
            // مؤقتاً نرجع قائمة فارغة
            return new List<UserPermissionHistoryDTO>();
        }

        /// <summary>
        /// الحصول على الصلاحيات الفعلية للمستخدم
        /// </summary>
        public async Task<UserEffectivePermissionsDTO> GetUserEffectivePermissions(Guid userId)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null) return null;

            // الحصول على صلاحيات الدور
            var userRoles = await _userManager.GetRolesAsync(user);
            var rolePermissions = new List<string>();

            foreach (var roleName in userRoles)
            {
                var role = await _roleManager.FindByNameAsync(roleName);
                if (role != null)
                {
                    var roleClaims = await _roleManager.GetClaimsAsync(role);
                    rolePermissions.AddRange(roleClaims.Where(c => c.Type == "permission").Select(c => c.Value));
                }
            }

            // الحصول على Claims المستخدم
            var userClaims = await _userManager.GetClaimsAsync(user);
            var additionalPermissions = userClaims.Where(c => c.Type == "additional_permission").Select(c => c.Value).ToList();
            var removedPermissions = userClaims.Where(c => c.Type == "removed_permission").Select(c => c.Value).ToList();

            // حساب الصلاحيات الفعلية
            var effectivePermissions = rolePermissions
                .Union(additionalPermissions)
                .Except(removedPermissions)
                .Distinct()
                .ToList();

            return new UserEffectivePermissionsDTO
            {
                UserId = userId,
                UserName = user.UserName,
                PrimaryRole = userRoles.FirstOrDefault() ?? string.Empty,
                RolePermissions = rolePermissions.Distinct().ToList(),
                AdditionalPermissions = additionalPermissions,
                RemovedPermissions = removedPermissions,
                EffectivePermissions = effectivePermissions,
                LastModified = DateTime.UtcNow
            };
        }

        // ==================== Helper Methods ====================

        /// <summary>
        /// التحقق من أن المستخدم لديه صلاحية في دوره
        /// </summary>
        private async Task<bool> UserHasPermissionInRole(User user, string permission)
        {
            var userRoles = await _userManager.GetRolesAsync(user);

            foreach (var roleName in userRoles)
            {
                var role = await _roleManager.FindByNameAsync(roleName);
                if (role != null)
                {
                    var roleClaims = await _roleManager.GetClaimsAsync(role);
                    if (roleClaims.Any(c => c.Type == "permission" && c.Value == permission))
                        return true;
                }
            }

            return false;
        }
    }
}
