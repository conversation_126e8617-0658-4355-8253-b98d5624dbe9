﻿@page "/listReceipts"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<AuthorizeView Policy="@PermissionConstants.ReceiptsView" Context="auth">
    <Authorized>
        <MudPaper Class="d-flex flex-column justify-space-evenly desktop rounded-0" Elevation="0">

    <MudTable Items="@receipts" Height="calc(100vh - 170px)" Loading="loading" Breakpoint="Breakpoint.Sm" Virtualize="true" Striped="true" Dense="true" FixedHeader="true" Elevation="4" Filter="new Func<ReceiptDTO,bool>(FilterFunc1)" @bind-SelectedItem="selectedItem">

        <ToolBarContent>
            <MudText Align="@Align.Justify" Typo="Typo.h6" Style="margin-top:24px; margin-left:48px;">الإيصالات</MudText>
            <MudTextField @bind-Value="Search" Placeholder="بحث" Adornment="Adornment.End" AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium" Class="mt-0"></MudTextField>
            <MudSpacer />
            <MudButton Variant="Variant.Filled" EndIcon="@Icons.Material.Filled.Add" OnClick="()=>Upsert(Guid.Empty,null)" Color="Color.Primary">جديد</MudButton>
        </ToolBarContent>



        <HeaderContent>
            <MudTh Class="rounded-0">ت</MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<ReceiptDTO, object>(x=>x.ReceiptNo)">رقم الإيصال</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<ReceiptDTO, object>(x=>x.Date)">التاريخ</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<ReceiptDTO, object>(x=>x.IsExchange)">نوع الواصل</MudTableSortLabel></MudTh>
            <MudTh></MudTh>
            <MudTh Class="rounded-0"><MudTableSortLabel SortBy="new Func<ReceiptDTO, object>(x=>x.Value)">القيمة</MudTableSortLabel></MudTh>
            <MudTh Class="rounded-0"><MudTableSortLabel SortBy="new Func<ReceiptDTO, object>(x=>x.UserTreasury)">الخزينة</MudTableSortLabel></MudTh>
            <MudTh></MudTh>
        </HeaderContent>
        <RowTemplate>
            <MudTd DataLabel="ت">@(receipts.ToList().IndexOf(context) + 1)</MudTd>
            <MudTd DataLabel="رقم الإيصال">@context.ReceiptNo</MudTd>
            <MudTd DataLabel="التاريخ">@context.Date.Value.ToShortDateString()</MudTd>
            <MudTd DataLabel="نوع الواصل">@(context.IsExchange == null ? context?.Financial?.Name : context.IsExchange == true ? "صرف" : "قبض")</MudTd>
            @switch (context.Financial?.Name)
            {
                case "مصروفات":
                    <MudTd DataLabel="مصروفات">@context.Expense.Name</MudTd>
                    break;
                case "موظف":
                    <MudTd DataLabel="موظف">@context.Employee.Name</MudTd>
                    break;
                case "صرف مرتب":
                    <MudTd DataLabel="صرف مرتب">@context.Employee.Name</MudTd>
                    break;
                case "عميل":
                    <MudTd DataLabel="عميل">@context.Client.Name</MudTd>
                    break;
                case "فاتورة مشتريات":
                    <MudTd DataLabel="فاتورة مشتريات">فاتورة مشتريات رقم @(context.Purchase.InvoiceNo +" - "+context.Client.Name)</MudTd>
                    break;
                case "فاتورة مبيعات":
                    <MudTd DataLabel="فاتورة مبيعات">فاتورة مبيعات رقم @(context.Sell.InvoiceNo + " - " + context.Client.Name)</MudTd>
                    break;
                case "تحويل":
                    <MudTd DataLabel="تحويل">@(context.ToTreasury?.User?.Name + " - " + context.ToTreasury?.Treasury?.Name)</MudTd>
                    break;
                case "إيداع":
                    <MudTd DataLabel="إيداع">-</MudTd>
                    break;
                case "سحب":
                    <MudTd DataLabel="سحب">-</MudTd>
                    break;
                case "رصيد افتتاحي للعميل":
                    <MudTd DataLabel="رصيد افتتاحي للعميل">@context.Client.Name</MudTd>
                    break;
                case "رصيد افتتاحي للموظف":
                    <MudTd DataLabel="رصيد افتتاحي للموظف">@context.Employee.Name</MudTd>
                    break;
            }
            <MudTd DataLabel="القيمة">@context.Value</MudTd>
            <MudTd DataLabel="الخزينة">@(context.UserTreasury?.User?.Name + " - " + context.UserTreasury?.Treasury?.Name)</MudTd>
            <MudTd>
                <MudIconButton Size="Size.Small" Color="Color.Info" Icon="@Icons.Material.Filled.Edit" OnClick="()=>Upsert(context.Id,context.Financial.Id)" />
                <MudIconButton Size="Size.Small" Color="Color.Error" Icon="@Icons.Material.Filled.Delete" OnClick="()=>Delete(context)" />
            </MudTd>
        </RowTemplate>


        <PagerContent>
            <MudTablePager style="height:24px;" PageSizeOptions="new int[] {int.MaxValue }"
                           RowsPerPageString="عدد الصفوف :"
                           InfoFormat="{last_item} -- {all_items}"
                           HorizontalAlignment="HorizontalAlignment.Center"
                           HideRowsPerPage="true"
                           HidePageNumber="true"
                           HidePagination="true" />
        </PagerContent>
    </MudTable>

</MudPaper>




<MudMessageBox @ref="mbox" Title="تنبيه" CancelText="إلغاء">
    <MessageContent>
        هل تريد حذف <b>@NameReceiptForDelete</b> ؟
    </MessageContent>
    <YesButton>
        <MudButton Variant="Variant.Filled" Color="Color.Error" StartIcon="@Icons.Material.Filled.DeleteForever">حذف!</MudButton>
    </YesButton>
</MudMessageBox>




<style>

    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }
</style>
    </Authorized>
    <NotAuthorized>
        <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Class="ma-4">
            ليس لديك صلاحية الوصول لعرض الإيصالات
        </MudAlert>
    </NotAuthorized>
</AuthorizeView>