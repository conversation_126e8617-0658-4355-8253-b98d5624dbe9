﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.CustomValidationAttribute
{
    public class PrecisionAttribute : ValidationAttribute
    {
        private readonly int _precision;
        private readonly int _scale;

        public PrecisionAttribute(int precision, int scale)
        {
            _precision = precision;
            _scale = scale;
        }



        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value != null && value is decimal decimalValue)
            {
                var maxAllowedValue = (decimal)Math.Pow(10, _precision - _scale) - (decimal)Math.Pow(10, -_scale);

                if (decimalValue > maxAllowedValue || decimalValue < -maxAllowedValue)
                {
                    return new ValidationResult(ErrorMessage);
                }
            }

            return ValidationResult.Success;
        }
    }
}
