﻿@page "/login"
@using PosGTech.Web.Services
@inject IdentityAuthenticationStateProvider authStateProvider
@* <AuthorizeView>
    <NotAuthorized>
        <MudPaper Class="d-flex flex-column  desktop rounded-0" Elevation="0">



            <MudPaper Class="d-flex flex-column desktop px-2">
                <MudPaper Height="45%" Elevation="0">

                    <MudGrid>
                        <MudItem xs="6" sm="12">
                            <MudTextField @bind-Value="userName" Label="اسم المستخدم" Required=true RequiredError="هذا الحقل مطلوب" Variant="Variant.Text" Class="mx-8" />
                        </MudItem>
                        <MudItem xs="6" sm="12">
                            <MudTextField @bind-Value="password" Label="كلمة المرور" Required=true RequiredError="هذا الحقل مطلوب" Variant="Variant.Text" InputType="@PasswordInput" Adornment="Adornment.End" AdornmentIcon="@PasswordInputIcon" OnAdornmentClick="ButtonTestclick" AdornmentAriaLabel="Show Password" Class="mx-8" />
                        </MudItem>
                        <MudItem xs="3">

                        </MudItem>
                        <MudItem xs="12">
                            <MudButton Style="align-self: end;" Variant="Variant.Filled" Color="Color.Primary" OnClick="@DoLoginAsync" EndIcon="@Icons.Material.Filled.Login" Class="px-6">
                                <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6"> دخول</MudText>
                            </MudButton>
                        </MudItem>
                    </MudGrid>
                </MudPaper>

            </MudPaper>
        </MudPaper>


    </NotAuthorized>
</AuthorizeView> *@


<AuthorizeView Context="auth">
    <NotAuthorized>
        <MudPaper Class="d-flex justify-center align-center" Style="height: 100vh; background: linear-gradient(135deg, var(--mud-palette-primary) 0%, var(--mud-palette-primary-darken) 100%);">
            <MudCard Class="pa-8" Style="width: 400px; max-width: 90vw;" Elevation="3">
                <MudCardHeader>
                    <MudStack AlignItems="AlignItems.Center" Spacing="2">
                        <MudAvatar Color="Color.Primary" Size="Size.Large">
                            <MudIcon Icon="@Icons.Material.Filled.Security" Size="Size.Large" />
                        </MudAvatar>
                        <MudText Typo="Typo.h4" Align="Align.Center" Class="mud-primary-text">تسجيل الدخول</MudText>
                        <MudText Typo="Typo.body2" Class="mud-text-secondary">مرحباً بك في نظام PosGTech</MudText>
                    </MudStack>
                </MudCardHeader>
                
                <MudCardContent>
                    <MudForm @bind-IsValid="@isValid" Class="mt-4">
                        <MudTextField 
                            @bind-Value="userName"
                            Label="اسم المستخدم"
                            Required="true"
                            RequiredError="هذا الحقل مطلوب"
                            Class="mt-3"
                            Variant="Variant.Outlined"
                            Adornment="Adornment.End"
                            AdornmentIcon="@Icons.Material.Filled.Person"
                            AdornmentColor="Color.Primary"/>

                        <MudTextField 
                            @bind-Value="password"
                            Label="كلمة المرور"
                            Required="true"
                            RequiredError="هذا الحقل مطلوب"
                            Class="mt-3"
                            Variant="Variant.Outlined"
                            InputType="@PasswordInput"
                            Adornment="Adornment.End"
                            AdornmentColor="Color.Primary"
                             AdornmentIcon="@PasswordInputIcon"
                                      OnAdornmentClick="ButtonTestclick" AdornmentAriaLabel="Show Password">
                               
                            
                        </MudTextField>
                    </MudForm>
                </MudCardContent>

                <MudCardActions Class="d-flex justify-center pt-4">
                    <MudButton 
                        Variant="Variant.Filled"
                        Color="Color.Primary"
                        Size="Size.Large"
                        FullWidth="true"
                        OnClick="@DoLoginAsync"
                        StartIcon="@Icons.Material.Filled.Login"
                        DisableElevation="true">
                        تسجيل الدخول
                    </MudButton>
                </MudCardActions>

                <MudDivider Class="my-4"/>
                
                <MudText Align="Align.Center" Typo="Typo.caption" Class="d-flex justify-center align-center gap-1">
                    <MudIcon Icon="@Icons.Material.Filled.Shield" Size="Size.Small" Color="Color.Success"/>
                    تم تأمين الاتصال
                </MudText>
            </MudCard>
        </MudPaper>
    </NotAuthorized>
</AuthorizeView>

@code {
    private string userName = "Admin";
    private string password = "Admin123*";
    bool isShow;
    private bool isValid;
    InputType PasswordInput = InputType.Password;
    string PasswordInputIcon = Icons.Material.Filled.VisibilityOff;
    void ButtonTestclick()
    {
        @if (isShow)
        {
            isShow = false;
            PasswordInputIcon = Icons.Material.Filled.VisibilityOff;
            PasswordInput = InputType.Password;
        }
        else
        {
            isShow = true;
            PasswordInputIcon = Icons.Material.Filled.Visibility;
            PasswordInput = InputType.Text;
        }
    }
    public async Task DoLoginAsync()
    {

        if (string.IsNullOrWhiteSpace(userName))
        {
            return;
        }

        if (string.IsNullOrWhiteSpace(password))
        {
            return;
        }
        try
        {
            var result = await authStateProvider.Login(new() { UserName = userName, Password = password });
            if (result.isAuthenticated)
            {
                _navigation.NavigateTo("/");
                // MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                _snackbar.Add(result.Message, Severity.Error);
            }
        }
        catch (Exception ex)
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
        }
    }

}
