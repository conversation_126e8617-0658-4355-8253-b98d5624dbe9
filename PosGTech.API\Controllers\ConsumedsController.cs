﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Consumeds;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ConsumedsController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("getAllConsumeds")]
        public async Task<IActionResult> GetAllConsumeds()
        {
            var consumeds = maper.Map<IEnumerable<Consumed>, IEnumerable<ConsumedDTO>>(await unitOfWork.Consumed.GetAll());
            return Ok(consumeds);
        }


        [HttpGet("getConsumedById/{id:Guid}")]
        public async Task<IActionResult> GetConsumedById([FromRoute] Guid id)
        {
            var consumed = maper.Map<Consumed, ConsumedDTO>(await unitOfWork.Consumed.GetFirstOrDefault(x => x.Id == id, "ConsumedItems.Item"));
            return Ok(consumed);
        }



        [HttpPost("insertConsumed")]
        public async Task<IActionResult> InsertConsumed([FromBody] ConsumedDTO model)
        {
            var newconsumed = maper.Map<ConsumedDTO, Consumed>(model);
            await unitOfWork.Consumed.AddConsumed(newconsumed);
            //  if (unitOfWork.Item.CheckItemsBelowZero()) return BadRequest(new ResponseVM() { State = false, Message = "الكمية غير موجودة" });
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpPut("updateConsumed/{id:Guid}")]
        public async Task<IActionResult> UpdateConsumed([FromRoute] Guid id, ConsumedDTO model)
        {
            var oldconsumed = await unitOfWork.Consumed.GetFirstOrDefault(x => x.Id == id, "ConsumedItems");

            if (oldconsumed == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });

            await unitOfWork.Consumed.UpdateConsumed(oldconsumed, maper.Map<ConsumedDTO, Consumed>(model));
            //  if (unitOfWork.Item.CheckItemsBelowZero()) return BadRequest(new ResponseVM() { State = false, Message = "الكمية غير موجودة" });
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpDelete("deleteConsumed/{id:Guid}")]
        public async Task<IActionResult> DeleteConsumed([FromRoute] Guid id)
        {
            var oldconsumed = await unitOfWork.Consumed.GetFirstOrDefault(x => x.Id == id, "ConsumedItems");
            if (oldconsumed == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            await unitOfWork.Consumed.DeleteConsumed(oldconsumed);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }
    }
}
