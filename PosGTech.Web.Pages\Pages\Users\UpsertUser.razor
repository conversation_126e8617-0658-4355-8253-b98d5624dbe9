﻿@page "/upsertUser/{id:guid}"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<MudContainer MaxWidth="MaxWidth.Large" Class="user-management-container">
    <MudCard Elevation="3" Class="rounded-lg user-card">
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h5" Class="mb-2 primary-text font-weight-bold">إدارة المستخدمين</MudText>
                <MudDivider />
            </CardHeaderContent>
            <CardHeaderActions>
                <MudButton Href="/listUsers" Color="Color.Default" Variant="Variant.Text"
                           StartIcon="@Icons.Material.Filled.ArrowForward" Class="ml-2">
                    <MudText Typo="Typo.button">رجوع</MudText>
                </MudButton>
            </CardHeaderActions>
        </MudCardHeader>

        <MudCardContent>
            <EditForm Model="@userCurrent" OnValidSubmit="@Upsert" Class="mt-3">
                <DataAnnotationsValidator />

                <MudGrid Spacing="3">
                    <MudItem xs="12" sm="6" md="4">
                        <MudPaper Elevation="0" Class="pa-4 rounded-lg form-section">
                            <MudText Typo="Typo.subtitle1" Class="mb-4 section-header">المعلومات الشخصية</MudText>

                            <MudTextField T="string" @bind-Value="userCurrent.Name"
                                          Label="الاسم كامل"
                                          Variant="Variant.Outlined"
                                          Adornment="Adornment.Start"
                                          AdornmentIcon="@Icons.Material.Filled.Person"
                                          Class="mb-3"
                                          For="@(() => userCurrent.Name)" />

                            <MudTextField T="string" @bind-Value="userCurrent.UserName"
                                          Label="اسم المستخدم"
                                          Variant="Variant.Outlined"
                                          Adornment="Adornment.Start"
                                          AdornmentIcon="@Icons.Material.Filled.AccountCircle"
                                          Class="mb-3"
                                          For="@(() => userCurrent.UserName)" />
                        </MudPaper>
                    </MudItem>

                    <MudItem xs="12" sm="6" md="4">
                        <MudPaper Elevation="0" Class="pa-4 rounded-lg form-section">
                            <MudText Typo="Typo.subtitle1" Class="mb-4 section-header">كلمة المرور</MudText>

                            <MudTextField T="string" @bind-Value="userCurrent.Password"
                                          Required="IsRequired"
                                          Label="كلمة المرور"
                                          Variant="Variant.Outlined"
                                          InputType="InputType.Password"
                                          Adornment="Adornment.End"
                                          AdornmentIcon="@Icons.Material.Filled.Lock"
                                          Class="mb-3"
                                          For="@(() => userCurrent.Password)" />

                            <MudTextField T="string" @bind-Value="userCurrent.ConfirmPassword"
                                          Label="تأكيد كلمة المرور"
                                          Variant="Variant.Outlined"
                                          InputType="InputType.Password"
                                          Adornment="Adornment.End"
                                          AdornmentIcon="@Icons.Material.Filled.Password"
                                          Class="mb-3"
                                          For="@(() => userCurrent.ConfirmPassword)" />
                        </MudPaper>
                    </MudItem>

                    <MudItem xs="12" sm="12" md="4">
                        <MudPaper Elevation="0" Class="pa-4 rounded-lg form-section">
                            <MudText Typo="Typo.subtitle1" Class="mb-4 section-header">حالة الحساب</MudText>

                            <MudSwitch T="bool" @bind-Value="@userCurrent.State"
                                       Color="Color.Success"
                                       Label="الحساب نشط"
                                       Class="ml-3 mb-4" />
                        </MudPaper>
                    </MudItem>

                    <!-- قسم اختيار الدور الأساسي -->
                    <MudItem xs="12" sm="6">
                        <MudPaper Elevation="0" Class="pa-4 rounded-lg form-section">
                            <MudText Typo="Typo.subtitle1" Class="mb-4 section-header">
                                <MudIcon Icon="@Icons.Material.Filled.AdminPanelSettings" Class="ml-2" />
                                الدور الأساسي
                            </MudText>

                            <MudSelect T="string" Value="userCurrent.RoleName"
                                       ValueChanged="@OnRoleChanged"
                                       Label="اختر الدور"
                                       Variant="Variant.Outlined"
                                       AnchorOrigin="Origin.BottomCenter"
                                       Class="mb-3"
                                       Required="true"
                                       For="@(() => userCurrent.RoleName)">
                                @foreach (var role in availableRoles)
                                {
                                    <MudSelectItem Value="@role.Name" @key="@($"role_{role.Name}")">
                                        <div class="d-flex align-center">
                                            <MudIcon Icon="@GetRoleIcon(role.Name)" Class="ml-2" />
                                            <div>
                                                <MudText Typo="Typo.body1">@role.Name</MudText>
                                                <MudText Typo="Typo.caption" Class="text-muted">@GetRoleDescription(role.Name)</MudText>
                                            </div>
                                        </div>
                                    </MudSelectItem>
                                }
                            </MudSelect>

                            @if (!string.IsNullOrEmpty(userCurrent.RoleName))
                            {
                                <div class="d-flex justify-space-between align-center mt-2">
                                    <MudAlert Severity="Severity.Info" Class="flex-grow-1">
                                        <MudText Typo="Typo.body2">
                                            <strong>صلاحيات هذا الدور:</strong> @GetRolePermissionsCount(userCurrent.RoleName) صلاحية
                                        </MudText>
                                    </MudAlert>

                                    <MudButton Variant="Variant.Outlined"
                                               Color="Color.Primary"
                                               StartIcon="Icons.Material.Filled.Refresh"
                                               OnClick="RefreshPermissions"
                                               Size="Size.Small"
                                               Class="ml-2">
                                        تحديث
                                    </MudButton>
                                </div>
                            }
                        </MudPaper>
                    </MudItem>

                    <!-- قسم ملخص الصلاحيات -->
                    <MudItem xs="12" sm="6">
                        <MudPaper Elevation="0" Class="pa-4 rounded-lg form-section">
                            <MudText Typo="Typo.subtitle1" Class="mb-4 section-header">
                                <MudIcon Icon="@Icons.Material.Filled.Security" Class="ml-2" />
                                ملخص الصلاحيات
                            </MudText>

                            @if (userCurrent.EffectivePermissions?.Any() == true)
                            {
                                <div class="permissions-summary">
                                    <MudText Typo="Typo.body2" Class="mb-2">
                                        إجمالي الصلاحيات: <strong>@userCurrent.EffectivePermissions.Count</strong>
                                    </MudText>

                                    <div class="permissions-breakdown">
                                        <MudChip T="string" Size="Size.Small" Color="Color.Primary" Class="ma-1">
                                            من الدور: @GetRolePermissionsCount(userCurrent.RoleName)
                                        </MudChip>
                                        <MudChip T="string" Size="Size.Small" Color="Color.Success" Class="ma-1">
                                            إضافية: @(userCurrent.AdditionalPermissions?.Count ?? 0)
                                        </MudChip>
                                        <MudChip T="string" Size="Size.Small" Color="Color.Warning" Class="ma-1">
                                            محذوفة: @(userCurrent.RemovedPermissions?.Count ?? 0)
                                        </MudChip>
                                    </div>
                                </div>
                            }
                            else
                            {
                                <MudAlert Severity="Severity.Warning">
                                    <MudText Typo="Typo.body2">يرجى اختيار دور أولاً لعرض الصلاحيات</MudText>
                                </MudAlert>
                            }
                        </MudPaper>
                    </MudItem>

                    <!-- قسم إدارة الصلاحيات الموحد -->
                    <MudItem xs="12">
                        <MudPaper Elevation="0" Class="pa-4 rounded-lg form-section">
                            <MudText Typo="Typo.subtitle1" Class="mb-4 section-header">
                                <MudIcon Icon="@Icons.Material.Filled.Security" Class="ml-2" />
                                إدارة صلاحيات المستخدم
                            </MudText>

                            @if (!string.IsNullOrEmpty(userCurrent.RoleName))
                            {
                                <MudAlert Severity="Severity.Info" Class="mb-3" Dense="true">
                                    <MudText Typo="Typo.body2">
                                        ✅ مُختار = المستخدم يملك هذه الصلاحية | ☐ غير مُختار = المستخدم لا يملك هذه الصلاحية
                                    </MudText>
                                </MudAlert>

                                <div class="permissions-tabs-container">
                                    <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
                                        @{
                                            Console.WriteLine($"Rendering permissions for role: {userCurrent.RoleName}");
                                            Console.WriteLine($"PermissionGroups.Groups count: {PermissionGroups.Groups.Count}");
                                        }
                                        @foreach (var group in PermissionGroups.Groups)
                                        {
                                            var groupPermissions = group.Value;
                                            Console.WriteLine($"Group: {group.Key}, Permissions: {groupPermissions.Count}");

                                            if (groupPermissions.Any())
                                            {
                                                <MudTabPanel Text="@group.Key" Icon="@GetGroupIcon(group.Key)" @key="@($"tab_{group.Key}")">
                                                    <div class="permission-tab-content">
                                                        <MudText Typo="Typo.h6" Class="mb-3 text-primary">
                                                            <MudIcon Icon="@GetGroupIcon(group.Key)" Class="ml-2" />
                                                            @group.Key
                                                        </MudText>

                                                        <MudDivider Class="mb-4" />

                                                        <MudGrid Spacing="3">
                                                            @foreach (var permission in groupPermissions)
                                                            {
                                                                var isChecked = IsPermissionChecked(permission);

                                                                <MudItem xs="12" sm="6" md="4" lg="3" @key="@($"{userCurrent.RoleName}_{permission}")">
                                                                    <MudPaper Elevation="1" Class="pa-3 permission-card">
                                                                        <MudCheckBox T="bool"
                                                                                     @key="@($"checkbox_{userCurrent.RoleName}_{permission}")"
                                                                                     Value="@isChecked"
                                                                                     ValueChanged="@((bool value) => OnPermissionToggle(permission, value))"
                                                                                     Label="@GetPermissionDescription(permission)"
                                                                                     Color="@GetPermissionColor(permission)"
                                                                                     Class="permission-checkbox-card"
                                                                                     Dense="false" />
                                                                    </MudPaper>
                                                                </MudItem>
                                                            }
                                                        </MudGrid>
                                                    </div>
                                                </MudTabPanel>
                                            }
                                        }
                                    </MudTabs>
                                </div>
                            }
                            else
                            {
                                <MudAlert Severity="Severity.Warning">
                                    <MudText Typo="Typo.body2">يرجى اختيار دور أولاً لإدارة الصلاحيات</MudText>
                                </MudAlert>
                            }
                        </MudPaper>
                    </MudItem>
                </MudGrid>

                <MudDivider Class="my-4" />

                <div class="d-flex justify-end">
                    <MudButton Variant="Variant.Text"
                               Color="Color.Default"
                               Href="/listUsers"
                               Disabled="@isSaving"
                               Class="mx-2">
                        إلغاء
                    </MudButton>
                    <MudButton Variant="Variant.Filled"
                               ButtonType="ButtonType.Submit"
                               Color="Color.Primary"
                               StartIcon="@(isSaving ? Icons.Material.Filled.HourglassEmpty : Icons.Material.Filled.Save)"
                               Disabled="@isSaving"
                               Class="px-6">
                        @(isSaving ? "جاري الحفظ..." : "حفظ البيانات")
                    </MudButton>
                </div>
            </EditForm>
        </MudCardContent>
    </MudCard>
</MudContainer>

<UpsertUserCSS />

<style>
    .permissions-tabs-container {
        border: 1px solid var(--mud-palette-lines-default);
        border-radius: 8px;
        overflow: hidden;
    }

    .permission-tab-content {
        min-height: 400px;
        max-height: 600px;
        overflow-y: auto;
    }

    .permission-card {
        border: 1px solid var(--mud-palette-lines-default);
        border-radius: 6px;
        transition: all 0.2s ease;
        background: var(--mud-palette-surface);
    }

    .permission-card:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
    }

    .permission-checkbox-card {
        margin: 0;
        width: 100%;
    }

    .permission-checkbox-card .mud-checkbox-label {
        font-size: 0.875rem;
        line-height: 1.4;
        font-weight: 500;
        word-wrap: break-word;
        white-space: normal;
    }

    .permissions-summary {
        background: var(--mud-palette-background-grey);
        padding: 12px;
        border-radius: 8px;
        border-left: 4px solid var(--mud-palette-primary);
    }

    .permissions-breakdown {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        margin-top: 8px;
    }

    .form-section {
        background: var(--mud-palette-surface);
        border: 1px solid var(--mud-palette-lines-default);
    }

    .section-header {
        color: var(--mud-palette-primary);
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .user-management-container {
        padding: 20px;
    }

    .user-card {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    /* تحسينات للتابات */
    .mud-tabs .mud-tab {
        font-weight: 500;
        font-size: 0.9rem;
    }

    .mud-tabs .mud-tab.mud-tab-active {
        color: var(--mud-palette-primary);
        font-weight: 600;
    }

    .mud-tabs .mud-tab-panel {
        background: var(--mud-palette-surface);
    }

    @@media (max-width: 768px) {
        .permission-tab-content {
            max-height: 400px;
        }

        .permission-checkbox-card .mud-checkbox-label {
            font-size: 0.8rem;
        }

        .mud-tabs .mud-tab {
            font-size: 0.8rem;
            padding: 8px 12px;
        }

        .permission-card {
            margin-bottom: 8px;
        }
    }

    @@media (max-width: 600px) {
        .mud-tabs {
            overflow-x: auto;
        }

        .mud-tabs .mud-tab {
            min-width: 120px;
            flex-shrink: 0;
        }
    }
</style>









@* 
<MudPaper Class="d-flex flex-column desktop rounded-0" Elevation="4">


    <EditForm Model="@userCurrent" OnValidSubmit="@Upsert">
        <DataAnnotationsValidator />
        <MudPaper Class="d-flex flex-column desktop">

            <MudButton Style="align-self: end;" Color="@Color.Secondary" Variant="@Variant.Outlined" EndIcon="@Icons.Material.Filled.ArrowBack" Class="px-6 ma-2">
                <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">رجوع</MudText>
            </MudButton>

            <MudGrid Class="px-4">

                <MudItem xs="3">
                    <MudTextField T="string" @bind-Text="userCurrent.Name" Label="الاسم كامل" For="@(() => userCurrent.Name)" />

                </MudItem>
                <MudItem xs="9" />

                <MudItem xs="3">
                    <MudTextField T="string" @bind-Text="userCurrent.UserName" Label="اسم المستخدم" For="@(() => userCurrent.UserName)" />

                </MudItem>
                <MudItem xs="9" />
                <MudItem xs="4">
                    <MudTextField T="string" @bind-Text="userCurrent.Password" Required="IsRequired" Label="كلمة المرور" For="@(() => userCurrent.Password)" />

                </MudItem>
                <MudItem xs="1" />
                <MudItem xs="4">
                    <MudTextField T="string" @bind-Text="userCurrent.ConfirmPassword" Label="تأكيد كلمة المرور" For="@(() => userCurrent.ConfirmPassword)" />

                </MudItem>


            </MudGrid>
            <MudPaper Class="align-self-end flex-1 d-flex align-end" Elevation="0">
                <MudButton Style="align-self: end;" Variant="Variant.Filled" ButtonType="ButtonType.Submit" Color="Color.Primary" EndIcon="@Icons.Material.Filled.Save" Class="px-6 ma-2 ">
                    <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">حفظ</MudText>
                </MudButton>
            </MudPaper>

        </MudPaper>
    </EditForm>
</MudPaper> *@