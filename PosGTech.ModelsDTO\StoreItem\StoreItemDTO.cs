﻿using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.StoreItemExp;

namespace PosGTech.ModelsDTO.StoreItem
{
    public class StoreItemDTO
    {
        public ItemDTO? Item { get; set; }
        public Guid StoreId { get; set; }

        public ICollection<StoreItemExpDTO>? StoreItemExps { get; set; } = new HashSet<StoreItemExpDTO>();
        //public ICollection<TransferItem>? TransferItems { get; set; }
    }
}
