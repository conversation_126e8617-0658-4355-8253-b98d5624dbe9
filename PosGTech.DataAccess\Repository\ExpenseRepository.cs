﻿using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Expenses;

namespace PosGTech.DataAccess.Repository
{
    public class ExpenseRepository : Repository<Expense>, IExpenseRepository
    {
        public ExpenseRepository(ApplicationDbContext db) : base(db)
        {
        }


        public void UpdateExpense(Expense oldExpense, ExpenseDTO newExpense)
        {
            oldExpense.Name = newExpense.Name;
            oldExpense.UpdatedAt = DateTime.Now;
            Update(oldExpense);
        }
    }
}
