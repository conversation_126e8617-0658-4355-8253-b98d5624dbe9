﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Receipts;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ReceiptsController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("getAllReceipts")]
        public async Task<IActionResult> GetAllReceipts()
        {
            var receipts = maper.Map<IEnumerable<Receipt>, IEnumerable<ReceiptDTO>>(await unitOfWork.Receipt.GetAll(
                includeProperties: $"ToTreasury.User,ToTreasury.{nameof(Treasury)},{nameof(Client)},{nameof(Employee)},{nameof(Purchase)},{nameof(UserTreasury)}.{nameof(Treasury)},{nameof(UserTreasury)}.{nameof(Models.User)},{nameof(Expense)},{nameof(Financial)},{nameof(Sell)}.Receipts"));
            return Ok(receipts);
        }
        [HttpGet("getReceiptById/{id:Guid}")]
        public async Task<IActionResult> GetReceiptById([FromRoute] Guid id)
        {
            var receipt = maper.Map<Receipt?, ReceiptDTO?>(await unitOfWork.Receipt.GetFirstOrDefault(x => x.Id == id, $"ToTreasury.User,ToTreasury.{nameof(Treasury)},{nameof(Client)},{nameof(Employee)},{nameof(Purchase)},{nameof(UserTreasury)}.{nameof(Treasury)},{nameof(Expense)},{nameof(Financial)},{nameof(UserTreasury)}.{nameof(Models.User)},{nameof(Sell)}.Receipts"));
            return Ok(receipt);
        }
        [HttpPost("insertReceipt")]
        public async Task<IActionResult> InsertReceipt([FromBody] ReceiptDTO model)
        {

            await unitOfWork.Receipt.AddReceipt(maper.Map<ReceiptDTO, Receipt>(model));
            if (unitOfWork.UserTreasury.CheckUserTreasuryBelowZero()) return BadRequest(new ResponseVM() { State = false, Message = "القيمة غير موجودة" });
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);

        }
        [HttpPut("updateReceipt/{id:Guid}")]
        public async Task<IActionResult> UpdateReceipt([FromRoute] Guid id, ReceiptDTO model)
        {
            var oldreceipt = await unitOfWork.Receipt.GetByIdAsync(id);
            if (oldreceipt == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            await unitOfWork.Receipt.UpdateReceipt(oldreceipt, maper.Map<ReceiptDTO, Receipt>(model));
            if (unitOfWork.UserTreasury.CheckUserTreasuryBelowZero()) return BadRequest(new ResponseVM() { State = false, Message = "القيمة غير موجودة" });
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }
        [HttpDelete("deleteReceipt/{id:Guid}")]
        public async Task<IActionResult> DeleteReceipt([FromRoute] Guid id)
        {
            var oldreceipt = await unitOfWork.Receipt.GetByIdAsync(id);
            if (oldreceipt == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            await unitOfWork.Receipt.DeleteReceipt(oldreceipt);
            if (unitOfWork.UserTreasury.CheckUserTreasuryBelowZero()) return BadRequest(new ResponseVM() { State = false, Message = "القيمة غير موجودة" });
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }
    }
}
