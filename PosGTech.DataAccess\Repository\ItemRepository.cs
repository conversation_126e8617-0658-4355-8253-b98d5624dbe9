﻿using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.Units;

namespace PosGTech.DataAccess.Repository
{
    public class ItemRepository : Repository<Item>, IItemRepository
    {
        IItemNumRepository _itemNumRepository;
        IItemUnitRepository _itemUnitRepository;
        public ItemRepository(ApplicationDbContext db, IItemNumRepository itemNumRepository, IItemUnitRepository itemUnitRepository) : base(db)
        {
            _itemNumRepository = itemNumRepository;
            _itemUnitRepository = itemUnitRepository;
        }

        public void UpdateItem(Item oldeitem, ItemDTO newItem)
        {
            _itemNumRepository.RemoveRange(oldeitem.ItemNums.ToList());
            _itemNumRepository.AddRange(newItem.ItemNums.Select(x => new ItemNum() { Id = x.Id, ItemId = oldeitem.Id, Barcode = x.Barcode }).ToList());
            _itemUnitRepository.RemoveRange(oldeitem.ItemUnits.Where(x => !newItem.ItemUnits.Select(z => z.Id).Contains(x.Id)).ToList());
            _itemUnitRepository.AddRange(newItem.ItemUnits.Where(x => !oldeitem.ItemUnits.Select(z => z.Id).Contains(x.Id)).Select(x => new ItemUnit()
            {
                IsBigger = x.IsBigger,
                IsBasicUnit = x.IsBasicUnit,
                Quantity = x.Quantity,
                SalePrice = x.SalePrice,
                UnitId = x.Unit.Id,
                ItemId = oldeitem.Id
            }).ToList());
            oldeitem.ItemNums = newItem.ItemNums.Select(x => new ItemNum() { Id = x.Id, ItemId = oldeitem.Id, Barcode = x.Barcode }).ToList();
            oldeitem.ItemUnits = newItem.ItemUnits.Select(x => new ItemUnit()
            {
                Id = x.Id,
                IsBigger = x.IsBigger,
                IsBasicUnit = x.IsBasicUnit,
                Quantity = x.Quantity,
                SalePrice = x.SalePrice,
                UnitId = x.Unit.Id,
                ItemId = oldeitem.Id
            }).ToList();
            oldeitem.Name = newItem.Name;
            oldeitem.CategoryId = newItem.CategoryId;
            oldeitem.CostPrice = newItem.CostPrice;
            oldeitem.IsHaveExp = newItem.IsHaveExp;
            oldeitem.UpdatedAt = DateTime.Now;
            Update(oldeitem);
        }
        public async Task UpdateCost(IEnumerable<ItemForChangeQte> items)
        {
            foreach (var item in items.GroupBy(x => x.ItemId))
            {
                var oldItem = await GetFirstOrDefault(x => x.Id == item.First().ItemId, includeProperties: "StoreItems.StoreItemExps");
                var oldQte = oldItem.StoreItems?.Sum(x => x.StoreItemExps?.Sum(z => z.Quantity)) ?? 0;
                var newQte = item.Sum(z => z.Quantity);
                oldItem.CostPrice = ((oldItem.CostPrice * oldQte) + item.Sum(z => z.Quantity * z.CostPrice)) / (newQte + oldQte);
                Update(oldItem);
            }
        }

        public async Task<ItemDTO?> GetItemDTOById(Guid id)
        {
            //,ItemUnits.PurchaseItems,ItemUnits.ReturnSellItems,ItemUnits.SellItems,ItemUnits.TransferItems,ItemUnits.ConsumedItems,ItemUnits.ReturnPurchaseItems,ItemUnits.InventoryItems
            return (await Select(filter: x => x.Id == id, includeProperties: "ItemNums,ItemUnits.Unit"
            , select: x => new ItemDTO()
            {
                Id = x.Id,
                Name = x.Name,
                CategoryId = x.CategoryId,
                CostPrice = x.CostPrice,
                IsHaveExp = x.IsHaveExp,
                ItemNums = x.ItemNums.Select(z => new ItemNumDTO()
                {
                    Id = z.Id,
                    Barcode = z.Barcode
                }).ToList(),
                ItemUnits = x.ItemUnits.Select(z => new ItemUnitDTO()
                {
                    Id = z.Id,
                    IsBasicUnit = z.IsBasicUnit,
                    IsBigger = z.IsBigger,
                    Quantity = z.Quantity,
                    Unit = new UnitDTO()
                    {
                        Id = z.Unit.Id,
                        Name = z.Unit.Name
                    },
                    SalePrice = z.SalePrice,
                    IsAbleToDelete = z.IsBasicUnit ? z.PurchaseItems.Count == 0 && z.ReturnSellItems.Count == 0 && z.SellItems.Count == 0 && z.TransferItems.Count == 0 && z.ConsumedItems.Count == 0 && z.ReturnPurchaseItems.Count == 0 && z.InventoryItems.Count == 0 && x.InventoryItems.Count == 0 && x.PurchaseItems.Count == 0 : z.PurchaseItems.Count == 0 && z.ReturnSellItems.Count == 0 && z.SellItems.Count == 0 && z.TransferItems.Count == 0 && z.ConsumedItems.Count == 0 && z.ReturnPurchaseItems.Count == 0 && z.InventoryItems.Count == 0
                }).ToList()
            })
                ).FirstOrDefault();
        }
    }
}
