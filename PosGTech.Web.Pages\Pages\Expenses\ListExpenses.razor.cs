﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.ModelsDTO.Expenses;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Expenses
{
    public partial class ListExpenses
    {
        [Inject]
        IGRepository<ExpenseDTO> _Expense { get; set; }
        IEnumerable<ExpenseDTO> Expenses = new List<ExpenseDTO>();
        [Inject]
        IDialogService DialogService { get; set; }
        ExpenseDTO selectedItem = null;
        bool loading = true;
        string Search = "";
        string NameExpenseForDelete;
        MudMessageBox mbox { get; set; }

        protected override async Task OnInitializedAsync() => await LoadingData();
        private bool FilterFunc1(ExpenseDTO element) => FilterFunc(element, Search);

        private bool FilterFunc(ExpenseDTO element, string searchString)
        {
            if (string.IsNullOrWhiteSpace(searchString))
                return true;
            if (element.Name.Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            return false;
        }
        async void Upsert(Guid id)
        {
            var parameters = new DialogParameters<UpsertExpense>();
            parameters.Add(x => x.id, id);
            var options = new DialogOptions() { CloseButton = false, MaxWidth = MaxWidth.Medium, FullWidth = true };
            var result = await DialogService.Show<UpsertExpense>(id != Guid.Empty ? "تعديل مصروف" : "إضافة مصروف", parameters, options).Result;
            if ((bool?)result.Data == true) await LoadingData();
        }
        async void Delete(ExpenseDTO obj)
        {
            NameExpenseForDelete = obj.Name;
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                var response = await _Expense.Delete("Expenses/deleteExpense", obj.Id);
                if (response.State)
                {
                    _snackbar.Add(response.Message, Severity.Success);
                    await LoadingData();
                }
                else _snackbar.Add(response.Message, Severity.Error);
            }
        }
        async Task LoadingData()
        {
            loading = true;
            StateHasChanged();
            var res = await _Expense.GetAll("Expenses/getAllExpenses");
            if (res.response == null) Expenses = res.list;
            else _snackbar.Add(res.response.Message, Severity.Error);
            loading = false;
            StateHasChanged();
        }
    }
}