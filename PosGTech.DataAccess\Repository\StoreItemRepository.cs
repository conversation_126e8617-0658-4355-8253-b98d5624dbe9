﻿using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Items;

namespace PosGTech.DataAccess.Repository;

public class StoreItemRepository(ApplicationDbContext db, IItemUnitRepository _itemUnitRepository, IItemRepository _itemRepository, IStoreItemExpRepository _storeItemExp) : Repository<StoreItem>(db), IStoreItemRepository
{


    public async Task AddQuantity(IEnumerable<ItemForChangeQte> itemForChangeQte, Guid? storeId)
    {
        itemForChangeQte = await _itemUnitRepository.GetQteForBasicUnit(itemForChangeQte);
        await _itemRepository.UpdateCost(itemForChangeQte);
        var listItemId = itemForChangeQte.Select(z => z.ItemId);
        var allStoreItems = await GetAll(x => listItemId.Contains(x.ItemId) && x.StoreId == storeId, includeProperties: "StoreItemExps");
        foreach (var item in itemForChangeQte.GroupBy(x => x.ItemId))
        {
            var storeItem = allStoreItems.FirstOrDefault(x => x.ItemId == item.Key);
            if (storeItem != null)
            {

                foreach (var newItem in item)
                {
                    var StoreItemExp = storeItem.StoreItemExps.FirstOrDefault(x => x.Exp == newItem.Exp);
                    if (StoreItemExp is not null)
                    {
                        StoreItemExp.Quantity += newItem.Quantity;
                    }
                    else
                    {
                        _storeItemExp.Add(new() { StoreItemId = storeItem.Id, Exp = newItem.Exp, Quantity = newItem.Quantity });
                    }
                }
                Update(storeItem);
            }
            else
            {
                var newStoreItem = new StoreItem() { ItemId = item.Key.Value, StoreId = storeId.Value };
                foreach (var newItem in item)
                {
                    newStoreItem.StoreItemExps.Add(new() { Exp = newItem.Exp, Quantity = newItem.Quantity });
                }
                Add(newStoreItem);

            }
        }
    }
    public async Task UpdateReduceQuantity(IEnumerable<ItemForChangeQte> itemForChangeQte, Guid? storeId)
    {
        itemForChangeQte = await _itemUnitRepository.GetQteForBasicUnit(itemForChangeQte);
        await _itemRepository.UpdateCost(itemForChangeQte);
        var listItemId = itemForChangeQte.Select(z => z.ItemId);
        var allStoreItems = await GetAll(x => listItemId.Contains(x.ItemId) && x.StoreId == storeId, includeProperties: "StoreItemExps");
        foreach (var item in itemForChangeQte.GroupBy(x => x.ItemId))
        {
            var storeItem = allStoreItems.FirstOrDefault(x => x.ItemId == item.Key);
            foreach (var newItem in item)
            {
                var StoreItemExp = storeItem.StoreItemExps.FirstOrDefault(x => x.Exp == newItem.Exp);
                StoreItemExp.Quantity -= newItem.Quantity;
            }
            Update(storeItem);
        }
    }
}

