﻿@page "/upsertItem/{id:guid}"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<MudDialog Style="min-width: 80vw;">
    <TitleContent>
        <div class="d-flex align-center gap-2 pa-2">
            <MudIcon Icon="@Icons.Material.Filled.Inventory" Color="Color.Primary" />
            <MudText Typo="Typo.h6">إدارة الأصناف</MudText>

            <MudSpacer />
            <MudItem xs="4" sm="4" Style="margin-right:550px; margin-bottom: 1rem;">
            <div class="small-TextField">
                <MudAutocomplete T="ItemDTO"
                                 Value="_itemSelected"
                                 ValueChanged="ChangeItemName"
                                 Label="بحث الصنف"
                                 Dense="true"
                                 ResetValueOnEmptyText="true"
                                 CoerceText="false"
                                 CoerceValue="false"
                                 SearchFunc="@SearchItem"
                                 Variant="Variant.Outlined"
                                 AdornmentIcon="@Icons.Material.Filled.Search"
                                 Adornment="Adornment.End"
                                 ToStringFunc="@(e => e?.Name ?? string.Empty)" />
            </div>
            </MudItem>
        </div>
    </TitleContent>

    <DialogContent>
        @*  <div class="dialog-scroll"> *@
        <EditForm id="_form" Model="@_itemDTO" @ref="Form" @onkeydown="keydownForm">
            <DataAnnotationsValidator />
            <div class="small-TextField">
                <MudGrid>
                    <!-- Basic Information -->

                    <MudItem xs="12">
                        <!-- Item Section -->
                        <MudItem xs="12">
                            <MudDivider Class="my-0" />
                            <div class="d-flex align-center gap-2 mb-2">
                                <MudIcon Icon="@Icons.Material.Filled.Scale" Color="Color.Secondary" />
                                <MudText Typo="Typo.h6">الاصناف</MudText>
                            </div>
                        </MudItem>
                        <!-- Add a sticky container for the item name field -->
                        <MudContainer Style="position: sticky; top: 0; background-color: var(--mud-palette-surface); z-index: 1000;">
                            <MudGrid Spacing="4">
                                <MudItem xs="3" sm="3">
                                    <MudTextField T="string" @ref="refName"
                                                  @bind-Value="_itemDTO.Name"
                                                  Label="اسم الصنف"
                                                  OnKeyDown="KeyDownName"
                                                  AdornmentIcon="@Icons.Material.Filled.Label"
                                                  Adornment="Adornment.End"
                                                  Variant="Variant.Outlined"
                                                  For="@(() => _itemDTO.Name)" />
                                </MudItem>


                                <MudItem xs="3" sm="3">
                                                    @* OnKeyDown="KeyDownCategory" *@
                                    <MudSelect @bind-Value="_itemDTO.CategoryId"
                                               Label="التصنيف" @ref="refCategory"
                                               Variant="Variant.Outlined"
                                               Adornment="Adornment.End"
                                               AdornmentIcon="@Icons.Material.Filled.Category"
                                               For="@(() => _itemDTO.CategoryId)">
                                        <MudSelectItem Value="Guid.Empty">اختر التصنيف</MudSelectItem>
                                        @foreach (var category in _CategoryList)
                                        {
                                            <MudSelectItem Value="category.Id">@category.Name</MudSelectItem>
                                        }
                                    </MudSelect>
                                </MudItem>

                                @*           <MudItem xs="12" sm="6">
                                                <div class="small-radio-group">
                                                    <MudPaper Elevation="0" Class="pa-4 rounded-lg border-1">
                                                        <MudRadioGroup @bind-Value="_itemDTO.IsHaveExp">
                                                            <MudRadio Value="true" Color="Color.Success">
                                                                <div class="d-flex gap-2 align-center">
                                                                    <MudIcon Icon="@Icons.Material.Filled.EventAvailable" Size="Size.Small" />
                                                                    <span>لديه تاريخ صلاحية</span>
                                                                </div>
                                                            </MudRadio>
                                                            <MudRadio Value="false" Color="Color.Error">
                                                                <div class="d-flex gap-2 align-center">
                                                                    <MudIcon Icon="@Icons.Material.Filled.EventBusy" Size="Size.Small" />
                                                                    <span>لا يوجد تاريخ صلاحية</span>
                                                                </div>
                                                            </MudRadio>
                                                        </MudRadioGroup>
                                                    </MudPaper>
                                                </div>
                                            </MudItem> *@

                                <MudItem xs="4" sm="4">
                                    <div class="small-radio-group">
                                        <MudPaper Elevation="0" Class="pa-1 rounded-lg border-1">
                                            <MudRadioGroup @bind-Value="_itemDTO.IsHaveExp">
                                                <MudRadio Value="true" Color="Color.Success">

                                                    <MudIcon Icon="@Icons.Material.Filled.EventAvailable" Size="Size.Small" />
                                                    <span>لديه تاريخ صلاحية</span>

                                                </MudRadio>
                                                <MudRadio Value="false" Color="Color.Error">

                                                    <MudIcon Icon="@Icons.Material.Filled.EventBusy" Size="Size.Small" />
                                                    <span>لا يوجد تاريخ صلاحية</span>

                                                </MudRadio>
                                            </MudRadioGroup>
                                        </MudPaper>
                                    </div>
                                </MudItem>

                            </MudGrid>

                        </MudContainer>


                        <MudTabs @ref="tabs" Elevation="2" Rounded="true" ApplyEffectsToContainer="true" Color="Color.Primary" Class="mt-4">
                            <MudTabPanel Text="وحدات البيع" Icon="@Icons.Material.Filled.Scale">




                                <!-- Units Section -->
                                <MudItem xs="12">
                                    <MudDivider Class="my-6" />
                                    <div class="d-flex align-center gap-2 mb-4">
                                        <MudIcon Icon="@Icons.Material.Filled.Scale" Color="Color.Secondary" />
                                        <MudText Typo="Typo.h6">الوحدات والأسعار</MudText>
                                    </div>
                                </MudItem>

                                <MudGrid>
                                    <MudItem xs="4" sm="5">
                                        <MudSelect T="UnitDTO"
                                                   @bind-Value="ItemUnitSelected.Unit"
                                                   @ref="refUnit"
                                                   OnKeyDown="KeyDownUnit"
                                                   Label="الوحدة"
                                                   Variant="Variant.Outlined"
                                                   Adornment="Adornment.End"
                                                   AdornmentIcon="@Icons.Material.Filled.Scale" For="@(() => ItemUnitSelected.Unit)">

                                            <MudSelectItem T="UnitDTO" Value="null">اختر الوحدة</MudSelectItem>
                                            @foreach (var unit in _UnitList)
                                            {
                                                <MudSelectItem T="UnitDTO" Value="unit">@unit.Name</MudSelectItem>
                                            }

                                        </MudSelect>

                                    </MudItem>
                                    @if (ItemUnitSelected.Unit != null)
                                    {
                          
                                        <MudItem xs="4" sm="6">
                                            <div class="small-radio-group">
                                                <MudPaper Elevation="0" Class="pa-1 rounded-lg border-1">
                                                    <MudRadioGroup T="bool" Value="ItemUnitSelected.IsBasicUnit" ValueChanged="ChangeIsBasicUnit" For="@(() => ItemUnitSelected.IsBasicUnit)">
                                                        <MudRadio Value="true" Color="Color.Primary">

                                                            <MudIcon Icon="@Icons.Material.Filled.Check" Size="Size.Small" />
                                                            <span>الوحدة الأساسية</span>

                                                        </MudRadio>
                                                        <MudRadio Value="false" Color="Color.Secondary">

                                                            <MudIcon Icon="@Icons.Material.Filled.SubdirectoryArrowRight" Size="Size.Small" />
                                                            <span>الوحدة الفرعية</span>

                                                        </MudRadio>
                                                    </MudRadioGroup>
                                                </MudPaper>
                                            </div>
                                        </MudItem>
                                    }
                                </MudGrid>

                                @if (ItemUnitSelected.Unit != null)
                                {
                                    <MudGrid>
                                        @if (ItemUnitSelected.IsBasicUnit == false)
                                        {
                                            <MudItem xs="3" sm="3">
                                                <MudNumericField T="decimal"
                                                                 Value="ItemUnitSelected.Quantity"
                                                                 @ref="refQuantity"
                                                                 OnKeyDown="KeyDownQuantity"
                                                                 Disabled="ItemUnitSelected.IsBasicUnit"
                                                                 Label="الكمية من الوحدة الأساسية"
                                                                 Variant="Variant.Outlined"
                                                                 Adornment="Adornment.End"
                                                                 AdornmentIcon="@Icons.Material.Filled.Numbers"
                                                                 For="@(() => ItemUnitSelected.Quantity)" ValueChanged="ChangeQte" />
                                            </MudItem>
                                        }
                                        <MudItem xs="3" sm="3">
                                            <MudNumericField T="decimal"
                                                             @bind-Value="ItemUnitSelected.SalePrice"
                                                             Label="سعر البيع"
                                                             @ref="refSalePrice"
                                                             Variant="Variant.Outlined"
                                                             Adornment="Adornment.End"
                                                             AdornmentIcon="@Icons.Material.Filled.PriceCheck"
                                                             For="@(() => ItemUnitSelected.SalePrice)" />
                                        </MudItem>
                                        <MudSpacer />
                                        <MudItem Style="margin-left:50px; margin-bottom:1rem;" xs="3" sm="3">
                                            <MudNumericField @bind-Value="_itemDTO.CostPrice"
                                                             Disabled="true"
                                                             Label="سعر التكلفة"
                                                             Variant="Variant.Outlined"
                                                             Adornment="Adornment.End"
                                                             AdornmentIcon="@Icons.Material.Filled.MonetizationOn"
                                                             For="@(() => _itemDTO.CostPrice)" />
                                        </MudItem>
                                    </MudGrid>



                                    <MudGrid>
                                        @if (ItemUnitSelected.IsBasicUnit == false)
                                        {
                                            <MudItem xs="12" sm="6">
                                                <div class="small-radio-group">
                                                    <MudPaper Elevation="0" Class="pa-1 rounded-lg border-1">
                                                        <MudRadioGroup T="bool" Value="ItemUnitSelected.IsBigger" ValueChanged="ChangeIsBigger" For="@(() => ItemUnitSelected.IsBigger)">
                                                            <MudRadio Value="false" Color="Color.Primary">
                                                                <div class="d-flex gap-2 align-center">
                                                                    <MudIcon Icon="@Icons.Material.Filled.ArrowDownward" Size="Size.Small" />
                                                                    <span>أصغر من الوحدة الأساسية</span>
                                                                </div>
                                                            </MudRadio>
                                                            <MudRadio Value="true" Color="Color.Secondary">
                                                                <div class="d-flex gap-2 align-center">
                                                                    <MudIcon Icon="@Icons.Material.Filled.ArrowUpward" Size="Size.Small" />
                                                                    <span>أكبر من الوحدة الأساسية</span>
                                                                </div>
                                                            </MudRadio>
                                                        </MudRadioGroup>
                                                    </MudPaper>
                                                </div>
                                            </MudItem>
                                        }
                                    </MudGrid>

                                    <MudItem xs="12">
                                        <MudButton Variant="Variant.Filled"
                                                   Color="Color.Secondary"
                                                   StartIcon="@Icons.Material.Filled.Add"
                                                   OnClick="AddItemUnit"
                                                   Class="mt-2">
                                            إضافة وحدة
                                        </MudButton>
                                    </MudItem>
                                }

                                <MudTable Items="@_itemDTO.ItemUnits"
                                          Dense="true"
                                          Hover="true"
                                          Bordered="true"
                                          Striped="true"
                                          Elevation="0">
                                    <HeaderContent>
                                        <MudTh Style="width:50px">#</MudTh>
                                        <MudTh>اسم صنف</MudTh>
                                        <MudTh>الوحدة</MudTh>
                                        <MudTh>نوع الوحدة</MudTh>
                                        <MudTh>الكمية</MudTh>
                                        <MudTh>سعر البيع</MudTh>
                                        <MudTh Style="width:70px"></MudTh>
                                    </HeaderContent>
                                    <RowTemplate Context="ItemUnits">
                                        <MudTd>@(_itemDTO.ItemUnits?.ToList().IndexOf(ItemUnits) + 1)</MudTd>
                                        <MudTd>@_itemDTO.Name</MudTd>
                                        <MudTd>@ItemUnits.Unit?.Name</MudTd>
                                        <MudTd>@(ItemUnits.IsBasicUnit ? "وحدة أساسية" : "وحدة فرعية")</MudTd>
                                        <MudTd>@ItemUnits.Quantity</MudTd>
                                        <MudTd>@ItemUnits.SalePrice</MudTd>
                                        <MudTd>
                                            <MudIconButton Size="Size.Small"
                                                           Color="Color.Info"
                                                           Icon="@Icons.Material.Filled.Edit"
                                                           OnClick="@(() => EditItemUnit(ItemUnits))" Disabled="!ItemUnits.IsAbleToDelete" />
                                        </MudTd>
                                        <MudTd>
                                            <MudIconButton Size="Size.Small"
                                                           Color="Color.Error"
                                                           Icon="@Icons.Material.Filled.Delete"
                                                           OnClick="@(() => DeletItemUnit(ItemUnits))" Disabled="!ItemUnits.IsAbleToDelete" />
                                        </MudTd>
                                    </RowTemplate>
                                </MudTable>

                            </MudTabPanel>
                            <MudTabPanel Text="الباركود" Icon="@Icons.Material.Filled.QrCode2">
                                <!-- Barcode Section -->
                                <MudItem xs="12">
                                    <MudDivider Class="my-6" />
                                    <div class="d-flex align-center gap-2 mb-4">
                                        <MudIcon Icon="@Icons.Material.Filled.QrCode2" Color="Color.Default" />
                                        <MudText Typo="Typo.h6">الباركود</MudText>
                                    </div>

                                    <div class="d-flex gap-4 align-center">
                                        <MudTextField Style="width:450px" T="string"
                                                      @bind-Value="ItemNumSelected.Barcode"
                                                      Label="الباركود"
                                                      Variant="Variant.Outlined"
                                                      Class="flex-grow-1"
                                                      Adornment="Adornment.End"
                                                      AdornmentIcon="@Icons.Material.Filled.QrCode2" />
                                        <MudButton Variant="Variant.Filled"
                                                   Color="Color.Secondary"
                                                   StartIcon="@Icons.Material.Filled.Add"
                                                   OnClick="AddItemNum">
                                            إضافة باركود
                                        </MudButton>
                                    </div>
                                </MudItem>
                                <MudTable Items="@_itemDTO.ItemNums"
                                          Dense="true"
                                          Hover="true"
                                          Bordered="true"
                                          Striped="true"
                                          Elevation="0">
                                    <HeaderContent>
                                        <MudTh Style="width:50px">#</MudTh>
                                        <MudTh>الباركود</MudTh>
                                        <MudTh></MudTh>
                                    </HeaderContent>

                                    <RowTemplate Context="itemNums">
                                        <MudTd>@(_itemDTO.ItemNums?.ToList().IndexOf(itemNums) + 1)</MudTd>
                                        <MudTd>@itemNums.Barcode</MudTd>
                                        <MudTd>
                                            <MudIconButton Size="Size.Small"
                                                           Color="Color.Error"
                                                           Icon="@Icons.Material.Filled.Delete"
                                                           OnClick="@(() => DeletItemNum(itemNums))" />
                                        </MudTd>
                                    </RowTemplate>
                                </MudTable>
                            </MudTabPanel>
                            <!-- Data Tables -->




                        </MudTabs>
                        <div class="pa-4 d-flex gap-4">
                            <MudButton Variant="Variant.Filled"
                                       OnClick="@Cancel"
                                       Color="Color.Error"
                                       StartIcon="@Icons.Material.Filled.Close"
                                       Size="Size.Large"
                                       Class="px-8">
                                إلغاء
                            </MudButton>
                            <MudButton Variant="Variant.Filled"
                                       form="_form"
                                       OnClick="@Upsert"
                                       Color="Color.Success"
                                       StartIcon="@Icons.Material.Filled.Save"
                                       Size="Size.Large"
                                       Class="px-8">
                                حفظ F2
                            </MudButton>
                            <MudButton Variant="Variant.Filled"
                                       form="_form"
                                       OnClick="@NewItem"
                                       Color="Color.Success"
                                       StartIcon="@Icons.Material.Filled.Add"
                                       Size="Size.Large"
                                       Class="px-8">
                                جديد F8
                            </MudButton>
                        </div>
                    </MudItem>
                </MudGrid>
            </div>


        </EditForm>
        @* </div> *@
    </DialogContent>


</MudDialog>

<style>
    .dialog-scroll {
        overflow-y: auto;
        max-height: 85vh;
        padding: 16px;
    }

    .mud-input-control {
        margin-bottom: 0;
    }

    .mud-card {
        transition: all 0.3s ease;
    }

        .mud-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--mud-elevation-5);
        }

    .mud-button {
        transition: transform 0.2s ease;
    }

        .mud-button:hover {
            transform: translateY(-1px);
        }

</style>


<style>
    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }
</style>