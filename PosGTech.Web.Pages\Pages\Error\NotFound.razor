@page "/error/404"
@page "/not-found"
@using MudBlazor
@inject NavigationManager Navigation
@inject ISnackbar Snackbar

<PageTitle>الصفحة غير موجودة - 404</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium" Class="mt-8">
    <MudPaper Elevation="3" Class="pa-8">
        <div dir="rtl" class="text-center">
            <!-- Error Icon -->
            <MudIcon Icon="@Icons.Material.Filled.SearchOff" 
                     Color="Color.Warning" 
                     Size="Size.Large" 
                     Style="font-size: 6rem;" 
                     Class="mb-4" />
            
            <!-- Error Code -->
            <MudText Typo="Typo.h2" Color="Color.Warning" Class="mb-2">
                404
            </MudText>
            
            <!-- Main Error Message -->
            <MudText Typo="Typo.h4" Class="mb-4">
                الصفحة غير موجودة
            </MudText>
            
            <!-- Description -->
            <MudText Typo="Typo.body1" Color="Color.Secondary" Class="mb-6">
                عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.
                يرجى التحقق من الرابط أو العودة إلى الصفحة الرئيسية.
            </MudText>
            
            <!-- Current URL Display -->
            @if (!string.IsNullOrEmpty(RequestedUrl))
            {
                <MudAlert Severity="Severity.Info" Class="mb-4" dir="ltr">
                    <strong>الرابط المطلوب:</strong> @RequestedUrl
                </MudAlert>
            }
            
            <!-- Action Buttons -->
            <div class="d-flex justify-center flex-wrap gap-3">
                <MudButton Variant="Variant.Filled" 
                           Color="Color.Primary" 
                           StartIcon="@Icons.Material.Filled.Home"
                           OnClick="NavigateToHome"
                           Size="Size.Large">
                    الصفحة الرئيسية
                </MudButton>
                
                <MudButton Variant="Variant.Outlined" 
                           Color="Color.Secondary" 
                           StartIcon="@Icons.Material.Filled.ArrowBack"
                           OnClick="GoBack"
                           Size="Size.Large">
                    العودة للخلف
                </MudButton>
                
                <MudButton Variant="Variant.Text" 
                           Color="Color.Info" 
                           StartIcon="@Icons.Material.Filled.Refresh"
                           OnClick="RefreshPage"
                           Size="Size.Large">
                    إعادة تحميل
                </MudButton>
            </div>
            
            <!-- Help Section -->
            <MudDivider Class="my-6" />
            
            <MudText Typo="Typo.h6" Class="mb-3">
                هل تحتاج مساعدة؟
            </MudText>
            
            <div class="d-flex justify-center flex-wrap gap-2">
                <MudChip T="bool" Icon="@Icons.Material.Filled.Search"
                         Color="Color.Info" 
                         Variant="Variant.Outlined"
                         OnClick="ShowSearchSuggestions">
                    البحث في الموقع
                </MudChip>
                
                <MudChip T="bool" Icon="@Icons.Material.Filled.ContactSupport"
                         Color="Color.Success" 
                         Variant="Variant.Outlined"
                         OnClick="ContactSupport">
                    الاتصال بالدعم
                </MudChip>
                
                <MudChip T="bool" Icon="@Icons.Material.Filled.BugReport"
                         Color="Color.Warning" 
                         Variant="Variant.Outlined"
                         OnClick="ReportIssue">
                    الإبلاغ عن مشكلة
                </MudChip>
            </div>
        </div>
    </MudPaper>
</MudContainer>

@code {
    [Parameter] public string? RequestedUrl { get; set; }
    
    protected override void OnInitialized()
    {
        // Get the current URL if not provided as parameter
        if (string.IsNullOrEmpty(RequestedUrl))
        {
            RequestedUrl = Navigation.Uri;
        }
    }
    
    private void NavigateToHome()
    {
        try
        {
            Navigation.NavigateTo("/", forceLoad: true);
        }
        catch (Exception ex)
        {
            Snackbar.Add("حدث خطأ أثناء الانتقال إلى الصفحة الرئيسية", Severity.Error);
            Console.WriteLine($"Navigation error: {ex.Message}");
        }
    }
    
    private void GoBack()
    {
        try
        {
            // Try to go back in browser history
            Navigation.NavigateTo("javascript:history.back()", forceLoad: true);
        }
        catch (Exception ex)
        {
            // Fallback to home page
            NavigateToHome();
        }
    }
    
    private void RefreshPage()
    {
        try
        {
            Navigation.NavigateTo(Navigation.Uri, forceLoad: true);
        }
        catch (Exception ex)
        {
            Snackbar.Add("حدث خطأ أثناء إعادة تحميل الصفحة", Severity.Error);
        }
    }
    
    private void ShowSearchSuggestions()
    {
        Snackbar.Add("ميزة البحث ستكون متاحة قريباً", Severity.Info);
    }
    
    private void ContactSupport()
    {
        Snackbar.Add("يرجى الاتصال بالدعم الفني على الرقم: ************", Severity.Info, config =>
        {
            config.VisibleStateDuration = 8000;
        });
    }
    
    private void ReportIssue()
    {
        Snackbar.Add("شكراً لك على الإبلاغ. سيتم التحقق من المشكلة قريباً.", Severity.Success);
    }
}
