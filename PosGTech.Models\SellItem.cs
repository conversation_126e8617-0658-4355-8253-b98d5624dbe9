﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول المواد المستهكلة لكل فاتورة
    /// </summary>
    public class SellItem : BaseEntity
    {
        public Guid? SellId { get; set; }
        public Sell? Sell { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal Quantity { get; set; } = 1;
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal CostPrice { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal SalePrice { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal SalePriceAfterDiscount { get; set; } = decimal.Zero;
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid StoreItemExpId { get; set; }
        public StoreItemExp? StoreItemExp { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid ItemUnitId { get; set; }
        public ItemUnit? ItemUnit { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal ReturnQuantity { get; set; } = 0;
    }
}
