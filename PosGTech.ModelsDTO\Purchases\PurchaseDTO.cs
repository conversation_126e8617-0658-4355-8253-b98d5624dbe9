﻿using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.Items;
using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Purchases;

public class PurchaseDTO
{
    public Guid Id { get; set; }
    public int InvoiceNo { get; set; } = 0;
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public DateOnly Date { get; set; }
    public decimal Paid { get; set; } = decimal.Zero;
    public decimal DiscountValue { get; set; } = decimal.Zero;
    public bool IsDiscountValue { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
    public decimal FinalTotal { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
    public decimal Total { get; set; }
    public string? Note { get; set; }
    public Guid? TreasuryId { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public ClientCMDTO? Client { get; set; }
    [Required(ErrorMessage = "هذا الحقل مطلوب")]
    public Guid? StoreId { get; set; }
    public List<PurchaseItemDTO>? PurchaseItemDTOs { get; set; } = new List<PurchaseItemDTO>();
    public List<ItemForChangeQte>? SalePriceChanges { get; set; } = new List<ItemForChangeQte>();
}
