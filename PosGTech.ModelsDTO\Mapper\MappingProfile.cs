﻿using AutoMapper;
using PosGTech.Models;
using PosGTech.ModelsDTO;
using PosGTech.ModelsDTO.Categories;
using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.Consumeds;
using PosGTech.ModelsDTO.Employees;
using PosGTech.ModelsDTO.Expenses;
using PosGTech.ModelsDTO.Finacnial;
using PosGTech.ModelsDTO.Inventories;
using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.Purchases;
using PosGTech.ModelsDTO.Receipts;
using PosGTech.ModelsDTO.Sells;
using PosGTech.ModelsDTO.StoreItem;
using PosGTech.ModelsDTO.StoreItemExp;
using PosGTech.ModelsDTO.Stores;
using PosGTech.ModelsDTO.ShopSettings;
using PosGTech.ModelsDTO.Treasury;
using PosGTech.ModelsDTO.Units;
using PosGTech.ModelsDTO.Users;

namespace PosGTech.Mapper
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            //------------------------------------------------------------------------------------------
            CreateMap<Item, ItemDTO>()
                .ForMember(dest => dest.Quantity, opt => opt.MapFrom(src =>
                    src.StoreItems != null ?
                    src.StoreItems.SelectMany(si => si.StoreItemExps ?? new List<StoreItemExp>()).Sum(sie => sie.Quantity) :
                    0m))
                .ReverseMap().ForMember(x => x.Id, y => y.Ignore());
            CreateMap<Item, ItemCMDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());
            CreateMap<ItemNum, ItemNumDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());
            CreateMap<ItemUnit, ItemUnitDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore()).ForMember(x => x.Unit, y => y.Ignore());
            //------------------------------------------------------------------------------------------
            CreateMap<Unit, UnitDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());

            //------------------------------------------------------------------------------------------
            CreateMap<Category, CategoryDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());

            //------------------------------------------------------------------------------------------
            CreateMap<Expense, ExpenseDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());

            //------------------------------------------------------------------------------------------
            CreateMap<Client, ClientDTO>().ReverseMap();
            CreateMap<Client, ClientCMDTO>().ReverseMap();
            //------------------------------------------------------------------------------------------
            CreateMap<Financial, FinancialDTO>().ReverseMap();
            //------------------------------------------------------------------------------------------
            CreateMap<User, UserCMDTO>().ReverseMap();

            //------------------------------------------------------------------------------------------
            CreateMap<Treasury, TreasuryDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());
            CreateMap<Treasury, TreasuryCMDTO>().ReverseMap();
            CreateMap<UserTreasury, UserTreasuryCMDTO>().ReverseMap();
            //------------------------------------------------------------------------------------------
            CreateMap<Employee, EmployeeDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());
            CreateMap<Employee, EmployeeCMDTO>().ReverseMap();
            //------------------------------------------------------------------------------------------

            CreateMap<ConsumedItem, ConsumedItemDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());
            CreateMap<Consumed, ConsumedDTO>().ForMember(x => x.ConsumedItemDTOs, y => y.MapFrom(z => z.ConsumedItems)).ReverseMap().ForMember(x => x.ConsumedItems, y => y.MapFrom(z => z.ConsumedItemDTOs)).ForMember(x => x.Id, y => y.Ignore());

            //------------------------------------------------------------------------------------------
            CreateMap<PurchaseItem, PurchaseItemDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore())
                .ForMember(x => x.Item, y => y.Ignore())
                .ForMember(x => x.ItemUnit, y => y.Ignore());
            CreateMap<Purchase, PurchaseDTO>()
                .ForMember(x => x.PurchaseItemDTOs, y => y.MapFrom(z => z.PurchaseItems))
                 .ForMember(dest => dest.Paid, opt => opt.MapFrom(src => src.Receipts != null ? src.Receipts.Sum(r => r.Value) : 0m))
                 .ReverseMap().ForMember(x => x.PurchaseItems, y => y.MapFrom(z => z.PurchaseItemDTOs))
                .ForMember(x => x.Id, y => y.Ignore())
                 .ForMember(x => x.Client, y => y.Ignore());
            CreateMap<Purchase, PurchaseReceiptDTO>()
                          .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                          .ForMember(dest => dest.InvoiceNo, opt => opt.MapFrom(src => src.InvoiceNo))
                          .ForMember(dest => dest.FinalTotal, opt => opt.MapFrom(src => src.FinalTotal))
                          .ForMember(dest => dest.Client, opt => opt.MapFrom(src => src.Client))
                          .ForMember(dest => dest.Paid, opt => opt.MapFrom(src => src.Receipts != null ? src.Receipts.Sum(r => r.Value) : 0m));

            //------------------------------------------------------------------------------------------
            CreateMap<Receipt, ReceiptDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore())
            .ForMember(x => x.ToTreasury, y => y.Ignore())
            .ForMember(x => x.UserTreasury, y => y.Ignore())
            .ForMember(x => x.Client, y => y.Ignore())
            .ForMember(x => x.Expense, y => y.Ignore())
            .ForMember(x => x.Purchase, y => y.Ignore())
            .ForMember(x => x.Sell, y => y.Ignore())
            .ForMember(x => x.Employee, y => y.Ignore());
            //------------------------------------------------------------------------------------------
            CreateMap<Store, StoreDTO>().ReverseMap();
            CreateMap<Store, StoreCMDTO>().ReverseMap();
            CreateMap<Store, StoreSettingsDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());
            //------------------------------------------------------------------------------------------
            CreateMap<ShopSettings, ShopSettingsDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore());
            CreateMap<StoreItemExp, StoreItemExpDTO>().ReverseMap();
            CreateMap<StoreItem, StoreItemDTO>().ReverseMap();
            CreateMap<StoreItem, StoreItemForSellDTO>().ReverseMap();
            //------------------------------------------------------------------------------------------
            CreateMap<Sell, SellDTO>().ForMember(x => x.SellItemDTOs, y => y.MapFrom(z => z.SellItems))
                .ForMember(dest => dest.Paid, opt => opt.MapFrom(src => src.Receipts != null ? src.Receipts.Sum(r => r.Value) : 0m)).ReverseMap()
                  .ForMember(x => x.SellItems, y => y.MapFrom(z => z.SellItemDTOs))
                .ForMember(x => x.Client, y => y.Ignore())
                .ForMember(x => x.Id, y => y.Ignore());
            CreateMap<SellItem, SellItemDTO>().ReverseMap()
                .ForMember(x => x.StoreItemExp, y => y.Ignore())
                .ForMember(x => x.ItemUnit, y => y.Ignore())
                .ForMember(x => x.Id, y => y.Ignore());
            CreateMap<Sell, SellReceiptDTO>()
                          .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                          .ForMember(dest => dest.InvoiceNo, opt => opt.MapFrom(src => src.InvoiceNo))
                          .ForMember(dest => dest.FinalTotal, opt => opt.MapFrom(src => src.FinalTotal))
                          .ForMember(dest => dest.Client, opt => opt.MapFrom(src => src.Client))
                          .ForMember(dest => dest.Paid, opt => opt.MapFrom(src => src.Receipts != null ? src.Receipts.Sum(r => r.Value) : 0m));

            CreateMap<Sell, SellsNumDTO>().ReverseMap();
            //------------------------------------------------------------------------------------------

            CreateMap<InventoryItem, InventoryItemDTO>().ReverseMap().ForMember(x => x.Id, y => y.Ignore())
            .ForMember(x => x.Item, y => y.Ignore())
            .ForMember(x => x.ItemUnit, y => y.Ignore());
            CreateMap<Inventory, InventoryDTO>()
                .ForMember(x => x.InventoryItemDTOs, y => y.MapFrom(z => z.InventoryItems))
                .ReverseMap().ForMember(x => x.InventoryItems, y => y.MapFrom(z => z.InventoryItemDTOs))
                .ForMember(x => x.Id, y => y.Ignore());
            //------------------------------------------------------------------------------------------
            CreateMap<Purchase, PurchasesNumDTO>().ReverseMap();


        }
    }
}
