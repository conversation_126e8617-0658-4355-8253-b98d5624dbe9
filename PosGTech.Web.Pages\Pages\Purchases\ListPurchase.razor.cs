﻿using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using MudBlazor;
using PosGTech.ModelsDTO.Purchases;
using PosGTech.ModelsDTO.ReportFileDto;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Purchases
{
    public partial class ListPurchase
    {
        [Inject]
        IDialogService DialogService { get; set; }

        [Inject]
        IGRepository<PurchaseDTO> _purchase { get; set; }
        [Inject]
        IGRepository<Reportfile> _ReportServer { get; set; }
        IEnumerable<PurchaseDTO> purchases = new List<PurchaseDTO>();
        PurchaseDTO selectedItem = null;
        Reportfile _Reportfile = new();

        bool loading = true;
        string Search = "";
        string NamePurchaseForDelete;
        MudMessageBox mbox { get; set; }
        private List<BreadcrumbItem> _items = new List<BreadcrumbItem>
        {
         new BreadcrumbItem("الرئيسية", href:"", icon: Icons.Material.Filled.Home),
        new BreadcrumbItem("الفواتير الشراء", href: null, disabled: true, icon: Icons.Material.Filled.AccountBalance)
        };

        protected override async Task OnInitializedAsync()
        {
            await LoadingData();
        }
        private bool FilterFunc1(PurchaseDTO element) => FilterFunc(element, Search);

        private bool FilterFunc(PurchaseDTO element, string searchString)
        {
            if (string.IsNullOrWhiteSpace(searchString))
                return true;
            if (element.InvoiceNo.ToString().Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            if (element.Date.ToString().Contains(searchString, StringComparison.OrdinalIgnoreCase))
                return true;
            return false;
        }
        async void Upsert(Guid id)
        {
            var parameters = new DialogParameters<UpsertPurchase>();
            parameters.Add(x => x.id, id);
            var options = new DialogOptions() { CloseButton = false, MaxWidth = MaxWidth.Medium, FullWidth = true };
            var result = await DialogService.Show<UpsertPurchase>("", parameters, options).Result;

            //   _navigation.NavigateTo($"/upsertPurchase/{id}");
        }
        async void PrintReport(Guid id)
        {

            var res = await _ReportServer.GetByIdAsync($"Report/ReportPurchase", id);
            if (res.response == null)
            {
                _Reportfile = res.model;

                await JS.InvokeVoidAsync(
                  "downloadFromByteArray",
            new
            {
                ContentType = _Reportfile.ContentType,
                ByteArray = _Reportfile.filestreem,
                FileName = _Reportfile.FileName,

            });

            }
            else
            {
                //_snackbar.Add("خطأ في الاتصال", Severity.Error);
                _navigation.NavigateTo("/listPurchases");
            }
        }
        async void Delete(PurchaseDTO obj)
        {
            NamePurchaseForDelete = obj.InvoiceNo.ToString();
            bool? result = await mbox.ShowAsync();
            if (result == true)
            {
                var response = await _purchase.Delete("Purchases/deletePurchase", obj.Id);
                if (response.State)
                {
                    //_snackbar.Add(response.Message, Severity.Success);
                    await LoadingData();
                }
                //else _snackbar.Add(response.Message, Severity.Error);
            }
        }
        async Task LoadingData()
        {
            loading = true;
            StateHasChanged();
            var res = await _purchase.GetAll("Purchases/getAllPurchases");
            if (res.response == null) purchases = res.list;
            //else _snackbar.Add(res.response.Message, Severity.Error);
            loading = false;
            StateHasChanged();
        }
    }
}