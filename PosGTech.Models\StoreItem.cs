﻿namespace PosGTech.Models
{
    public class StoreItem : BaseEntity
    {
        public Guid ItemId { get; set; }
        public Item? Item { get; set; }
        public Guid StoreId { get; set; }
        public Store? Store { get; set; }
        public ICollection<StoreItemExp>? StoreItemExps { get; set; } = new HashSet<StoreItemExp>();
        public ICollection<TransferItem>? TransferItems { get; set; }
    }
}
