﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Employees;
using PosGTech.Web.Pages.Pages.Receipts;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Employees;

public partial class UpsertEmployee
{
    [Inject]
    IDialogService DialogService { get; set; }
    [CascadingParameter] MudDialogInstance MudDialog { get; set; }
    [Parameter]
    public Guid id { get; set; }
    EmployeeDTO employee = new();
    bool isEdit = false;
    [Inject]
    IGRepository<EmployeeDTO> _employee { get; set; }
    protected override async Task OnInitializedAsync()
    {
        if (id != Guid.Empty) await LoadingData();
    }
    async void Upsert()
    {
        ResponseVM response;
        if (id == Guid.Empty)
            response = await _employee.Insert("Employees/insertEmployee", employee);
        else
            response = await _employee.Update("Employees/updateEmployee", employee, id);

        if (response.State)
        {
            _snackbar.Add(response.Message, Severity.Success);
            MudDialog.Close(DialogResult.Ok(true));
        }
        else
        {
            _snackbar.Add(response.Message, Severity.Error);
        }
    }
    void Cancel()
    {
        if (isEdit) MudDialog.Close(DialogResult.Ok(true));
        else MudDialog.Cancel();
    }
    async void AddReceipt(Guid financialId)
    {
        var parameters = new DialogParameters<UpsertReceipt>();
        parameters.Add(x => x.id, Guid.Empty);
        parameters.Add(x => x.financialId, financialId);
        parameters.Add(x => x.Employee, new() { Id = id, Name = employee.Name });
        var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.Medium, FullWidth = true };
        var result = await DialogService.Show<UpsertReceipt>("إضافة الإيصال", parameters, options).Result;
        if ((bool?)result.Data == true)
        {
            await LoadingData();
            isEdit = true;
        }
    }
    async Task LoadingData()
    {
        var res = await _employee.GetByIdAsync("Employees/getEmployeeById", id);
        if (res.response == null)
        {
            employee = res.model;
            StateHasChanged();
        }
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            MudDialog.Cancel();
        }
    }
}