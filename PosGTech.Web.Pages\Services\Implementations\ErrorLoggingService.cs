using Blazored.LocalStorage;
using Microsoft.Extensions.Logging;
using PosGTech.Web.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// Implementation of error logging service using local storage
    /// </summary>
    public class ErrorLoggingService : IErrorLoggingService
    {
        private readonly ILocalStorageService _localStorage;
        private readonly ILogger<ErrorLoggingService> _logger;
        private const string ErrorLogsKey = "error_logs";
        private const int MaxErrorLogs = 100; // Maximum number of errors to keep in storage

        public ErrorLoggingService(ILocalStorageService localStorage, ILogger<ErrorLoggingService> logger)
        {
            _localStorage = localStorage;
            _logger = logger;
        }

        public async Task LogErrorAsync(ErrorInfo errorInfo)
        {
            try
            {
                var existingLogs = await GetErrorLogsFromStorageAsync();

                // Add new error to the beginning of the list
                existingLogs.Insert(0, errorInfo);

                // Keep only the most recent errors
                if (existingLogs.Count > MaxErrorLogs)
                {
                    existingLogs = existingLogs.Take(MaxErrorLogs).ToList();
                }

                await _localStorage.SetItemAsync(ErrorLogsKey, existingLogs);

                // Also log to ErrorLog.txt file for user's preference
                await LogToFileAsync(errorInfo);

                // Also log to console/standard logger
                _logger.LogError("Error logged: {ErrorType} - {Message} in {Context}",
                    errorInfo.ExceptionType, errorInfo.Message, errorInfo.Context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log error to local storage");
            }
        }

        private async Task LogToFileAsync(ErrorInfo errorInfo)
        {
            try
            {
                var logEntry = FormatLogEntry(errorInfo);
                var filePath = Path.Combine(Directory.GetCurrentDirectory(), "ErrorLog.txt");

                await File.AppendAllTextAsync(filePath, logEntry + Environment.NewLine);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to write error to ErrorLog.txt file");
            }
        }

        private string FormatLogEntry(ErrorInfo errorInfo)
        {
            var sb = new StringBuilder();
            sb.AppendLine("=".PadRight(80, '='));
            sb.AppendLine($"TIMESTAMP: {errorInfo.Timestamp:yyyy-MM-dd HH:mm:ss} UTC");
            sb.AppendLine($"ERROR TYPE: {errorInfo.ExceptionType}");
            sb.AppendLine($"MESSAGE: {errorInfo.Message}");
            sb.AppendLine($"CONTEXT: {errorInfo.Context}");
            sb.AppendLine($"SEVERITY: {errorInfo.Severity}");
            sb.AppendLine($"URL: {errorInfo.Url}");
            sb.AppendLine($"USER AGENT: {errorInfo.UserAgent}");
            sb.AppendLine($"USER ID: {errorInfo.UserId}");

            if (!string.IsNullOrEmpty(errorInfo.InnerException))
            {
                sb.AppendLine($"INNER EXCEPTION: {errorInfo.InnerException}");
            }

            if (errorInfo.AdditionalData?.Any() == true)
            {
                sb.AppendLine("ADDITIONAL DATA:");
                foreach (var kvp in errorInfo.AdditionalData)
                {
                    sb.AppendLine($"  {kvp.Key}: {kvp.Value}");
                }
            }

            if (!string.IsNullOrEmpty(errorInfo.StackTrace))
            {
                sb.AppendLine("STACK TRACE:");
                sb.AppendLine(errorInfo.StackTrace);
            }

            sb.AppendLine("=".PadRight(80, '='));

            return sb.ToString();
        }

        public async Task LogExceptionAsync(Exception exception, string context = "", Dictionary<string, object>? additionalData = null)
        {
            var errorInfo = new ErrorInfo
            {
                ExceptionType = exception.GetType().Name,
                Message = exception.Message,
                StackTrace = exception.StackTrace ?? string.Empty,
                Context = context,
                Severity = DetermineErrorSeverity(exception),
                InnerException = exception.InnerException?.Message ?? string.Empty,
                AdditionalData = additionalData ?? new Dictionary<string, object>()
            };

            await LogErrorAsync(errorInfo);
        }

        public async Task<List<ErrorInfo>> GetRecentErrorsAsync(int count = 10)
        {
            try
            {
                var allLogs = await GetErrorLogsFromStorageAsync();
                return allLogs.Take(count).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve error logs from storage");
                return new List<ErrorInfo>();
            }
        }

        public async Task ClearOldLogsAsync(int olderThanDays = 7)
        {
            try
            {
                var allLogs = await GetErrorLogsFromStorageAsync();
                var cutoffDate = DateTime.UtcNow.AddDays(-olderThanDays);
                
                var recentLogs = allLogs.Where(log => log.Timestamp > cutoffDate).ToList();
                
                await _localStorage.SetItemAsync(ErrorLogsKey, recentLogs);
                
                _logger.LogInformation("Cleared {Count} old error logs", allLogs.Count - recentLogs.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear old error logs");
            }
        }

        private async Task<List<ErrorInfo>> GetErrorLogsFromStorageAsync()
        {
            try
            {
                var logs = await _localStorage.GetItemAsync<List<ErrorInfo>>(ErrorLogsKey);
                return logs ?? new List<ErrorInfo>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve error logs from local storage");
                return new List<ErrorInfo>();
            }
        }

        private ErrorSeverity DetermineErrorSeverity(Exception exception)
        {
            return exception switch
            {
                ArgumentException => ErrorSeverity.Low,
                FormatException => ErrorSeverity.Low,
                TaskCanceledException => ErrorSeverity.Low,
                System.Net.Http.HttpRequestException httpEx when httpEx.Message.Contains("timeout") => ErrorSeverity.Medium,
                System.Net.Http.HttpRequestException httpEx when httpEx.Message.Contains("404") => ErrorSeverity.Medium,
                System.Net.Http.HttpRequestException => ErrorSeverity.High,
                UnauthorizedAccessException => ErrorSeverity.High,
                InvalidOperationException => ErrorSeverity.Medium,
                NotSupportedException => ErrorSeverity.Medium,
                OverflowException => ErrorSeverity.Medium,
                DivideByZeroException => ErrorSeverity.High,
                NullReferenceException => ErrorSeverity.High,
                OutOfMemoryException => ErrorSeverity.Critical,
                StackOverflowException => ErrorSeverity.Critical,
                _ => ErrorSeverity.Medium
            };
        }
    }
}
