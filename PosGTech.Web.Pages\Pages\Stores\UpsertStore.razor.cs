﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Stores;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Stores
{
    public partial class UpsertStore
    {
        [CascadingParameter] MudDialogInstance MudDialog { get; set; }
        [Parameter]
        public Guid id { get; set; }
        StoreDTO Store = new();
        [Inject]
        IGRepository<StoreDTO> _Store { get; set; }
        protected override async Task OnInitializedAsync()
        {
            if (id != Guid.Empty)
            {
                var res = await _Store.GetByIdAsync("Stores/getStoreById", id);
                if (res.response == null)
                {
                    Store = res.model;
                }
                else
                {
                    _snackbar.Add("خطأ في الاتصال", Severity.Error);
                    MudDialog.Cancel();
                }


            }
        }
        async void Upsert()
        {
            ResponseVM response;
            if (id == Guid.Empty)
                response = await _Store.Insert("Stores/insertStore", Store);
            else
                response = await _Store.Update("Stores/updateStore", Store, id);

            if (response.State)
            {
                _snackbar.Add(response.Message, Severity.Success);
                MudDialog.Close(DialogResult.Ok(true));
            }
            else
            {
                _snackbar.Add(response.Message, Severity.Error);
            }
        }
        void Cancel() => MudDialog.Cancel();

    }
}