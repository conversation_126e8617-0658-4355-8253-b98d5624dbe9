﻿@page "/upsertEmployee/{id:guid}"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<MudDialog>
    <DialogContent>
        <EditForm id="_form" Model="@employee" OnValidSubmit="@Upsert">
            <DataAnnotationsValidator />
            <MudPaper Class="employee-form-container" Elevation="3">
                <div class="form-header">
                    <MudText Typo="Typo.h5" Class="mb-4 primary-text">
                        @(id == Guid.Empty ? "إضافة موظف جديد" : "تعديل بيانات الموظف")
                    </MudText>
                </div>

                <MudDivider Class="mb-4" />

                <MudGrid Class="pa-4">
                    <MudItem xs="12" sm="6">
                        <MudTextField T="string"
                                      @bind-Value="employee.Name"
                                      Label="اسم الموظف"
                                      For="@(() => employee.Name)"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.End"
                                      AdornmentIcon="@Icons.Material.Filled.Person"
                                      Class="mud-input-elegant" />
                    </MudItem>

                    <MudItem xs="12" sm="6">
                        <MudNumericField HideSpinButtons="true"
                                         Variant="Variant.Outlined"
                                         T="int?"
                                         @bind-Value="employee.Phone"
                                         Label="رقم الهاتف"
                                         For="@(() => employee.Phone)"
                                         Adornment="Adornment.End"
                                         AdornmentIcon="@Icons.Material.Filled.Phone"
                                         Class="mud-input-elegant" />
                    </MudItem>

                    <MudItem xs="12" sm="6">
                        <MudTextField T="string"
                                      @bind-Value="employee.Job"
                                      Label="الوظيفة"
                                      For="@(() => employee.Job)"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.End"
                                      AdornmentIcon="@Icons.Material.Filled.Work"
                                      Class="mud-input-elegant" />
                    </MudItem>

                    <MudItem xs="12" sm="6">
                        <MudNumericField HideSpinButtons="true"
                                         Variant="Variant.Outlined"
                                         T="decimal"
                                         @bind-Value="employee.Salary"
                                         Label="المرتب"
                                         For="@(() => employee.Salary)"
                                         Adornment="Adornment.End"
                                         AdornmentIcon="@Icons.Material.Filled.AttachMoney"
                                         Class="mud-input-elegant" />
                    </MudItem>

                    <MudItem xs="12" sm="6">
                        <MudNumericField ReadOnly="id!=Guid.Empty"
                                         T="decimal"
                                         @bind-Value="employee.Balance"
                                         Label="الرصيد الحالي"
                                         For="@(() => employee.Balance)"
                                         Variant="Variant.Outlined"
                                         Adornment="Adornment.End"
                                         AdornmentIcon="@Icons.Material.Filled.AccountBalance"
                                         Class="mud-input-elegant" />
                    </MudItem>
                </MudGrid>

                @if (id != Guid.Empty)
                {
                    <MudDivider Class="my-4" />

                    <div class="action-buttons-container mt-4 px-4">
                        <MudText Typo="Typo.subtitle1" Class="mb-3">إجراءات إضافية:</MudText>
                        <div class="d-flex flex-wrap gap-2">
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       OnClick="@(()=>AddReceipt(FinancialId.Employee))"
                                       StartIcon="@Icons.Material.Filled.Receipt"
                                       Class="receipt-button">
                                إيصال صرف\قبض
                            </MudButton>

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Secondary"
                                       OnClick="@(()=>AddReceipt(FinancialId.OpeningBalanceForEmployee))"
                                       StartIcon="@Icons.Material.Filled.PlaylistAdd"
                                       Class="receipt-button">
                                إيصال رصيد افتتاحي
                            </MudButton>

                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Tertiary"
                                       OnClick="@(()=>AddReceipt(FinancialId.SalaryPayment))"
                                       StartIcon="@Icons.Material.Filled.Payments"
                                       Class="receipt-button">
                                إيصال راتب
                            </MudButton>
                        </div>
                    </div>
                }
            </MudPaper>
        </EditForm>
    </DialogContent>

    <DialogActions >
        <MudButton Variant="Variant.Outlined"
                   OnClick="@Cancel"
                   Color="Color.Error"
                   StartIcon="@Icons.Material.Filled.Cancel"
                   Class="cancel-button">
            إلغاء
        </MudButton>

        <MudButton form="_form"
                   Variant="Variant.Filled"
                   ButtonType="ButtonType.Submit"
                   Color="Color.Success"
                   StartIcon="@Icons.Material.Filled.Save"
                   Class="save-button">
            حفظ
        </MudButton>
    </DialogActions>
</MudDialog>

<style>
    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }

    .employee-form-container {
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .form-header {
        padding: 16px;
      /*   background-color: var(--mud-palette-primary-lighten); */
    }

    .primary-text {
        color: var(--mud-palette-primary-darken);
        font-weight: 500;
    }

    .mud-input-elegant {
        margin-bottom: 8px;
    }

    .action-buttons-container {
        margin-bottom: 16px;
    }

    .receipt-button {
        transition: transform 0.2s;
        border-radius: 4px;
        font-weight: 500;
        margin-bottom: 8px;
    }

        .receipt-button:hover {
            transform: translateY(-2px);
        }

    .save-button, .cancel-button {
        border-radius: 4px;
        font-weight: 500;
        min-width: 100px;
        transition: all 0.2s ease;
    }

        .save-button:hover, .cancel-button:hover {
            transform: translateY(-2px);
        }
</style>

@*

<MudDialog>
    <DialogContent>

        <EditForm id="_form" Model="@employee" OnValidSubmit="@Upsert">
            <DataAnnotationsValidator />
            <MudPaper Class="d-flex" Elevation="0">
                <MudGrid Class="pa-4">
                    <MudItem xs="6">
                        <MudTextField T="string" @bind-Value="employee.Name" Label="اسم الموظف" For="@(() => employee.Name)" />

                    </MudItem> <MudItem xs="4">
                        <MudNumericField HideSpinButtons="true" Variant="Variant.Text" T="int?" @bind-Value="employee.Phone" Label="رقم الهاتف" For="@(() => employee.Phone)" />

                    </MudItem> <MudItem xs="4">
                        <MudTextField T="string" @bind-Value="employee.Job" Label="الوظيفة" For="@(() => employee.Job)" />

                    </MudItem> <MudItem xs="4">
                        <MudNumericField HideSpinButtons="true" Variant="Variant.Text" T="decimal" @bind-Value="employee.Salary" Label="المرتب" For="@(() => employee.Salary)" />

                    </MudItem> <MudItem xs="4">
                        <MudNumericField ReadOnly="id!=Guid.Empty" T="decimal" @bind-Value="employee.Balance" Label="الرصيد الحالي" For="@(() => employee.Balance)" />

                    </MudItem>
                    @if (id != Guid.Empty)
                    {
                        <MudItem xs="12" Class="d-flex">
                            <MudButton Style="align-self: end;" Variant="Variant.Filled" Color="Color.Primary" OnClick="@(()=>AddReceipt(FinancialId.Employee))" EndIcon="@Icons.Material.Filled.Save" Class="px-6 ma-2">
                                <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">إيصال صرف\قبض</MudText>
                            </MudButton>
                            <MudButton Style="align-self: end;" Variant="Variant.Filled" Color="Color.Primary" OnClick="@(()=>AddReceipt(FinancialId.OpeningBalanceForEmployee))" EndIcon="@Icons.Material.Filled.Save" Class="px-6 ma-2">
                                <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">إيصال رصيد افتتاحي</MudText>
                            </MudButton>
                            <MudButton Style="align-self: end;" Variant="Variant.Filled" Color="Color.Primary" OnClick="@(()=>AddReceipt(FinancialId.SalaryPayment))" EndIcon="@Icons.Material.Filled.Save" Class="px-6 ma-2">
                                <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">إيصال راتب</MudText>
                            </MudButton>
                        </MudItem>
                    }
                </MudGrid>
            </MudPaper>
        </EditForm>
    </DialogContent>
    <DialogActions>

        <MudButton Style="align-self: end;" Variant="Variant.Filled" OnClick="@Cancel" Color="Color.Error" EndIcon="@Icons.Material.Filled.Cancel" Class="px-6 ma-2">
            <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">الغاء</MudText>
        </MudButton>


        <MudButton Style="align-self: end;" form="_form" Variant="Variant.Filled" ButtonType="ButtonType.Submit" Color="Color.Primary" EndIcon="@Icons.Material.Filled.Save" Class="px-6 ma-2">
            <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">حفظ</MudText>
        </MudButton>
    </DialogActions>
</MudDialog>

<style>

    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }
</style> *@