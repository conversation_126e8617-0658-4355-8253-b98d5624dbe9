﻿namespace PosGTech.Models;

/// <summary>
/// جدول فواتير المبيعات
/// </summary>
public class ReturnSell : BaseEntity
{
    public short InvoiceNo { get; set; }
    public DateOnly Date { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
    public decimal Total { get; set; }
    [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
    public decimal FinalTotal { get; set; }
    public Guid? ClientId { get; set; }
    public Client? Client { get; set; }
    public ICollection<ReturnSellItem>? ReturnSellItems { get; set; }
    public ICollection<Receipt>? Receipts { get; set; }
}
