﻿using AutoMapper;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO;
using PosGTech.ModelsDTO.Inventories;
using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.Purchases;

namespace PosGTech.DataAccess.Repository;

public class InventoryRepository(ApplicationDbContext db, IStoreItemRepository _storeItemRepository, IInventoryItemRepository _inventoryItemRepository) : Repository<Inventory>(db), IInventoryRepository
{
    public async Task AddInventory(Inventory inventory)
    {
        await _storeItemRepository.AddQuantity(inventory.InventoryItems.Select(x => new ItemForChangeQte() { Quantity = x.Quantity, CostPrice = x.Price, ItemId = x.ItemId, Exp = x.Exp, ItemUnitId = x.ItemUnitId }).ToList(), inventory.StoreId);
        inventory.InvoiceNo = ((await GetLastOrDefault(filter: x => x.StoreId == inventory.StoreId, order: x => x.CreatedAt, tracked: false))?.InvoiceNo).GetValueOrDefault(0) + 1;
        Add(inventory);
    }
    public async Task DeleteInventory(Inventory inventory)
    {
        await _storeItemRepository.UpdateReduceQuantity(inventory.InventoryItems.Select(x => new ItemForChangeQte() { Quantity = x.Quantity, CostPrice = x.Price, ItemId = x.ItemId, Exp = x.Exp, ItemUnitId = x.ItemUnitId }).ToList(), inventory.StoreId);
        Remove(inventory);
    }
    public async Task<InventoryDTO> GetInventoryById(IMapper mapper, Guid id)
    {
        return (await Select(x => new InventoryDTO()
        {
            Id = x.Id,
            Date = x.Date,
            InvoiceNo = x.InvoiceNo,
            InventoryItemDTOs = mapper.Map<ICollection<InventoryItem>, List<InventoryItemDTO>>(x.InventoryItems),
            Note = x.Note,
            StoreId = x.StoreId,
            Total = x.Total,
        }, x => x.Id == id, includeProperties: "InventoryItems.Item,InventoryItems.ItemUnit.Unit")).First();
    }

    public async Task<InventoryDTO?> GetInventoryByNum(IMapper mapper, int num)
    {
        return (await Select(x => new InventoryDTO()
        {
            Id = x.Id,
            InvoiceNo = x.InvoiceNo,
            Note = x.Note,
            StoreId = x.StoreId,
            Total = x.Total,
        }, x => x.InvoiceNo == num)).FirstOrDefault();
    }

    public async Task UpdateInventory(Inventory inventory, Inventory model)
    {
        await _storeItemRepository.UpdateReduceQuantity(inventory.InventoryItems.Select(x => new ItemForChangeQte() { Quantity = x.Quantity, CostPrice = x.Price, ItemId = x.ItemId, Exp = x.Exp, ItemUnitId = x.ItemUnitId }).ToList(), inventory.StoreId);
        _inventoryItemRepository.RemoveRange(inventory.InventoryItems);
        await _storeItemRepository.AddQuantity(model.InventoryItems.Select(x => new ItemForChangeQte() { Quantity = x.Quantity, CostPrice = x.Price, ItemId = x.ItemId, Exp = x.Exp, ItemUnitId = x.ItemUnitId }).ToList(), inventory.StoreId);
        inventory.InventoryItems = model.InventoryItems;
        _inventoryItemRepository.AddRange(model.InventoryItems.ToList());
        inventory.Total = model.Total;
        inventory.Note = model.Note;
        inventory.StoreId = model.StoreId;
        inventory.Date = model.Date;
        inventory.UpdatedAt = DateTime.Now;
        Update(inventory);

    }
   
}
