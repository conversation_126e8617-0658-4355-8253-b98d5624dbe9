﻿
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول المواد
    /// </summary>
    [Index("Name", IsUnique = true)]
    public class Item : BaseEntity
    {

        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        [StringLength(100, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
        public string Name { get; set; }
        public bool IsHaveExp { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid CategoryId { get; set; }
        public Category? Category { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        [Range(0, 10000000, ErrorMessage = "خطا في القيمة")]
        public decimal CostPrice { get; set; }
        public ICollection<ItemUnit>? ItemUnits { get; set; } = new HashSet<ItemUnit>();
        public ICollection<ItemNum>? ItemNums { get; set; } = new HashSet<ItemNum>();
        public ICollection<StoreItem>? StoreItems { get; set; } = new HashSet<StoreItem>();
        public ICollection<PurchaseItem>? PurchaseItems { get; set; }
        public ICollection<InventoryItem>? InventoryItems { get; set; }

    }
}
