# إصلاح مشكلة عدم ظهور سعر البيع في تصفية المخازن

## وصف المشكلة

عند اختيار مخزن محدد في صفحة الأصناف، كانت أسعار البيع تظهر كـ "00" بدلاً من القيم الصحيحة، بينما في وضع "عرض الكل" تظهر الأسعار بشكل صحيح.

## سبب المشكلة

في الـ API endpoint الجديد `getAllItemsByStore`، كان يتم إنشاء `ItemUnitDTO` بدون تعيين خاصية `SalePrice`:

### الكود المسبب للمشكلة:
```csharp
ItemUnits = item.ItemUnits.Select(unit => new ItemUnitDTO
{
    Id = unit.Id,
    IsBasicUnit = unit.IsBasicUnit,
    IsBigger = unit.IsBigger,
    Quantity = unit.Quantity,
    // ❌ مفقود: SalePrice = unit.SalePrice
    Unit = new UnitDTO
    {
        Id = unit.Unit.Id,
        Name = unit.Unit.Name
    }
}).ToList()
```

## كيف يعمل سعر البيع في النظام

### 1. **تخزين السعر**
- سعر البيع محفوظ في `ItemUnit.SalePrice` في قاعدة البيانات
- كل وحدة لها سعر بيع منفصل

### 2. **عرض السعر**
- `ItemDTO.SalePriceForBasicUnit` يبحث عن الوحدة الأساسية
- يستخرج `SalePrice` من `ItemUnitDTO` للوحدة الأساسية

```csharp
public decimal SalePriceForBasicUnit
{
    get
    {
        var basicUnit = ItemUnits?.FirstOrDefault(x => x.IsBasicUnit);
        return basicUnit?.SalePrice ?? 0; // ❌ كان يعيد 0 لأن SalePrice لم يتم تعيينه
    }
}
```

### 3. **عرض السعر مع الوحدة**
```csharp
public string SalePriceForBasicUnitDisplay
{
    get
    {
        var basicUnit = ItemUnits?.FirstOrDefault(x => x.IsBasicUnit);
        if (basicUnit == null) return "0.00";
        
        return $"{basicUnit.SalePrice:F2} / {basicUnit.Unit?.Name ?? "وحدة"}";
    }
}
```

## الحل المطبق

### إضافة SalePrice في الـ mapping:
```csharp
// ✅ الكود المصحح
ItemUnits = item.ItemUnits.Select(unit => new ItemUnitDTO
{
    Id = unit.Id,
    IsBasicUnit = unit.IsBasicUnit,
    IsBigger = unit.IsBigger,
    Quantity = unit.Quantity,
    SalePrice = unit.SalePrice, // ✅ إضافة سعر البيع
    Unit = new UnitDTO
    {
        Id = unit.Unit.Id,
        Name = unit.Unit.Name
    }
}).ToList()
```

## الفرق في السلوك

### قبل الإصلاح:
- ✅ "عرض الكل": أسعار البيع تظهر صحيحة (لأن AutoMapper يتعامل مع التعيين)
- ❌ "مخزن محدد": أسعار البيع تظهر "00" (لأن SalePrice لم يتم تعيينه يدوياً)

### بعد الإصلاح:
- ✅ "عرض الكل": أسعار البيع تظهر صحيحة
- ✅ "مخزن محدد": أسعار البيع تظهر صحيحة

## لماذا يعمل "عرض الكل" بشكل صحيح؟

في وضع "عرض الكل"، يتم استخدام AutoMapper:

```csharp
// في getAllItems (عرض الكل)
var allItems = maper.Map<IEnumerable<Item>, IEnumerable<ItemDTO>>(
    await unitOfWork.Item.GetAll(includeProperties: "ItemNums,ItemUnits.Unit,StoreItems.StoreItemExps"));
```

AutoMapper يقوم تلقائياً بتعيين جميع الخصائص المتطابقة، بما في ذلك `SalePrice`.

## اختبار الإصلاح

### خطوات الاختبار:
1. **انتقل لصفحة الأصناف**
2. **اختر "عرض الكل"** - تأكد من ظهور أسعار البيع
3. **اختر مخزن محدد** - تأكد من ظهور أسعار البيع الصحيحة
4. **قارن الأسعار** - يجب أن تكون متطابقة في الحالتين

### النتائج المتوقعة:
- ✅ أسعار البيع تظهر في جميع الحالات
- ✅ الأسعار متطابقة بين "عرض الكل" و "مخزن محدد"
- ✅ عرض السعر مع اسم الوحدة يعمل بشكل صحيح

## ملاحظات للمطورين

### نصائح لتجنب مشاكل مشابهة:

1. **استخدام AutoMapper عند الإمكان**
   ```csharp
   // أفضل
   var items = maper.Map<IEnumerable<Item>, IEnumerable<ItemDTO>>(sourceItems);
   
   // بدلاً من التعيين اليدوي
   var items = sourceItems.Select(item => new ItemDTO { ... });
   ```

2. **التأكد من تعيين جميع الخصائص المهمة**
   - عند التعيين اليدوي، تأكد من تضمين جميع الخصائص
   - راجع الـ DTO للتأكد من عدم نسيان خصائص مهمة

3. **اختبار جميع السيناريوهات**
   - اختبر الوظائف الجديدة مع جميع الحالات المختلفة
   - قارن النتائج مع الوظائف الموجودة

4. **استخدام نفس منطق التعيين**
   - إذا كان هناك endpoint موجود يعمل بشكل صحيح، استخدم نفس منطق التعيين
   - تجنب إعادة اختراع العجلة

## الخلاصة

تم حل المشكلة بنجاح من خلال:
1. **تحديد السبب**: عدم تعيين `SalePrice` في التعيين اليدوي
2. **إضافة الخاصية المفقودة**: `SalePrice = unit.SalePrice`
3. **التأكد من التطابق**: مع سلوك "عرض الكل"

الآن أسعار البيع تظهر بشكل صحيح في جميع الحالات! 🎉

## الملفات المحدثة
- ✅ `PosGTech.API/Controllers/ItemsController.cs` - إضافة SalePrice في التعيين
- ✅ `Bug_Fix_Missing_SalePrice.md` - توثيق الإصلاح
