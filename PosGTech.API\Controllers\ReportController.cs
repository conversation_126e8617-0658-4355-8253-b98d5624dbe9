﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Reporting.NETCore;
using PosGTech.API.Services;
using PosGTech.DataAccess.Repository.IRepository;

using System.Data;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace PosGTech.API.Controllers
{
    // ViewModels محلية لتجنب أخطاء compilation بعد حذف مشروع PosGTech.Reports
    public class PurchaseItemRVM
    {
        public string Quantity { get; set; } = string.Empty;
        public string Price { get; set; } = string.Empty;
        public string PriceAfterDiscount { get; set; } = string.Empty;
        public string Exp { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public string ItemUnit { get; set; } = string.Empty;
        public string FinalTotal { get; set; } = string.Empty;
    }

    public class SellItemRVM
    {
        public string Quantity { get; set; } = string.Empty;
        public string SalePrice { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public string ItemUnit { get; set; } = string.Empty;
        public string SalePriceAfterDiscount { get; set; } = string.Empty;
        public string Total { get; set; } = string.Empty;
    }
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class ReportController(IUnitOfWork unitOfWork, IMapper maper, ReportService _reportService) : ControllerBase
    {
        [HttpGet("ReportPurchase/{id:Guid}")]
        public async Task<IActionResult> ReportPurchase([FromRoute] Guid id)
        {
            // تم استبدال نظام RDLC بـ Print.js في الواجهة الأمامية
            return BadRequest(new {
                message = "تم استبدال نظام الطباعة القديم بـ Print.js في الواجهة الأمامية. يرجى استخدام النظام الجديد.",
                endpoint = "ReportPurchase",
                status = "deprecated"
            });
        }

        [HttpGet("ReportSell/{id:Guid}")]

        public async Task<IActionResult> ReportSell([FromRoute] Guid id)
        {

            // تم استبدال نظام RDLC بـ Print.js في الواجهة الأمامية
            return BadRequest(new {
                message = "تم استبدال نظام الطباعة القديم بـ Print.js في الواجهة الأمامية. يرجى استخدام النظام الجديد.",
                endpoint = "ReportSell",
                status = "deprecated"
            });





        }

        /// <summary>
        /// إنشاء تقرير مبيعات متجاوب يدعم الطباعة الحرارية وA4
        /// </summary>
        /// <param name="id">معرف فاتورة المبيعات</param>
        /// <param name="printType">نوع الطباعة: Thermal أو A4</param>
        /// <returns>ملف PDF للتقرير</returns>
        [HttpGet("ReportSellResponsive/{id:Guid}")]
        public async Task<IActionResult> ReportSellResponsive([FromRoute] Guid id, [FromQuery] string printType = "Thermal")
        {
            // تم استبدال نظام RDLC بـ Print.js في الواجهة الأمامية
            return BadRequest(new {
                message = "تم استبدال نظام الطباعة القديم بـ Print.js في الواجهة الأمامية. يرجى استخدام النظام الجديد.",
                endpoint = "ReportSellResponsive",
                status = "deprecated"
            });
        }

        /// <summary>
        /// إنشاء تقرير مبيعات حراري (اختصار للطباعة الحرارية)
        /// </summary>
        /// <param name="id">معرف فاتورة المبيعات</param>
        /// <returns>ملف PDF للتقرير الحراري</returns>
        [HttpGet("ReportSellThermal/{id:Guid}")]
        public async Task<IActionResult> ReportSellThermal([FromRoute] Guid id)
        {
            // تم استبدال نظام RDLC بـ Print.js في الواجهة الأمامية
            return BadRequest(new {
                message = "تم استبدال نظام الطباعة القديم بـ Print.js في الواجهة الأمامية. يرجى استخدام النظام الجديد.",
                endpoint = "ReportSellThermal",
                status = "deprecated"
            });
        }

        /// <summary>
        /// إنشاء تقرير مبيعات A4 (اختصار لطباعة A4)
        /// </summary>
        /// <param name="id">معرف فاتورة المبيعات</param>
        /// <returns>ملف PDF للتقرير A4</returns>
        [HttpGet("ReportSellA4/{id:Guid}")]
        public async Task<IActionResult> ReportSellA4([FromRoute] Guid id)
        {
            // تم استبدال نظام RDLC بـ Print.js في الواجهة الأمامية
            return BadRequest(new {
                message = "تم استبدال نظام الطباعة القديم بـ Print.js في الواجهة الأمامية. يرجى استخدام النظام الجديد.",
                endpoint = "ReportSellA4",
                status = "deprecated"
            });
        }

        /// <summary>
        /// دالة مساعدة للحصول على اسم المستخدم الحالي من JWT token
        /// </summary>
        /// <returns>اسم المستخدم الحالي أو "غير محدد" إذا لم يتم العثور عليه</returns>
        private string GetCurrentUserName()
        {
            try
            {
                // محاولة الحصول على اسم المستخدم من JWT claims
                var userName = User.FindFirst(JwtRegisteredClaimNames.Name)?.Value
                              ?? User.FindFirst(ClaimTypes.Name)?.Value
                              ?? User.Identity?.Name;

                return !string.IsNullOrEmpty(userName) ? userName : "غير محدد";
            }
            catch (Exception)
            {
                return "غير محدد";
            }
        }

        /// <summary>
        /// دالة مساعدة لتحويل مسار الشعار إلى مسار كامل
        /// </summary>
        /// <param name="logoPath">مسار الشعار النسبي</param>
        /// <returns>المسار الكامل للشعار أو مسار افتراضي</returns>
        private string GetFullLogoPath(string? logoPath)
        {
            if (string.IsNullOrEmpty(logoPath))
            {
                // إرجاع صورة شفافة افتراضية إذا لم يكن هناك شعار
                return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
            }

            // إذا كان المسار يبدأ بـ http أو https، فهو مسار كامل
            if (logoPath.StartsWith("http://") || logoPath.StartsWith("https://"))
            {
                return logoPath;
            }

            // إذا كان المسار يبدأ بـ data:، فهو base64
            if (logoPath.StartsWith("data:"))
            {
                return logoPath;
            }

            // تحويل المسار النسبي إلى مسار كامل للـ API
            // مثال: /logo/image.png -> https://localhost:7282/logo/image.png
            var baseUrl = $"{Request.Scheme}://{Request.Host}";
            var fullPath = logoPath.StartsWith("/") ? logoPath : "/" + logoPath;
            return baseUrl + fullPath;
        }
    }


}
