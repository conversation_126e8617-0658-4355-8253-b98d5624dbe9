﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Purchases;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PurchasesController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {


        [HttpGet("getAllPurchases")]
        public async Task<IActionResult> GetAllPurchases()
        {
            var purchases = maper.Map<IEnumerable<Purchase>, IEnumerable<PurchaseDTO>>(await unitOfWork.Purchase.GetAll(includeProperties: "Client,PurchaseItems.Item,PurchaseItems.ItemUnit.Unit,Receipts"));
            return Ok(purchases);
        }
        [HttpGet("getAllPurchasesNum")]
        public async Task<IActionResult> GetAllPurchasesNum()
        {
            var purchases = maper.Map<IEnumerable<Purchase>, IEnumerable<PurchasesNumDTO>>(await unitOfWork.Purchase.GetAll(order: x => x.InvoiceNo));
            return Ok(purchases);
        }

        [HttpGet("getPurchaseById/{id:Guid}")]
        public async Task<IActionResult> GetPurchaseById([FromRoute] Guid id)
        {
            var purchase = maper.Map<Purchase, PurchaseDTO>(await unitOfWork.Purchase.GetFirstOrDefault(x => x.Id == id, includeProperties: "Client,PurchaseItems.Item,PurchaseItems.ItemUnit.Unit,Receipts"));
            return Ok(purchase);

        }

        [HttpGet("getPurchaseByNum/{billNum:int}")]
        public async Task<IActionResult> GetPurchaseByNum([FromRoute] int billNum)
        {
            var purchase = maper.Map<Purchase, PurchaseReceiptDTO>(await unitOfWork.Purchase.GetFirstOrDefault(x => x.InvoiceNo == billNum, includeProperties: "Client,PurchaseItems.Item,PurchaseItems.ItemUnit.Unit,Receipts"));
            return Ok(purchase);
        }


        [HttpPost("insertPurchase")]
        public async Task<IActionResult> InsertPurchase([FromBody] PurchaseDTO model)
        {

            var newpurchase = maper.Map<PurchaseDTO, Purchase>(model);
            var id = await unitOfWork.Purchase.AddPurchase(newpurchase, model.Paid, model.TreasuryId);
            if (model.Paid != 0 && unitOfWork.UserTreasury.CheckUserTreasuryBelowZero())
                return BadRequest(new ResponseVM() { State = false, Message = "القيمة غير موجودة" });
            if (model.SalePriceChanges.Any()) await unitOfWork.ItemUnit.ChangePriceForBasicUnit(model.SalePriceChanges);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) { res.Message = id.ToString(); return Ok(res); }
            else return BadRequest(res);
        }


        [HttpPut("updatePurchase/{id:Guid}")]
        public async Task<IActionResult> UpdatePurchase([FromRoute] Guid id, PurchaseDTO model)
        {
            var oldpurchase = await unitOfWork.Purchase.GetFirstOrDefault(x => x.Id == id, "Client,PurchaseItems.Item,PurchaseItems.ItemUnit.Unit");
            if (oldpurchase == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            if (model.Paid != 0 && oldpurchase.ClientId != model.Client.Id) return BadRequest(new ResponseVM() { Message = "لا يمكن تغيير العميل" });
            await unitOfWork.Purchase.UpdatePurchase(oldpurchase, maper.Map<PurchaseDTO, Purchase>(model));
            if (unitOfWork.StoreItemExp.CheckQuantityItemsBelowZero()) return BadRequest(new ResponseVM() { State = false, Message = "الكمية غير موجودة" });
            if (model.SalePriceChanges.Any()) await unitOfWork.ItemUnit.ChangePriceForBasicUnit(model.SalePriceChanges);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpDelete("deletePurchase/{id:Guid}")]
        public async Task<IActionResult> DeletePurchase([FromRoute] Guid id)
        {
            var oldpurchase = await unitOfWork.Purchase.GetFirstOrDefault(x => x.Id == id, "Client,PurchaseItems.Item,PurchaseItems.ItemUnit.Unit,Receipts");
            if (oldpurchase == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            if (oldpurchase.PurchaseItems.Any(x => x.ReturnQuantity > 0)) return BadRequest(new ResponseVM() { Message = "لا يمكن الحذف يوجد اصناف مرجعة" });
            if (oldpurchase.Receipts.Any()) return BadRequest(new ResponseVM() { Message = "لا يمكن الحذف يوجد ايصالات لهذه الفاتورة" });
            await unitOfWork.Purchase.DeletePurchase(oldpurchase);
            if (unitOfWork.StoreItemExp.CheckQuantityItemsBelowZero()) return BadRequest(new ResponseVM() { State = false, Message = "الكمية غير موجودة" });
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }
    }
}
