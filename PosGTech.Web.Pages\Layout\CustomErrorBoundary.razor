﻿@using PosGTech.Web.Services.Interfaces
@using MudBlazor
@using Microsoft.AspNetCore.Components
@inherits ErrorBoundary
@inject IErrorHandlingService ErrorHandlingService
@inject ISnackbar Snackbar
@inject NavigationManager NavigationManager

@{
    Console.WriteLine($"CustomErrorBoundary: Rendering - CurrentException is null: {CurrentException is null}");
    if (CurrentException != null)
    {
        Console.WriteLine($"CustomErrorBoundary: Rendering error UI for exception: {CurrentException.GetType().Name}");
        Console.WriteLine($"CustomErrorBoundary: Total exceptions: {receivedExceptions.Count}");
    }
}

@if (CurrentException is null)
{
    @ChildContent
}
else if (ErrorContent is not null)
{
    @ErrorContent(CurrentException)
}
else
{
    <!-- Force full screen error display with high z-index -->
    <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0.95); z-index: 9999; display: flex; align-items: center; justify-content: center;">
        <MudContainer MaxWidth="MaxWidth.Medium">
            <MudPaper Elevation="3" Class="pa-6" Style="background-color: white; border: 2px solid #f44336;">
                <div dir="rtl" class="text-center">
                    <MudIcon Icon="@Icons.Material.Filled.Error"
                             Color="Color.Error"
                             Size="Size.Large"
                             Class="mb-4" />

                    <MudText Typo="Typo.h5" Color="Color.Error" Class="mb-4">
                        حدث خطأ غير متوقع
                    </MudText>

                    <MudText Typo="Typo.body1" Class="mb-4">
                        @GetUserFriendlyMessage()
                    </MudText>

                @if (ShowDetails)
                {
                    <MudExpansionPanels Elevation="0">
                        <MudExpansionPanel Text="تفاصيل الخطأ (للمطورين)">
                            <div dir="ltr" style="font-family: monospace; font-size: 0.8rem; background-color: #f5f5f5; padding: 1rem; border-radius: 4px; text-align: left;">
                                @foreach (var exception in receivedExceptions)
                                {
                                    <div class="mb-3">
                                        <strong>Type:</strong> @exception.GetType().Name<br/>
                                        <strong>Message:</strong> @exception.Message<br/>
                                        @if (!string.IsNullOrEmpty(exception.StackTrace))
                                        {
                                            <strong>Stack Trace:</strong><br/>
                                            <pre style="white-space: pre-wrap; font-size: 0.7rem;">@exception.StackTrace</pre>
                                        }
                                    </div>
                                }
                            </div>
                        </MudExpansionPanel>
                    </MudExpansionPanels>
                }

                <div class="mt-6">
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Refresh"
                               OnClick="HandleRecover"
                               Class="ml-2">
                        إعادة المحاولة
                    </MudButton>

                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Secondary"
                               StartIcon="@Icons.Material.Filled.Home"
                               OnClick="NavigateToHome"
                               Class="ml-2">
                        الصفحة الرئيسية
                    </MudButton>

                    <MudButton Variant="Variant.Text"
                               Color="Color.Info"
                               StartIcon="@Icons.Material.Filled.BugReport"
                               OnClick="ToggleDetails">
                        @(ShowDetails ? "إخفاء التفاصيل" : "إظهار التفاصيل")
                    </MudButton>
                </div>
            </div>
        </MudPaper>
    </MudContainer>
    </div>
}

@code {
    private List<Exception> receivedExceptions = new();
    private bool ShowDetails = false;

    protected override async Task OnErrorAsync(Exception exception)
    {
        Console.WriteLine("=== CustomErrorBoundary: OnErrorAsync STARTED ===");
        Console.WriteLine($"CustomErrorBoundary: Error caught - {exception.GetType().Name}: {exception.Message}");
        Console.WriteLine($"CustomErrorBoundary: Stack trace: {exception.StackTrace}");
        Console.WriteLine($"CustomErrorBoundary: Current exceptions count before adding: {receivedExceptions.Count}");

        receivedExceptions.Add(exception);
        Console.WriteLine($"CustomErrorBoundary: Current exceptions count after adding: {receivedExceptions.Count}");

        // Log the error using the error handling service
        try
        {
            Console.WriteLine("CustomErrorBoundary: Attempting to log error...");
            await ErrorHandlingService.LogErrorAsync(exception, "ErrorBoundary", new { Component = "CustomErrorBoundary" });
            Console.WriteLine("CustomErrorBoundary: Error logged successfully");
        }
        catch (Exception loggingException)
        {
            // Fallback if error handling service fails
            Console.WriteLine($"CustomErrorBoundary: Failed to log error: {loggingException.Message}");
        }

        Console.WriteLine("CustomErrorBoundary: Calling base.OnErrorAsync...");
        await base.OnErrorAsync(exception);
        Console.WriteLine("CustomErrorBoundary: OnErrorAsync completed");
        Console.WriteLine("CustomErrorBoundary: Calling StateHasChanged...");
        StateHasChanged();
        Console.WriteLine("=== CustomErrorBoundary: OnErrorAsync FINISHED ===");
    }

    private string GetUserFriendlyMessage()
    {
        if (receivedExceptions.Count == 0)
            return "حدث خطأ غير معروف.";

        var latestException = receivedExceptions.Last();

        try
        {
            return ErrorHandlingService.GetUserFriendlyMessage(latestException);
        }
        catch
        {
            // Fallback message if error handling service fails
            return "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.";
        }
    }

    private async Task HandleRecover()
    {
        try
        {
            receivedExceptions.Clear();
            ShowDetails = false;
            Recover();

            Snackbar.Add("تم إعادة تحميل المكون بنجاح", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add("فشل في إعادة تحميل المكون", Severity.Error);
            Console.WriteLine($"Recovery failed: {ex.Message}");
        }
    }

    private void NavigateToHome()
    {
        try
        {
            NavigationManager.NavigateTo("/");
            Snackbar.Add("تم الانتقال إلى الصفحة الرئيسية", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add("فشل في الانتقال إلى الصفحة الرئيسية", Severity.Error);
            Console.WriteLine($"Navigation failed: {ex.Message}");
        }
    }

    private void ToggleDetails()
    {
        ShowDetails = !ShowDetails;
        StateHasChanged();
    }

    public new void Recover()
    {
        receivedExceptions.Clear();
        ShowDetails = false;
        base.Recover();
    }
}