using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using PosGTech.Web.Services.Interfaces;
using System;
using System.Threading.Tasks;

namespace PosGTech.Web.Services
{
    /// <summary>
    /// Global exception handler for Blazor WebAssembly applications
    /// </summary>
    public class GlobalExceptionHandler
    {
        private readonly IErrorHandlingService _errorHandlingService;
        private readonly ILogger<GlobalExceptionHandler> _logger;
        private readonly IJSRuntime _jsRuntime;

        public GlobalExceptionHandler(
            IErrorHandlingService errorHandlingService,
            ILogger<GlobalExceptionHandler> logger,
            IJSRuntime jsRuntime)
        {
            _errorHandlingService = errorHandlingService;
            _logger = logger;
            _jsRuntime = jsRuntime;
        }

        /// <summary>
        /// Initializes global exception handling
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                // Set up .NET unhandled exception handlers
                AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
                TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;

                // Set up JavaScript error handling
                await SetupJavaScriptErrorHandlingAsync();

                _logger.LogInformation("Global exception handler initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize global exception handler");
            }
        }

        /// <summary>
        /// Handles unhandled exceptions from .NET code
        /// </summary>
        /// <param name="exception">The unhandled exception</param>
        /// <param name="context">Context where the exception occurred</param>
        public async Task HandleUnhandledExceptionAsync(Exception exception, string context = "Global")
        {
            try
            {
                await _errorHandlingService.HandleExceptionAsync(exception, context, true);
            }
            catch (Exception handlingException)
            {
                // Fallback logging if error handling service fails
                _logger.LogCritical(handlingException, "Critical failure in exception handling for: {OriginalException}", exception.Message);
            }
        }

        /// <summary>
        /// Sets up JavaScript error handling for unhandled JS errors and promise rejections
        /// </summary>
        private async Task SetupJavaScriptErrorHandlingAsync()
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("setupGlobalErrorHandling", 
                    DotNetObjectReference.Create(this));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to setup JavaScript error handling");
            }
        }

        /// <summary>
        /// Called from JavaScript when an unhandled error occurs
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="source">Source file where error occurred</param>
        /// <param name="line">Line number where error occurred</param>
        /// <param name="column">Column number where error occurred</param>
        /// <param name="error">Error object details</param>
        [JSInvokable]
        public async Task HandleJavaScriptErrorAsync(string message, string source, int line, int column, string error)
        {
            try
            {
                var jsException = new JavaScriptException(message, source, line, column, error);
                await _errorHandlingService.HandleExceptionAsync(jsException, "JavaScript", true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to handle JavaScript error: {Message}", message);
            }
        }

        /// <summary>
        /// Called from JavaScript when an unhandled promise rejection occurs
        /// </summary>
        /// <param name="reason">Rejection reason</param>
        [JSInvokable]
        public async Task HandleUnhandledPromiseRejectionAsync(string reason)
        {
            try
            {
                var promiseException = new UnhandledPromiseRejectionException(reason);
                await _errorHandlingService.HandleExceptionAsync(promiseException, "Promise Rejection", true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to handle promise rejection: {Reason}", reason);
            }
        }

        /// <summary>
        /// Handles unhandled exceptions from AppDomain
        /// </summary>
        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                if (e.ExceptionObject is Exception exception)
                {
                    // Use fire-and-forget pattern since this is a synchronous event handler
                    _ = Task.Run(async () => await HandleUnhandledExceptionAsync(exception, "AppDomain"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogCritical(ex, "Failed to handle AppDomain unhandled exception");
            }
        }

        /// <summary>
        /// Handles unobserved task exceptions
        /// </summary>
        private void OnUnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            try
            {
                // Mark as observed to prevent application termination
                e.SetObserved();

                // Handle each exception in the aggregate
                foreach (var exception in e.Exception.InnerExceptions)
                {
                    // Use fire-and-forget pattern since this is a synchronous event handler
                    _ = Task.Run(async () => await HandleUnhandledExceptionAsync(exception, "UnobservedTask"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogCritical(ex, "Failed to handle unobserved task exception");
            }
        }
    }

    /// <summary>
    /// Custom exception for JavaScript errors
    /// </summary>
    public class JavaScriptException : Exception
    {
        public new string Source { get; }
        public int Line { get; }
        public int Column { get; }
        public string ErrorDetails { get; }

        public JavaScriptException(string message, string source, int line, int column, string errorDetails)
            : base($"JavaScript Error: {message}")
        {
            Source = source;
            Line = line;
            Column = column;
            ErrorDetails = errorDetails;
        }

        public override string ToString()
        {
            return $"{Message}\nSource: {Source}\nLine: {Line}, Column: {Column}\nDetails: {ErrorDetails}";
        }
    }

    /// <summary>
    /// Custom exception for unhandled promise rejections
    /// </summary>
    public class UnhandledPromiseRejectionException : Exception
    {
        public UnhandledPromiseRejectionException(string reason)
            : base($"Unhandled Promise Rejection: {reason}")
        {
        }
    }
}
