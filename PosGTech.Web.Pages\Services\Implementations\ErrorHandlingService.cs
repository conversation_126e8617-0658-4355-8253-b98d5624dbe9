using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using MudBlazor;
using PosGTech.Web.Services.Interfaces;
using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// Implementation of centralized error handling service
    /// </summary>
    public class ErrorHandlingService : IErrorHandlingService
    {
        private readonly ILogger<ErrorHandlingService> _logger;
        private readonly ISnackbar _snackbar;
        private readonly NavigationManager _navigationManager;
        private readonly IErrorLoggingService _errorLoggingService;

        public ErrorHandlingService(
            ILogger<ErrorHandlingService> logger,
            ISnackbar snackbar,
            NavigationManager navigationManager,
            IErrorLoggingService errorLoggingService)
        {
            _logger = logger;
            _snackbar = snackbar;
            _navigationManager = navigationManager;
            _errorLoggingService = errorLoggingService;
        }

        public async Task HandleExceptionAsync(Exception exception, string context = "", bool showToUser = true)
        {
            try
            {
                // Log the error
                await LogErrorAsync(exception, context);

                // Show user-friendly message if requested
                if (showToUser)
                {
                    var userMessage = GetUserFriendlyMessage(exception);
                    var severity = GetErrorSeverity(exception);
                    
                    var snackbarSeverity = severity switch
                    {
                        ErrorSeverity.Low => Severity.Info,
                        ErrorSeverity.Medium => Severity.Warning,
                        ErrorSeverity.High => Severity.Error,
                        ErrorSeverity.Critical => Severity.Error,
                        _ => Severity.Error
                    };

                    _snackbar.Add(userMessage, snackbarSeverity, config =>
                    {
                        config.VisibleStateDuration = severity == ErrorSeverity.Critical ? 10000 : 5000;
                        config.ShowCloseIcon = true;
                        config.RequireInteraction = severity >= ErrorSeverity.High;
                    });
                }

                // Navigate to error page for critical errors
                if (GetErrorSeverity(exception) == ErrorSeverity.Critical)
                {
                    await NavigateToErrorPageAsync(exception, _navigationManager.Uri);
                }
            }
            catch (Exception handlingException)
            {
                // Fallback error handling
                _logger.LogCritical(handlingException, "Error occurred while handling exception: {OriginalException}", exception.Message);
                _snackbar.Add("حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.", Severity.Error);
            }
        }

        public async Task LogErrorAsync(Exception exception, string context = "", object? additionalData = null)
        {
            try
            {
                var errorInfo = new ErrorInfo
                {
                    ExceptionType = exception.GetType().Name,
                    Message = exception.Message,
                    StackTrace = exception.StackTrace ?? string.Empty,
                    Context = context,
                    Severity = GetErrorSeverity(exception),
                    Url = _navigationManager.Uri,
                    InnerException = exception.InnerException?.Message ?? string.Empty
                };

                if (additionalData != null)
                {
                    errorInfo.AdditionalData["CustomData"] = additionalData;
                }

                await _errorLoggingService.LogErrorAsync(errorInfo);
                
                // Also log to standard logger
                _logger.LogError(exception, "Error in context: {Context}", context);
            }
            catch (Exception loggingException)
            {
                _logger.LogCritical(loggingException, "Failed to log error: {OriginalException}", exception.Message);
            }
        }

        public string GetUserFriendlyMessage(Exception exception)
        {
            return exception switch
            {
                HttpRequestException httpEx when httpEx.Message.Contains("timeout") => "انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.",
                HttpRequestException httpEx when httpEx.Message.Contains("network") => "خطأ في الشبكة. يرجى التحقق من اتصال الإنترنت.",
                HttpRequestException => "خطأ في الاتصال بالخادم. يرجى المحاولة لاحقاً.",
                TaskCanceledException => "تم إلغاء العملية أو انتهت المهلة المحددة.",
                UnauthorizedAccessException => "غير مصرح لك بالوصول إلى هذه الميزة.",
                ArgumentNullException => "بيانات مطلوبة مفقودة. يرجى التحقق من المدخلات.",
                ArgumentException => "البيانات المدخلة غير صحيحة.",
                InvalidOperationException => "لا يمكن تنفيذ هذه العملية في الوقت الحالي.",
                NotSupportedException => "هذه العملية غير مدعومة.",
                FormatException => "تنسيق البيانات غير صحيح.",
                OverflowException => "القيمة المدخلة كبيرة جداً أو صغيرة جداً.",
                DivideByZeroException => "لا يمكن القسمة على صفر.",
                NullReferenceException => "حدث خطأ في البيانات. يرجى إعادة تحميل الصفحة.",
                _ => "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني."
            };
        }

        public ErrorSeverity GetErrorSeverity(Exception exception)
        {
            return exception switch
            {
                ArgumentException => ErrorSeverity.Low,
                FormatException => ErrorSeverity.Low,
                TaskCanceledException => ErrorSeverity.Low,
                HttpRequestException httpEx when httpEx.Message.Contains("timeout") => ErrorSeverity.Medium,
                HttpRequestException httpEx when httpEx.Message.Contains("404") => ErrorSeverity.Medium,
                HttpRequestException => ErrorSeverity.High,
                UnauthorizedAccessException => ErrorSeverity.High,
                InvalidOperationException => ErrorSeverity.Medium,
                NotSupportedException => ErrorSeverity.Medium,
                OverflowException => ErrorSeverity.Medium,
                DivideByZeroException => ErrorSeverity.High,
                NullReferenceException => ErrorSeverity.High,
                OutOfMemoryException => ErrorSeverity.Critical,
                StackOverflowException => ErrorSeverity.Critical,
                _ => ErrorSeverity.Medium
            };
        }

        public async Task NavigateToErrorPageAsync(Exception exception, string? returnUrl = null)
        {
            try
            {
                var errorType = exception switch
                {
                    HttpRequestException httpEx when httpEx.Message.Contains("404") => "404",
                    HttpRequestException httpEx when httpEx.Message.Contains("500") => "500",
                    HttpRequestException httpEx when httpEx.Message.Contains("timeout") => "network",
                    HttpRequestException httpEx when httpEx.Message.Contains("network") => "network",
                    HttpRequestException => "500",
                    TaskCanceledException => "network",
                    UnauthorizedAccessException => "403",
                    ArgumentNullException => "400",
                    ArgumentException => "400",
                    InvalidOperationException => "500",
                    NotSupportedException => "500",
                    FormatException => "400",
                    OverflowException => "400",
                    DivideByZeroException => "500",
                    NullReferenceException => "500",
                    OutOfMemoryException => "500",
                    StackOverflowException => "500",
                    _ => "500"
                };

                var errorPageUrl = $"/error/{errorType}";
                if (!string.IsNullOrEmpty(returnUrl))
                {
                    errorPageUrl += $"?returnUrl={Uri.EscapeDataString(returnUrl)}";
                }

                _navigationManager.NavigateTo(errorPageUrl);
                await Task.CompletedTask;
            }
            catch (Exception navException)
            {
                _logger.LogError(navException, "Failed to navigate to error page for exception: {Exception}", exception.Message);
            }
        }
    }
}
