using PosGTech.ModelsDTO.Sells;
using PosGTech.ModelsDTO.StoreItem;

namespace PosGTech.Web.Services.Interfaces
{
    /// <summary>
    /// Interface for inventory validation service
    /// </summary>
    public interface IInventoryValidationService
    {
        /// <summary>
        /// Validates if the requested quantity is available in inventory
        /// </summary>
        /// <param name="sellItems">List of items to validate</param>
        /// <param name="storeItems">Available store items with inventory</param>
        /// <returns>Validation result with details</returns>
        Task<InventoryValidationResult> ValidateInventoryAsync(
            IEnumerable<SellItemDTO> sellItems, 
            IEnumerable<StoreItemDTO> storeItems);

        /// <summary>
        /// Checks if an item has low stock (less than 5 units)
        /// </summary>
        /// <param name="storeItem">Store item to check</param>
        /// <param name="expId">Expiration ID to check specific batch</param>
        /// <returns>Low stock warning result</returns>
        Task<LowStockWarningResult> CheckLowStockAsync(StoreItemDTO storeItem, Guid expId);

        /// <summary>
        /// Validates a single sell item against available inventory
        /// </summary>
        /// <param name="sellItem">Item to validate</param>
        /// <param name="storeItems">Available store items</param>
        /// <returns>Single item validation result</returns>
        Task<SingleItemValidationResult> ValidateSingleItemAsync(
            SellItemDTO sellItem, 
            IEnumerable<StoreItemDTO> storeItems);
    }

    /// <summary>
    /// Result of inventory validation
    /// </summary>
    public class InventoryValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> ErrorMessages { get; set; } = new();
        public List<InventoryValidationError> ValidationErrors { get; set; } = new();
    }

    /// <summary>
    /// Result of low stock warning check
    /// </summary>
    public class LowStockWarningResult
    {
        public bool HasLowStock { get; set; }
        public decimal AvailableQuantity { get; set; }
        public string WarningMessage { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Result of single item validation
    /// </summary>
    public class SingleItemValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public decimal AvailableQuantity { get; set; }
        public decimal RequestedQuantity { get; set; }
    }

    /// <summary>
    /// Detailed validation error information
    /// </summary>
    public class InventoryValidationError
    {
        public string ItemName { get; set; } = string.Empty;
        public decimal RequestedQuantity { get; set; }
        public decimal AvailableQuantity { get; set; }
        public string UnitName { get; set; } = string.Empty;
        public DateTime? ExpirationDate { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
