using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using PosGTech.Models;
using System.Security.Claims;

namespace PosGTech.ModelsDTO.Authorization;

/// <summary>
/// معالج التفويض المبني على الصلاحيات
/// يتحقق من وجود الصلاحية المطلوبة للمستخدم
/// </summary>
public class PermissionHandler : AuthorizationHandler<PermissionRequirement>
{
    private readonly UserManager<User> _userManager;
    private readonly RoleManager<Role> _roleManager;

    public PermissionHandler(UserManager<User> userManager, RoleManager<Role> roleManager)
    {
        _userManager = userManager;
        _roleManager = roleManager;
    }

    /// <summary>
    /// التحقق من متطلب الصلاحية
    /// </summary>
    /// <param name="context">سياق التفويض</param>
    /// <param name="requirement">متطلب الصلاحية</param>
    /// <returns>مهمة غير متزامنة</returns>
    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        PermissionRequirement requirement)
    {
        // التحقق من أن المستخدم مُصادق عليه
        if (!context.User.Identity?.IsAuthenticated == true)
        {
            context.Fail();
            return;
        }

        // الحصول على معرف المستخدم
        var userId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            context.Fail();
            return;
        }

        // التحقق من وجود الصلاحية في Claims المستخدم مباشرة
        if (context.User.HasClaim("permission", requirement.Permission))
        {
            context.Succeed(requirement);
            return;
        }

        try
        {
            // البحث عن المستخدم
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                context.Fail();
                return;
            }

            // الحصول على أدوار المستخدم
            var userRoles = await _userManager.GetRolesAsync(user);
            
            // التحقق من صلاحيات الأدوار
            foreach (var roleName in userRoles)
            {
                var role = await _roleManager.FindByNameAsync(roleName);
                if (role != null)
                {
                    var roleClaims = await _roleManager.GetClaimsAsync(role);
                    if (roleClaims.Any(c => c.Type == "permission" && c.Value == requirement.Permission))
                    {
                        context.Succeed(requirement);
                        return;
                    }
                }
            }

            // الحصول على Claims المستخدم الإضافية
            var userClaims = await _userManager.GetClaimsAsync(user);
            
            // التحقق من الصلاحيات الإضافية
            var additionalPermissions = userClaims.Where(c => c.Type == "additional_permission").Select(c => c.Value);
            if (additionalPermissions.Contains(requirement.Permission))
            {
                // التحقق من أن الصلاحية ليست محذوفة
                var removedPermissions = userClaims.Where(c => c.Type == "removed_permission").Select(c => c.Value);
                if (!removedPermissions.Contains(requirement.Permission))
                {
                    context.Succeed(requirement);
                    return;
                }
            }

            // إذا لم توجد الصلاحية، فشل التفويض
            context.Fail();
        }
        catch (Exception)
        {
            // في حالة حدوث خطأ، فشل التفويض
            context.Fail();
        }
    }
}
