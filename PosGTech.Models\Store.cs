﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    public class Store : BaseEntity
    {
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        [StringLength(100, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
        public string Name { get; set; }

        [StringLength(100, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
        public string? CompanyName { get; set; }

        [StringLength(20, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
        public string? PhoneNumber { get; set; }

        public string? Address { get; set; }

        [StringLength(500, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
        public string? LogoPath { get; set; }

        public ICollection<StoreItem>? StoreItems { get; set; }
    }
}
