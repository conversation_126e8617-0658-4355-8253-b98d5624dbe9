﻿
@page "/listItems"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<AuthorizeView Policy="@PermissionConstants.ItemsView" Context="auth">
    <Authorized>
        <MudContainer MaxWidth="MaxWidth.False" Class="pa-0">
            <MudPaper Class="d-flex flex-column desktop rounded-lg" Elevation="0">
                <MudTable Items="@items"
                          Height="calc(100vh - 170px)"
                          Loading="loading"
                          Breakpoint="Breakpoint.Sm"
                          Filter="new Func<ItemDTO,bool>(FilterFunc1)"
                          @bind-SelectedItem="selectedItem"
                          Virtualize="true"
                          Striped="true"
                          Dense="true"
                          FixedHeader="true"
                          Elevation="2"
                          Class="rounded-lg">
                    <ToolBarContent>
                        <div class="d-flex align-center gap-4 pa-4 flex-grow-1 toolbar-content">
                            <MudIcon Icon="@Icons.Material.Filled.Inventory"
                                     Color="Color.Primary"
                                     Size="Size.Large" />
                            <MudText Typo="Typo.h5" Color="Color.Primary">الاصناف</MudText>
                            @if (selectedStore != null)
                            {
                                <MudChip T="string"
                                         Color="Color.Info"
                                         Size="Size.Small"
                                         Icon="@Icons.Material.Filled.FilterAlt"
                                         OnClose="@(() => OnStoreSelectionChanged(null))"
                                         Class="ml-2">
                                    @selectedStore.Name
                                </MudChip>
                            }
                            <MudSpacer />

                            <!-- حقل اختيار المخزن -->
                            <MudAutocomplete T="StoreCMDTO"
                                             Value="selectedStore"
                                             SearchFunc="@SearchStores"
                                             ToStringFunc="@(e => e?.Name ?? "عرض الكل")"
                                             Placeholder="اختيار المخزن"
                                             Label="المخزن"
                                             Variant="Variant.Outlined"
                                             Dense="true"
                                             Clearable="true"
                                             ResetValueOnEmptyText="true"
                                             CoerceText="false"
                                             CoerceValue="false"
                                             AdornmentIcon="@Icons.Material.Filled.Warehouse"
                                             Adornment="Adornment.End"
                                             Class="ml-4 flex-grow-0 store-autocomplete"
                                             Style="min-width: 200px;"
                                             ValueChanged="OnStoreSelectionChanged">
                                <ItemTemplate Context="store">
                                    <div class="d-flex align-center gap-2" style="direction: rtl;">
                                        <MudIcon Icon="@Icons.Material.Filled.Warehouse" Size="Size.Small" />
                                        <MudText>@store.Name</MudText>
                                    </div>
                                </ItemTemplate>
                                <NoItemsTemplate>
                                    <div class="d-flex align-center gap-2" style="direction: rtl;">
                                        <MudIcon Icon="@Icons.Material.Filled.ViewList" Size="Size.Small" />
                                        <MudText>عرض الكل</MudText>
                                    </div>
                                </NoItemsTemplate>
                            </MudAutocomplete>

                            <MudTextField @bind-Value="Search"
                                          Placeholder="بحث"
                                          Adornment="Adornment.End"
                                          AdornmentIcon="@Icons.Material.Filled.Search"
                                          Class="ml-4 flex-grow-0"
                                          Style="min-width: 300px;"
                                          Immediate="true"
                                          Variant="Variant.Outlined"
                                          Dense="true" />
                            <AuthorizeView Policy="@PermissionConstants.ItemsAdd" Context="auth1">
                                <Authorized>
                                    <MudButton Variant="Variant.Filled"
                                               EndIcon="@Icons.Material.Filled.Add"
                                               OnClick="()=>Upsert(Guid.Empty)"
                                               Color="Color.Primary"
                                               Size="Size.Large"
                                               Class="ml-4">
                                        إضافة صنف جديد
                                    </MudButton>
                                </Authorized>
                            </AuthorizeView>
                        </div>
                    </ToolBarContent>

                    <HeaderContent>
                        <MudTh Style="width: 80px">#</MudTh>
                        <MudTh>
                            <MudTableSortLabel SortBy="new Func<ItemDTO, object>(x=>x.Name)">
                                <div class="d-flex gap-2">
                                    <MudIcon Icon="@Icons.Material.Filled.Label" Size="Size.Small" />
                                    الاسم
                                </div>
                            </MudTableSortLabel>
                        </MudTh>
                        <MudTh>
                            <MudTableSortLabel SortBy="new Func<ItemDTO, object>(x=>x.Quantity)">
                                <div class="d-flex gap-2">
                                    <MudIcon Icon="@Icons.Material.Filled.Numbers" Size="Size.Small" />
                                    الكمية
                                </div>
                            </MudTableSortLabel>
                        </MudTh>
                        <MudTh>
                            <MudTableSortLabel SortBy="new Func<ItemDTO, object>(x=>x.CostPrice)">
                                <div class="d-flex gap-2">
                                    <MudIcon Icon="@Icons.Material.Filled.MonetizationOn" Size="Size.Small" />
                                    سعر التكلفة
                                </div>
                            </MudTableSortLabel>
                        </MudTh>
                        <MudTh>
                            <MudTableSortLabel SortBy="new Func<ItemDTO, object>(x=>x.SalePriceForBasicUnit)">
                                <div class="d-flex gap-2">
                                    <MudIcon Icon="@Icons.Material.Filled.Sell" Size="Size.Small" />
                                    سعر البيع
                                </div>
                            </MudTableSortLabel>
                        </MudTh>
                        <MudTh Style="width: 100px">الإجراءات</MudTh>
                    </HeaderContent>

                    <RowTemplate>
                        <MudTd DataLabel="#">@(items.ToList().IndexOf(context) + 1)</MudTd>
                        <MudTd DataLabel="الاسم">@context.Name</MudTd>
                        <MudTd DataLabel="الكمية">@context.Quantity</MudTd>
                        <MudTd DataLabel="سعر التكلفة">@context.CostPriceForBasicUnitDisplay</MudTd>
                        <MudTd DataLabel="سعر البيع">@context.SalePriceForBasicUnitDisplay</MudTd>
                        <MudTd>
                            <div class="d-flex gap-2">
                                <AuthorizeView Policy="@PermissionConstants.ItemsEdit" Context="auth2">
                                    <Authorized>
                                        <MudIconButton Size="Size.Small"
                                                       Color="Color.Info"
                                                       Icon="@Icons.Material.Filled.Edit"
                                                       OnClick="()=>Upsert(context.Id)"
                                                       Class="pa-1" />
                                    </Authorized>
                                </AuthorizeView>
                                <AuthorizeView Policy="@PermissionConstants.ItemsDelete" Context="auth3">
                                    <Authorized>
                                        <MudIconButton Size="Size.Small"
                                                       Color="Color.Error"
                                                       Icon="@Icons.Material.Filled.Delete"
                                                       OnClick="()=>Delete(context)"
                                                       Class="pa-1" />
                                    </Authorized>
                                </AuthorizeView>
                            </div>
                        </MudTd>
                    </RowTemplate>

                    <PagerContent>
                        <MudTablePager RowsPerPageString="عدد الصفوف :"
                                       InfoFormat="{last_item} -- {all_items}"
                                       HorizontalAlignment="HorizontalAlignment.Center"
                                       Class="pa-4" />
                    </PagerContent>
                </MudTable>
            </MudPaper>
        </MudContainer>
    </Authorized>

    <NotAuthorized>
        <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Class="ma-4">
            ليس لديك صلاحية الوصول
        </MudAlert>
    </NotAuthorized>
</AuthorizeView>

<MudMessageBox @ref="mbox"
               Title="تأكيد الحذف"
               CancelText="إلغاء"
               Class="rounded-lg">
    <MessageContent>
        <div class="d-flex flex-column gap-4">
            <MudIcon Icon="@Icons.Material.Filled.Warning"
                     Color="Color.Warning"
                     Size="Size.Large"
                     Class="mx-auto" />
            <MudText Align="Align.Center">
                هل أنت متأكد من حذف <b>@NameItemForDelete</b> ؟
            </MudText>
        </div>
    </MessageContent>
    <YesButton>
        <MudButton Variant="Variant.Filled"
                   Color="Color.Error"
                   StartIcon="@Icons.Material.Filled.DeleteForever"
                   Size="Size.Large">
            تأكيد الحذف
        </MudButton>
    </YesButton>
</MudMessageBox>

<style>
    .desktop {
        height: calc(100vh - 64px);
        width: 100%;
        overflow: hidden;
    }

    .mud-table {
        border-radius: 8px;
    }

    .mud-table-container {
        overflow: hidden;
    }

    .mud-table-head {
        background-color: var(--mud-palette-background-grey);
    }

    .mud-table-cell {
        font-size: 0.875rem;
    }

    .mud-button {
        text-transform: none;
    }

    .mud-icon-button {
        transition: transform 0.2s ease;
    }

        .mud-icon-button:hover {
            transform: scale(1.1);
        }

    /* تحسينات للمخزن Autocomplete */
    .store-autocomplete .mud-input-control {
        direction: rtl;
    }

    .store-autocomplete .mud-input-control-input-container {
        direction: rtl;
    }

    .store-autocomplete .mud-autocomplete {
        direction: rtl;
    }

    .store-autocomplete .mud-list {
        direction: rtl;
    }

    .store-autocomplete .mud-list-item {
        direction: rtl;
        text-align: right;
    }

    /* تحسين التخطيط للشاشات الصغيرة */
    @@media (max-width: 1280px) {
        .toolbar-content {
            flex-wrap: wrap;
            gap: 8px !important;
        }

        .toolbar-content .mud-autocomplete,
        .toolbar-content .mud-textfield {
            min-width: 180px !important;
        }
    }

    @@media (max-width: 960px) {
        .toolbar-content {
            flex-direction: column;
            align-items: stretch;
        }

        .toolbar-content .mud-autocomplete,
        .toolbar-content .mud-textfield,
        .toolbar-content .mud-button {
            width: 100%;
            margin: 4px 0 !important;
        }
    }
</style>
