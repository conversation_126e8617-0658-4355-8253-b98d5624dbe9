﻿using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;

namespace PosGTech.DataAccess.Repository
{
    public class UserTreasuryRepository : Repository<UserTreasury>, IUserTreasuryRepository
    {
        public UserTreasuryRepository(ApplicationDbContext db) : base(db)
        {
        }
        public async Task UpdateUserTreasuryBalanceDec(Guid userTreasuryId, decimal newBalance)
        {
            var userTreasury = await GetByIdAsync(userTreasuryId);
            userTreasury.Balance -= newBalance;
            userTreasury.UpdatedAt = DateTime.Now;
            Update(userTreasury);
        }
        public async Task UpdateUserTreasuryBalanceInc(Guid userTreasuryId, decimal newBalance)
        {
            var userTreasury = await GetByIdAsync(userTreasuryId);
            userTreasury.Balance += newBalance;
            userTreasury.UpdatedAt = DateTime.Now;
            Update(userTreasury);
        }
        public bool CheckUserTreasuryBelowZero() => GetAll().Result.Any(x => x.Balance < 0);
    }
}
