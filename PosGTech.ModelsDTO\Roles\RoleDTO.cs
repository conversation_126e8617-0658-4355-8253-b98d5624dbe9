using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Roles
{
    /// <summary>
    /// نموذج بيانات الدور مع الصلاحيات
    /// </summary>
    public class RoleDTO
    {
        /// <summary>
        /// معرف الدور
        /// </summary>
        public Guid? Id { get; set; }

        /// <summary>
        /// اسم الدور
        /// </summary>
        [Required(ErrorMessage = "اسم الدور مطلوب")]
        [StringLength(50, ErrorMessage = "اسم الدور يجب ألا يتجاوز 50 حرف")]
        public string Name { get; set; }

        /// <summary>
        /// وصف الدور
        /// </summary>
        [StringLength(200, ErrorMessage = "وصف الدور يجب ألا يتجاوز 200 حرف")]
        public string? Description { get; set; }

        /// <summary>
        /// صلاحيات الدور
        /// </summary>
        [Required(ErrorMessage = "يجب اختيار صلاحية واحدة على الأقل")]
        public List<string> Permissions { get; set; } = new List<string>();

        /// <summary>
        /// عدد المستخدمين المخصصين لهذا الدور
        /// </summary>
        public int UsersCount { get; set; }

        /// <summary>
        /// تاريخ إنشاء الدور
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// تاريخ آخر تعديل
        /// </summary>
        public DateTime? LastModified { get; set; }

        /// <summary>
        /// هل هذا دور أساسي في النظام (غير قابل للحذف)
        /// </summary>
        public bool IsSystemRole { get; set; }

        /// <summary>
        /// هل الدور نشط
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// المستخدم الذي أنشأ الدور
        /// </summary>
        public Guid? CreatedBy { get; set; }

        /// <summary>
        /// المستخدم الذي عدل الدور آخر مرة
        /// </summary>
        public Guid? ModifiedBy { get; set; }
    }

    /// <summary>
    /// نموذج بيانات لتحديث صلاحيات الدور
    /// </summary>
    public class UpdateRolePermissionsDTO
    {
        /// <summary>
        /// معرف الدور
        /// </summary>
        [Required(ErrorMessage = "معرف الدور مطلوب")]
        public Guid RoleId { get; set; }

        /// <summary>
        /// الصلاحيات الجديدة للدور
        /// </summary>
        [Required(ErrorMessage = "يجب اختيار صلاحية واحدة على الأقل")]
        public List<string> Permissions { get; set; } = new List<string>();

        /// <summary>
        /// المستخدم الذي يقوم بالتحديث
        /// </summary>
        public Guid? ModifiedBy { get; set; }
    }

    /// <summary>
    /// نموذج بيانات لعرض تفاصيل الدور مع المستخدمين
    /// </summary>
    public class RoleDetailsDTO
    {
        /// <summary>
        /// معلومات الدور الأساسية
        /// </summary>
        public RoleDTO Role { get; set; }

        /// <summary>
        /// قائمة المستخدمين المخصصين لهذا الدور
        /// </summary>
        public List<RoleUserDTO> Users { get; set; } = new List<RoleUserDTO>();

        /// <summary>
        /// الصلاحيات مجمعة حسب المجموعات
        /// </summary>
        public Dictionary<string, List<string>> PermissionGroups { get; set; } = new Dictionary<string, List<string>>();
    }

    /// <summary>
    /// نموذج بيانات للمستخدم في الدور
    /// </summary>
    public class RoleUserDTO
    {
        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// الاسم الكامل
        /// </summary>
        public string FullName { get; set; }

        /// <summary>
        /// تاريخ إضافة المستخدم للدور
        /// </summary>
        public DateTime AssignedDate { get; set; }

        /// <summary>
        /// هل المستخدم نشط
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// عدد الصلاحيات الإضافية للمستخدم
        /// </summary>
        public int AdditionalPermissionsCount { get; set; }

        /// <summary>
        /// عدد الصلاحيات المحذوفة من الدور
        /// </summary>
        public int RemovedPermissionsCount { get; set; }
    }

    /// <summary>
    /// نموذج بيانات لإحصائيات الأدوار
    /// </summary>
    public class RoleStatisticsDTO
    {
        /// <summary>
        /// إجمالي عدد الأدوار
        /// </summary>
        public int TotalRoles { get; set; }

        /// <summary>
        /// عدد الأدوار النشطة
        /// </summary>
        public int ActiveRoles { get; set; }

        /// <summary>
        /// عدد الأدوار الأساسية
        /// </summary>
        public int SystemRoles { get; set; }

        /// <summary>
        /// عدد الأدوار المخصصة
        /// </summary>
        public int CustomRoles { get; set; }

        /// <summary>
        /// إجمالي عدد المستخدمين
        /// </summary>
        public int TotalUsers { get; set; }

        /// <summary>
        /// الأدوار الأكثر استخداماً
        /// </summary>
        public List<RoleUsageDTO> MostUsedRoles { get; set; } = new List<RoleUsageDTO>();
    }

    /// <summary>
    /// نموذج بيانات لاستخدام الدور
    /// </summary>
    public class RoleUsageDTO
    {
        /// <summary>
        /// اسم الدور
        /// </summary>
        public string RoleName { get; set; }

        /// <summary>
        /// عدد المستخدمين
        /// </summary>
        public int UsersCount { get; set; }

        /// <summary>
        /// النسبة المئوية من إجمالي المستخدمين
        /// </summary>
        public decimal Percentage { get; set; }
    }
}
