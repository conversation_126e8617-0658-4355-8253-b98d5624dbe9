﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول تصنيفات المنتجات التي تباع
    /// </summary>
    [Index("Name", IsUnique = true)]
    public class Category : BaseEntity
    {
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        [StringLength(100, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
        public string Name { get; set; }
        public ICollection<Item>? Items { get; set; }
    }
}
