﻿using Blazored.LocalStorage;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.DependencyInjection;
using MudBlazor;
using MudBlazor.Services;
using MudExtensions.Services;
using PosGTech.ModelsDTO.Authorization;
using PosGTech.Web.Services;
using PosGTech.Web.Services.Contracts;
using PosGTech.Web.Services.Implementations;
using PosGTech.Web.Services.Interfaces;
using PosGTech.Web.Services.Repositories;
namespace PosGTech.Web.Pages;

public static class WebPages
{
    public static IServiceCollection AddPages(this IServiceCollection services)
    {
        services.AddScoped(typeof(IGRepository<>), typeof(GRepository<>));
        //services.AddScoped(sp => new HttpClient { BaseAddress = new Uri("https://posgtechapi.runasp.net/api/") });
        services.AddScoped(sp => new HttpClient
        {
            BaseAddress = new Uri("https://localhost:7282/api/"),
            Timeout = TimeSpan.FromSeconds(30) // تكوين timeout مناسب
        });
        services.AddBlazoredLocalStorage();
        services.AddMudServices(config =>
        {
            config.SnackbarConfiguration.PositionClass = Defaults.Classes.Position.BottomLeft;
            config.SnackbarConfiguration.PreventDuplicates = false;
            config.SnackbarConfiguration.NewestOnTop = false;
            config.SnackbarConfiguration.ShowCloseIcon = true;
            config.SnackbarConfiguration.VisibleStateDuration = 10000;
            config.SnackbarConfiguration.HideTransitionDuration = 500;
            config.SnackbarConfiguration.ShowTransitionDuration = 500;
            config.SnackbarConfiguration.SnackbarVariant = Variant.Filled;
        });

        // إعداد نظام التفويض المبني على الصلاحيات
        services.AddAuthorizationCore(options =>
        {
            // سياسة عامة للتحقق من وجود أي صلاحية
            options.AddPolicy("permission", policy =>
                policy.Requirements.Add(new PermissionRequirement("any")));

            // إضافة سياسة لكل صلاحية في النظام
            foreach (var permission in PermissionConstants.GetAllPermissions())
            {
                options.AddPolicy(permission, policy =>
                    policy.Requirements.Add(new PermissionRequirement(permission)));
            }
        });

        // تسجيل معالج الصلاحيات للـ WebAssembly
        services.AddScoped<IAuthorizationHandler, PermissionHandlerWebAssembly>();

        services.AddMudExtensions();
        services.AddScoped<IdentityAuthenticationStateProvider>();
        services.AddScoped<AuthenticationStateProvider>(s => s.GetRequiredService<IdentityAuthenticationStateProvider>());
        services.AddScoped<IAuthorizeApi, AuthorizeApi>();

        // Error handling services
        services.AddScoped<IErrorLoggingService, ErrorLoggingService>();
        services.AddScoped<IErrorHandlingService, ErrorHandlingService>();
        services.AddSingleton<GlobalExceptionHandler>();

        // Inventory validation services
        services.AddScoped<IInventoryValidationService, InventoryValidationService>();

        return services;
    }
}
