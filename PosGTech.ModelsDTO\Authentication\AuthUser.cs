﻿namespace PosGTech.ModelsDTO.Authentication;

public class AuthUser
{
    public string? Message { get; set; }
    public bool isAuthenticated { get; set; }
    public string? UserName { get; set; }
    public string? Password { get; set; }
    public string? Email { get; set; }
    public List<string>? Roles { get; set; }
    public string? Token { get; set; }
    public DateTime? ExpiresOn { get; set; }
    public string? RefreshToken { get; set; }
    public DateTime RefreshTokenExpiration { get; set; }
}
