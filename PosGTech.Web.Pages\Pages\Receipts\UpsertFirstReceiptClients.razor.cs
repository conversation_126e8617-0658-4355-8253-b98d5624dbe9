﻿using Microsoft.AspNetCore.Components;
using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.Receipts;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Receipts;

public partial class UpsertFirstReceiptClients
{

    IEnumerable<ClientCMDTO> Clients = new List<ClientCMDTO>();
    [Inject]
    IGRepository<ClientCMDTO> _Client { get; set; }

    [Parameter]
    public EventCallback<ReceiptDTO> ReceiptChanged { get; set; }
    [Parameter]
    public ReceiptDTO Receipt
    {
        get => _receipt;
        set
        {
            if (value == _receipt)
                return;

            _receipt = value;
            if (ReceiptChanged.HasDelegate)
            {
                ReceiptChanged.InvokeAsync(_receipt);
            }
        }
    }
    private ReceiptDTO _receipt;
    protected override async Task OnInitializedAsync()
    {
        var resClient = await _Client.GetAll("Clients/getAllSuppliers");
        if (resClient.response == null) Clients = resClient.list;
        Receipt.IsExchange = null;
    }
    private async Task<IEnumerable<ClientCMDTO>> SearchClient(string value, CancellationToken token)
    {
        if (string.IsNullOrEmpty(value))
            return Clients;
        return Clients.Where(x => x.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase));
    }


}