﻿using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Employees;

namespace PosGTech.DataAccess.Repository
{
    public class EmployeeRepository : Repository<Employee>, IEmployeeRepository
    {
        public EmployeeRepository(ApplicationDbContext db) : base(db)
        {
        }
        public void UpdateEmployee(Employee oldEmployee, EmployeeDTO newEmployee)
        {
            oldEmployee.Name = newEmployee.Name;
            oldEmployee.UpdatedAt = DateTime.Now;
            Update(oldEmployee);
        }

        public async Task UpdateEmployeeBalanceDec(Guid employeeId, decimal newBalance)
        {
            var employee = await GetByIdAsync(employeeId);
            employee.Balance -= newBalance;
            employee.UpdatedAt = DateTime.Now;
            Update(employee);
        }
        public async Task UpdateEmployeeBalanceInc(Guid employeeId, decimal newBalance)
        {
            var employee = await GetByIdAsync(employeeId);
            employee.Balance += newBalance;
            employee.UpdatedAt = DateTime.Now;
            Update(employee);
        }
    }
}
