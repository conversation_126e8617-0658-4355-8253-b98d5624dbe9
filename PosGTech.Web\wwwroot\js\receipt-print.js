// Receipt Printing Functions using Print.js
// Supports Arabic/RTL layout and responsive design for A4, A5, and 80mm thermal printers

console.log('Receipt Print JS loaded successfully');

window.receiptPrint = {
    // Print receipt using Print.js with responsive design
    printReceipt: function(receiptHtml, printType = 'auto') {
        try {
            // Create a temporary container for the receipt
            const tempContainer = document.createElement('div');
            tempContainer.innerHTML = receiptHtml;
            tempContainer.style.display = 'none';
            document.body.appendChild(tempContainer);

            // Determine print type and apply appropriate styles
            const printOptions = this.getPrintOptions(printType);
            
            // Use Print.js to print the receipt
            printJS({
                printable: tempContainer.innerHTML,
                type: 'html',
                ...printOptions,
                onPrintDialogClose: function() {
                    // Clean up temporary container
                    document.body.removeChild(tempContainer);
                }
            });

            return true;
        } catch (error) {
            console.error('Error printing receipt:', error);
            return false;
        }
    },

    // Get print options based on print type
    getPrintOptions: function(printType) {
        const baseOptions = {
            documentTitle: 'إيصال',
            style: this.getReceiptStyles(printType),
            scanStyles: false,
            honorMarginPadding: true,
            honorColor: true,
            css: [
                'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap'
            ]
        };

        // جميع الطباعات ستكون بتنسيق A4
        return {
            ...baseOptions,
            targetStyles: ['*'],
            ignoreElements: []
        };
    },

    // Get CSS styles for different print formats
    getReceiptStyles: function(printType) {
        const commonStyles = `
            @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

            * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            body {
                font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                direction: rtl;
                text-align: right;
                color: #1e293b;
                line-height: 1.5;
                background: #fefefe;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .modern-receipt, .receipt-container {
                width: 190mm;
                min-height: 135mm;
                max-height: 148mm;
                margin: 10mm auto;
                background: #ffffff;
                border: 3px solid #000;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                direction: rtl;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                padding: 8mm;
            }

            .receipt-top-bar {
                height: 8px;
                background: linear-gradient(90deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
            }

            .receipt-header {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                padding: 20px 30px;
                position: relative;
                border-bottom: 3px solid #1e40af;
            }

            .header-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
            }

            .logo-section {
                display: flex;
                align-items: center;
                gap: 15px;
            }

            .company-logo {
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 24px;
                font-weight: bold;
                box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
            }

            .company-info {
                text-align: right;
            }

            .company-name {
                font-size: 18px;
                font-weight: 700;
                color: #1e293b;
                margin-bottom: 5px;
            }

            .company-details {
                font-size: 12px;
                color: #64748b;
                line-height: 1.4;
            }

            .receipt-title {
                text-align: center;
                font-size: 24px;
                font-weight: 700;
                color: #1e40af;
                margin: 15px 0;
                text-shadow: 0 2px 4px rgba(30, 64, 175, 0.1);
            }

            .date-section {
                position: absolute;
                top: 20px;
                left: 30px;
                background: #1e40af;
                color: white;
                padding: 8px 15px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                box-shadow: 0 2px 8px rgba(30, 64, 175, 0.3);
            }

            .receipt-body {
                padding: 30px;
                background: #ffffff;
            }

            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 25px;
            }

            .info-card {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 15px;
                transition: all 0.3s ease;
            }

            .info-card:hover {
                background: #f1f5f9;
                border-color: #cbd5e1;
            }

            .info-label {
                font-size: 12px;
                color: #64748b;
                font-weight: 500;
                margin-bottom: 5px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .info-value {
                font-size: 16px;
                color: #1e293b;
                font-weight: 600;
            }

            .statement-section {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 20px;
                margin: 20px 0;
            }

            .statement-label {
                font-size: 14px;
                color: #64748b;
                font-weight: 600;
                margin-bottom: 10px;
            }

            .statement-value {
                font-size: 16px;
                color: #1e293b;
                line-height: 1.6;
                background: white;
                padding: 15px;
                border-radius: 6px;
                border: 1px solid #e2e8f0;
            }

            .amount-section {
                background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
                color: white;
                padding: 25px;
                border-radius: 12px;
                text-align: center;
                margin: 25px 0;
                box-shadow: 0 4px 20px rgba(30, 64, 175, 0.3);
            }

            .amount-label {
                font-size: 16px;
                font-weight: 500;
                margin-bottom: 10px;
                opacity: 0.9;
            }

            .amount-value {
                font-size: 32px;
                font-weight: 800;
                margin-bottom: 8px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            .amount-words {
                font-size: 14px;
                opacity: 0.8;
                font-style: italic;
            }

            .receipt-footer {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                padding: 20px 30px;
                border-top: 3px solid #1e40af;
                text-align: center;
            }

            .footer-message {
                font-size: 16px;
                font-weight: 600;
                color: #1e40af;
                margin-bottom: 10px;
            }

            .footer-details {
                font-size: 12px;
                color: #64748b;
                line-height: 1.5;
            }

            .print-info {
                margin-top: 15px;
                padding-top: 15px;
                border-top: 1px dotted #cbd5e1;
                font-size: 11px;
                color: #94a3b8;
            }

            /* تنسيقات الطباعة */
            @media print {
                * {
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                body {
                    margin: 0 !important;
                    padding: 0 !important;
                    background: white !important;
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                .modern-receipt, .receipt-container {
                    width: 190mm !important;
                    min-height: 135mm !important;
                    max-height: 148mm !important;
                    margin: 10mm auto !important;
                    padding: 8mm !important;
                    background: white !important;
                    box-shadow: none !important;
                    border-radius: 0 !important;

                    /* حواف قوية ومرئية */
                    border: 4px solid #000000 !important;
                    border-left: 4px solid #000000 !important;
                    border-right: 4px solid #000000 !important;
                    border-top: 4px solid #000000 !important;
                    border-bottom: 4px solid #000000 !important;

                    /* إضافة outline كطبقة إضافية */
                    outline: 2px solid #000000 !important;
                    outline-offset: -2px !important;

                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }

                .receipt-top-bar {
                    background: #000 !important;
                }

                .receipt-header {
                    background: #f5f5f5 !important;
                    border-bottom: 2px solid #000 !important;
                }

                .company-logo {
                    background: #000 !important;
                }

                .date-section {
                    background: #000 !important;
                }

                .amount-section {
                    background: #000 !important;
                }

                .receipt-footer {
                    background: #f5f5f5 !important;
                    border-top: 2px solid #000 !important;
                }

                @page {
                    size: A4 portrait !important;
                    margin: 5mm !important;
                }
            }

            /* دعم الوضع الداكن */
            @media (prefers-color-scheme: dark) {
                body {
                    background: #1a1a1a;
                    color: #e9ecef;
                }

                .modern-receipt {
                    background: #2d3748;
                    border: 2px solid #4a5568;
                    color: #e9ecef;
                }

                .receipt-header {
                    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
                    border-bottom: 3px solid #60a5fa;
                }

                .company-name {
                    color: #f7fafc;
                }

                .company-details {
                    color: #cbd5e0;
                }

                .receipt-title {
                    color: #60a5fa;
                }

                .date-section {
                    background: #60a5fa;
                }

                .info-card {
                    background: #374151;
                    border: 1px solid #4b5563;
                }

                .info-label {
                    color: #e2e8f0;
                }

                .info-value {
                    color: #f7fafc;
                }

                .statement-section {
                    background: #374151;
                    border: 1px solid #4b5563;
                }

                .statement-value {
                    background: #2d3748;
                    border: 1px solid #4b5563;
                    color: #f7fafc;
                }

                .amount-section {
                    background: linear-gradient(135deg, #1e40af 0%, #60a5fa 100%);
                    border: 1px solid #6b7280;
                    color: #f7fafc;
                }

                .receipt-footer {
                    background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
                    border-top: 3px solid #60a5fa;
                    color: #cbd5e0;
                }
            }

            /* تنسيقات الطباعة الحرارية */
            @media print and (max-width: 80mm) {
                .modern-receipt {
                    font-size: 12px;
                }

                .info-grid {
                    grid-template-columns: 1fr;
                    gap: 10px;
                }

                .header-content {
                    flex-direction: column;
                    text-align: center;
                }

                .company-logo {
                    width: 60px;
                    height: 60px;
                    font-size: 18px;
                }

                .receipt-title {
                    font-size: 18px;
                }

                .amount-value {
                    font-size: 24px;
                }
            }
        `;

        switch (printType.toLowerCase()) {
            case 'thermal':
            case '80mm':
                return commonStyles + `
                    .modern-receipt {
                        width: 72mm;
                        max-width: 72mm;
                        font-size: 11px;
                        padding: 5px;
                        border-radius: 0;
                    }

                    .receipt-header {
                        padding: 10px 15px;
                    }

                    .company-logo {
                        width: 50px;
                        height: 50px;
                        font-size: 16px;
                    }

                    .company-name {
                        font-size: 14px;
                    }

                    .company-details {
                        font-size: 9px;
                    }

                    .receipt-title {
                        font-size: 16px;
                        margin: 10px 0;
                    }

                    .date-section {
                        position: static;
                        display: inline-block;
                        margin-bottom: 10px;
                        font-size: 11px;
                        padding: 5px 10px;
                    }

                    .receipt-body {
                        padding: 15px;
                    }

                    .info-grid {
                        grid-template-columns: 1fr;
                        gap: 8px;
                        margin-bottom: 15px;
                    }

                    .info-card {
                        padding: 8px;
                    }

                    .info-label {
                        font-size: 10px;
                    }

                    .info-value {
                        font-size: 12px;
                    }

                    .amount-section {
                        padding: 15px;
                        margin: 15px 0;
                    }

                    .amount-label {
                        font-size: 12px;
                    }

                    .amount-value {
                        font-size: 20px;
                    }

                    .amount-words {
                        font-size: 10px;
                    }

                    .receipt-footer {
                        padding: 10px 15px;
                    }

                    .footer-message {
                        font-size: 12px;
                    }

                    .footer-details {
                        font-size: 9px;
                    }

                    .print-info {
                        font-size: 8px;
                    }

                    @media print {
                        @page {
                            size: 80mm auto;
                            margin: 0;
                        }

                        body {
                            margin: 0;
                            padding: 0;
                        }
                    }
                `;
            
            case 'a5':
                return commonStyles + `
                    .modern-receipt {
                        width: 140mm;
                        max-width: 140mm;
                        font-size: 12px;
                        padding: 8px;
                    }

                    .receipt-header {
                        padding: 15px 20px;
                    }

                    .company-logo {
                        width: 60px;
                        height: 60px;
                        font-size: 18px;
                    }

                    .company-name {
                        font-size: 16px;
                    }

                    .receipt-title {
                        font-size: 20px;
                    }

                    .receipt-body {
                        padding: 20px;
                    }

                    .amount-value {
                        font-size: 26px;
                    }

                    @media print {
                        @page {
                            size: A5;
                            margin: 10mm;
                        }
                    }
                `;

            case 'a4':
            default:
                return commonStyles + `
                    .modern-receipt {
                        width: 180mm;
                        max-width: 180mm;
                        font-size: 13px;
                        padding: 15px;
                    }

                    .receipt-header {
                        padding: 25px 35px;
                    }

                    .company-logo {
                        width: 90px;
                        height: 90px;
                        font-size: 28px;
                    }

                    .company-name {
                        font-size: 20px;
                    }

                    .receipt-title {
                        font-size: 28px;
                    }

                    .receipt-body {
                        padding: 35px;
                    }

                    .amount-value {
                        font-size: 36px;
                    }
                    
                    @media print {
                        @page {
                            size: A4;
                            margin: 15mm;
                        }
                    }
                `;
        }
    },

    // Auto-detect print format based on screen size or user preference
    detectPrintFormat: function() {
        const screenWidth = window.innerWidth;
        
        if (screenWidth <= 480) {
            return 'thermal';
        } else if (screenWidth <= 768) {
            return 'a5';
        } else {
            return 'a4';
        }
    },

    // Preview receipt before printing
    previewReceipt: function(receiptHtml, printType = 'auto') {
        try {
            const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
            const styles = this.getReceiptStyles(printType);

            previewWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>معاينة الإيصال</title>
                    <style>
                        ${styles}
                        @media print {
                            * {
                                -webkit-print-color-adjust: exact !important;
                                print-color-adjust: exact !important;
                                color-adjust: exact !important;
                            }
                            body { margin: 0 !important; padding: 0 !important; background: white !important; }
                            .modern-receipt, .receipt-container {
                                width: 190mm !important;
                                min-height: 135mm !important;
                                max-height: 148mm !important;
                                margin: 10mm auto !important;
                                padding: 8mm !important;
                                background: white !important;

                                border: 4px solid #000000 !important;
                                border-left: 4px solid #000000 !important;
                                border-right: 4px solid #000000 !important;
                                border-top: 4px solid #000000 !important;
                                border-bottom: 4px solid #000000 !important;

                                outline: 2px solid #000000 !important;
                                outline-offset: -2px !important;
                            }
                            .preview-actions { display: none !important; }
                            @page { size: A4 portrait !important; margin: 5mm !important; }
                        }
                    </style>
                </head>
                <body>
                    ${receiptHtml}
                    <div class="preview-actions" style="text-align: center; margin-top: 30px; padding: 20px; border-top: 3px solid #1e40af; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
                        <button onclick="window.print()" style="padding: 12px 25px; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; border: none; border-radius: 8px; cursor: pointer; font-family: 'Cairo', Arial, sans-serif; font-weight: 600; margin: 0 10px; box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3); transition: all 0.3s ease;">🖨️ طباعة</button>
                        <button onclick="window.close()" style="padding: 12px 25px; background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%); color: white; border: none; border-radius: 8px; cursor: pointer; margin: 0 10px; font-family: 'Cairo', Arial, sans-serif; font-weight: 600; box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3); transition: all 0.3s ease;">❌ إغلاق</button>
                    </div>
                </body>
                </html>
            `);

            previewWindow.document.close();
            return true;
        } catch (error) {
            console.error('Error previewing receipt:', error);
            return false;
        }
    },

    // Fallback print method using window.print()
    fallbackPrint: function(receiptHtml, printType = 'auto') {
        try {
            const printWindow = window.open('', '_blank', 'width=800,height=600');
            const styles = this.getReceiptStyles(printType);

            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>طباعة الإيصال</title>
                    <style>
                        ${styles}
                        @media print {
                            * {
                                -webkit-print-color-adjust: exact !important;
                                print-color-adjust: exact !important;
                                color-adjust: exact !important;
                            }
                            body { margin: 0 !important; padding: 0 !important; background: white !important; }
                            .modern-receipt, .receipt-container {
                                width: 190mm !important;
                                min-height: 135mm !important;
                                max-height: 148mm !important;
                                margin: 10mm auto !important;
                                padding: 8mm !important;
                                background: white !important;

                                border: 4px solid #000000 !important;
                                border-left: 4px solid #000000 !important;
                                border-right: 4px solid #000000 !important;
                                border-top: 4px solid #000000 !important;
                                border-bottom: 4px solid #000000 !important;

                                outline: 2px solid #000000 !important;
                                outline-offset: -2px !important;
                            }
                            @page { size: A4 portrait !important; margin: 5mm !important; }
                        }
                    </style>
                </head>
                <body>
                    ${receiptHtml}
                </body>
                </html>
            `);

            printWindow.document.close();

            // انتظار تحميل المحتوى ثم الطباعة
            printWindow.onload = function() {
                setTimeout(() => {
                    printWindow.print();
                    printWindow.close();
                }, 500);
            };

            return true;
        } catch (error) {
            console.error('Error in fallback print:', error);
            return false;
        }
    }
};

// Utility function for Blazor integration
window.printReceiptFromBlazor = function(receiptHtml, printType = 'auto') {
    try {
        // التحقق من وجود مكتبة Print.js
        if (typeof printJS !== 'undefined') {
            return window.receiptPrint.printReceipt(receiptHtml, printType);
        } else {
            // استخدام طريقة بديلة إذا لم تكن Print.js متاحة
            return window.receiptPrint.fallbackPrint(receiptHtml, printType);
        }
    } catch (error) {
        console.error('Error in printReceiptFromBlazor:', error);
        return false;
    }
};

window.previewReceiptFromBlazor = function(receiptHtml, printType = 'auto') {
    try {
        return window.receiptPrint.previewReceipt(receiptHtml, printType);
    } catch (error) {
        console.error('Error in previewReceiptFromBlazor:', error);
        return false;
    }
};

// تأكيد تحميل جميع الدوال
console.log('Receipt Print functions loaded:', {
    printReceiptFromBlazor: typeof window.printReceiptFromBlazor,
    previewReceiptFromBlazor: typeof window.previewReceiptFromBlazor,
    receiptPrint: typeof window.receiptPrint
});
