﻿using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components.Authorization;
using PosGTech.ModelsDTO.Authentication;
using PosGTech.Web.Services.Contracts;
using System.Security.Claims;

namespace PosGTech.Web.Services
{
    public class IdentityAuthenticationStateProvider : AuthenticationStateProvider
    {
        private readonly IAuthorizeApi _authorizeApi;
        ILocalStorageService _localStorage;
        public IdentityAuthenticationStateProvider(IAuthorizeApi authorizeApi, ILocalStorageService localStorage)
        {
            this._authorizeApi = authorizeApi;
            _localStorage = localStorage;
        }

        public async Task<AuthUser> Login(LoginUser loginParameters)
        {
            var result = await _authorizeApi.Login(loginParameters);
            if (result.isAuthenticated)
                NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
            return result;
        }
        public async Task Logout()
        {
            await _authorizeApi.Logout();
            NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
        }
        public void SetToken()
        {
            NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
        }


        public override async Task<AuthenticationState> GetAuthenticationStateAsync()
        {
            var identity = new ClaimsIdentity();
            try
            {
                identity = await _authorizeApi.GetClaims();
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine("Request failed:" + ex.ToString());
            }

            return new AuthenticationState(new ClaimsPrincipal(identity));
        }


    }
}
