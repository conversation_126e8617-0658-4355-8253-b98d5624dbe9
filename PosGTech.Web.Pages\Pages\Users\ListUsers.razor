﻿@page "/listUsers"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<AuthorizeView Policy="@PermissionConstants.UsersView" Context="auth">
    <Authorized>
        <MudPaper Class="d-flex flex-column justify-space-evenly desktop rounded-0" Elevation="0">

    <MudTable Items="@users" Height="calc(100vh - 170px)" Loading="loading" Breakpoint="Breakpoint.Sm" Filter="new Func<UserDTO,bool>(FilterFunc1)" @bind-SelectedItem="selectedItem" Virtualize="true" Striped="true" Dense="true" FixedHeader="true" Elevation="4">

        <ToolBarContent>
            <MudText Align="@Align.Justify" Typo="Typo.h6" Style="margin-top:24px; margin-left:48px;">المستخدمين</MudText>
            <MudTextField @bind-Value="Search" Placeholder="بحث" Adornment="Adornment.End" AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium" Class="mt-0"></MudTextField>
            <MudSpacer />
            <MudButton Variant="Variant.Filled" EndIcon="@Icons.Material.Filled.Add" OnClick="()=>Upsert(Guid.Empty)" Color="Color.Primary">جديد</MudButton>
        </ToolBarContent>

        <HeaderContent>
            <MudTh Class="rounded-0">ت</MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<UserDTO, object>(x=>x.Name)">الاسم</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<UserDTO, object>(x=>x.UserName)">اسم المستخدم</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<UserDTO, object>(x=>x.RoleName)">الدور</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<UserDTO, object>(x=>x.State)">الحالة</MudTableSortLabel></MudTh>
            <MudTh Class="rounded-0">الإجراءات</MudTh>
        </HeaderContent>
        <RowTemplate>
            <MudTd DataLabel="ت">@(users.ToList().IndexOf(context) + 1)</MudTd>
            <MudTd DataLabel="الاسم">@context.Name</MudTd>
            <MudTd DataLabel="اسم المستخدم">@context.UserName</MudTd>
            <MudTd DataLabel="الدور">
                <MudChip T="string" Size="Size.Small" Color="Color.Primary">
                    @context.RoleName
                </MudChip>
            </MudTd>
            <MudTd DataLabel="الحالة">
                <MudChip T="string" Size="Size.Small" Color="@(context.State ? Color.Success : Color.Error)">
                    @(context.State ? "نشط" : "موقوف")
                </MudChip>
            </MudTd>
            <MudTd DataLabel="الإجراءات">
                <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                    <MudIconButton Size="Size.Small"
                                   Color="Color.Info"
                                   Icon="@Icons.Material.Filled.Edit"
                                   OnClick="()=>Upsert(context.Id!.Value)"
                                   Title="تعديل المستخدم" />

                    @if (CanDeleteUser(context))
                    {
                        @if (IsUserActive(context))
                        {
                            <MudIconButton Size="Size.Small"
                                           Color="Color.Error"
                                           Icon="@Icons.Material.Filled.Delete"
                                           OnClick="()=>Delete(context)"
                                           Title="حذف المستخدم" />
                        }
                        else
                        {
                            <MudIconButton Size="Size.Small"
                                           Color="Color.Success"
                                           Icon="@Icons.Material.Filled.Restore"
                                           OnClick="()=>Restore(context)"
                                           Title="استعادة المستخدم" />
                        }
                    }
                    else
                    {
                        <MudTooltip Text="لا يمكن حذف الأدمن الرئيسي">
                            <MudIconButton Size="Size.Small"
                                           Color="Color.Default"
                                           Icon="@Icons.Material.Filled.Delete"
                                           Disabled="true" />
                        </MudTooltip>
                    }
                </MudButtonGroup>
            </MudTd>
        </RowTemplate>


            <PagerContent>
                <MudTablePager style="height:24px;" PageSizeOptions="new int[] {int.MaxValue }"
                               RowsPerPageString="عدد الصفوف :"
                               InfoFormat="{last_item} -- {all_items}"
                               HorizontalAlignment="HorizontalAlignment.Center"
                               HideRowsPerPage="true"
                               HidePageNumber="true"
                               HidePagination="true" />
            </PagerContent>
        </MudTable>


    </MudPaper>

    <MudMessageBox @ref="mbox" Title="تنبيه" CancelText="إلغاء">
        <MessageContent>
            هل تريد حذف <b>@NameUserForDelete</b>؟
            <br />
            <small class="text-muted">ملاحظة: سيتم تحديد نوع الحذف تلقائياً حسب أنشطة المستخدم في النظام</small>
        </MessageContent>
        <YesButton>
            <MudButton Variant="Variant.Filled" Color="Color.Error" StartIcon="@Icons.Material.Filled.Delete">حذف</MudButton>
        </YesButton>
    </MudMessageBox>




<style>
    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }

    .permissions-summary {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .permission-chip {
        font-size: 0.75rem;
        height: 20px;
        min-width: 24px;
    }

    .permissions-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        max-height: 300px;
        overflow-y: auto;
        padding: 8px;
    }

    .permissions-grid .mud-chip {
        margin: 2px;
    }
</style>
    </Authorized>
    <NotAuthorized>
        <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Class="ma-4">
            ليس لديك صلاحية الوصول لعرض المستخدمين
        </MudAlert>
    </NotAuthorized>
</AuthorizeView>