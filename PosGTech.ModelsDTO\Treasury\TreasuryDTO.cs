﻿using PosGTech.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PosGTech.ModelsDTO.Treasury
{
    public class TreasuryDTO
    {
        public string Name { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(100, 0, ErrorMessage = "خطا في القيمة")]

        public decimal Tax { get; set; }
        public ICollection<UserTreasury>? UserTreasuries { get; set; }
    }
}
