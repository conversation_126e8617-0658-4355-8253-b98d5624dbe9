﻿@using Blazored.LocalStorage
@inherits LayoutComponentBase
@inject ILocalStorageService _localStorage



<MudRTLProvider RightToLeft="@true">

    <MudThemeProvider IsDarkMode="_switchDark" Theme="_theme" />
    <MudPopoverProvider />
    <MudDialogProvider Position="DialogPosition.Center" CloseOnEscapeKey="true" />
    <MudSnackbarProvider />

    <MudLayout Style="text-align:right">

        <MudAppBar Color="Color.Primary" Fixed="false" Dense="true" Style="padding:0;">
            <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@((e) => DrawerToggle())" />
            <MudSpacer />
            <MudToggleIconButton @bind-Toggled="@_switchDark"
                                 Icon="@Icons.Material.Filled.LightMode" Color="@Color.Error" Title="Off"
                                 ToggledIcon="@Icons.Material.Filled.DarkMode" ToggledColor="@Color.Success" ToggledTitle="On" />
                                 <AuthorizeView>
                                     <Authorized>
                    <MudIconButton Icon="@Icons.Material.Filled.Logout" Color="Color.Error" OnClick="Logout" />

                                     </Authorized>
                                 </AuthorizeView>

        </MudAppBar>

        <MudDrawer @bind-Open="_drawerOpen" Elevation="1" Variant="@DrawerVariant.Temporary">
            <MudDrawerHeader>
                <MudText Typo="Typo.h6">نظام مبيعات</MudText>
            </MudDrawerHeader>
            <NavMenu />
        </MudDrawer>

        <div class="FitHeightUtile">
            <CustomErrorBoundary>
                @Body

            </CustomErrorBoundary>

        </div>

    </MudLayout>
</MudRTLProvider>
<MudMessageBox Class="px-6" @ref="mbox" Title="تنبيه" CancelText="لا">
    <MessageContent>هل تريد الخروج؟&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</MessageContent>
    <YesButton>
        <MudButton class="mr-12" Variant="Variant.Filled" Color="Color.Error" EndIcon="@Icons.Material.Filled.Logout">نعم</MudButton>
    </YesButton>
</MudMessageBox>


<style>
    .FitHeightUtile {
        min-height: calc(100vh - 66px);
    }
</style>
@code {
    bool _drawerOpen = true;
    bool _switchDark = true;
    MudMessageBox mbox { get; set; }
    private readonly MudTheme _theme = new()
        {


            LayoutProperties = new LayoutProperties()
            {
                DefaultBorderRadius = "24px",

            },

            PaletteLight = new PaletteLight
            {
                Primary = "#1E88E5",
                TextPrimary = "#467fff", // Royal blue text color
                Secondary = "#ff4081",
                Background = "#f5f5f5",
                Surface = "#ffffff",
                DrawerBackground = "#ffffff",
                AppbarBackground = "#1E88E5",
            },
            PaletteDark = new PaletteDark
            {
                Primary = "#2196f3",
                TextPrimary = "#ffffff",
                Secondary = "#f50057",
                Background = "#1a1a1a",
                Surface = "#242424",
                DrawerBackground = "#1a1a1a",
                AppbarBackground = "#0d47a1",
            }

        };

    void DrawerToggle()
    {
        _drawerOpen = !_drawerOpen;
    }
    async void Logout()
    {
        bool? result = await mbox.ShowAsync();
        if (result == true)
        {
            await _localStorage.RemoveItemAsync("authToken");
            _navigation.NavigateTo("/login",true);
        }
    }

}