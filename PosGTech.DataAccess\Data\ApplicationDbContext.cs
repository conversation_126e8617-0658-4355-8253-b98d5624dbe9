﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using PosGTech.Models;

namespace PosGTech.DataAccess.Data;
public class ApplicationDbContext : IdentityDbContext<
    User, Role, Guid,
        IdentityUserClaim<Guid>, UserRole, IdentityUserLogin<Guid>,
        IdentityRoleClaim<Guid>, IdentityUserToken<Guid>
    >
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {

    }
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        foreach (var foreignKey in modelBuilder.Model.GetEntityTypes().SelectMany(e => e.GetForeignKeys()))
        {
            foreignKey.DeleteBehavior = DeleteBehavior.NoAction;
        }
        modelBuilder.Entity<ItemUnit>(entity =>
        {
            entity.HasOne(d => d.Item).WithMany(p => p.ItemUnits)
                .HasForeignKey(d => d.ItemId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        modelBuilder.Entity<StoreItem>(entity =>
        {
            entity.HasOne(d => d.Item).WithMany(p => p.StoreItems)
                .HasForeignKey(d => d.ItemId)
                .OnDelete(DeleteBehavior.Cascade);
            entity.HasOne(d => d.Store).WithMany(p => p.StoreItems)
                .HasForeignKey(d => d.StoreId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        modelBuilder.Entity<StoreItemExp>(entity =>
        {
            entity.HasOne(d => d.StoreItem).WithMany(p => p.StoreItemExps)
                .HasForeignKey(d => d.StoreItemId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        modelBuilder.Entity<ItemNum>(entity =>
        {
            entity.HasOne(d => d.Item).WithMany(p => p.ItemNums)
                .HasForeignKey(d => d.ItemId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        modelBuilder.Entity<ConsumedItem>(entity =>
        {
            entity.HasOne(d => d.Consumed).WithMany(p => p.ConsumedItems)
                .HasForeignKey(d => d.ConsumedId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        modelBuilder.Entity<PurchaseItem>(entity =>
        {
            entity.HasOne(d => d.Purchase).WithMany(p => p.PurchaseItems)
                .HasForeignKey(d => d.PurchaseId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        modelBuilder.Entity<ReturnPurchaseItem>(entity =>
        {
            entity.HasOne(d => d.ReturnPurchase).WithMany(p => p.ReturnPurchaseItems)
                .HasForeignKey(d => d.ReturnPurchaseId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        modelBuilder.Entity<ReturnSellItem>(entity =>
        {
            entity.HasOne(d => d.ReturnSell).WithMany(p => p.ReturnSellItems)
                .HasForeignKey(d => d.ReturnSellId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        modelBuilder.Entity<SellItem>(entity =>
        {
            entity.HasOne(d => d.Sell).WithMany(p => p.SellItems)
                .HasForeignKey(d => d.SellId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        modelBuilder.Entity<TransferItem>(entity =>
        {
            entity.HasOne(d => d.Transfer).WithMany(p => p.TransferItems)
                .HasForeignKey(d => d.TransferId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        modelBuilder.Entity<InventoryItem>(entity =>
        {
            entity.HasOne(d => d.Inventory).WithMany(p => p.InventoryItems)
                .HasForeignKey(d => d.InventoryId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        modelBuilder.Entity<User>(b =>
        {
            b.HasMany(e => e.UserRoles)
                .WithOne(e => e.User)
                .HasForeignKey(ur => ur.UserId)
                .IsRequired();
        });

        modelBuilder.Entity<Role>(b =>
        {
            b.HasMany(e => e.UserRoles)
                .WithOne(e => e.Role)
                .HasForeignKey(ur => ur.RoleId)
                .IsRequired();
        });

        modelBuilder.Entity<UserTreasury>()
            .HasMany(r => r.UserTreasuryReceipts)
            .WithOne(t => t.UserTreasury).HasForeignKey(r => r.UserTreasuryId).OnDelete(DeleteBehavior.Restrict);
        modelBuilder.Entity<UserTreasury>()
           .HasMany(r => r.FromTreasuryReceipts)
           .WithOne(t => t.ToTreasury).HasForeignKey(r => r.ToTreasuryId);

        // ==================== فهارس UserClaim لتحسين الأداء ====================

        // فهرس مركب على UserId و ClaimType لتسريع البحث عن صلاحيات المستخدم
        modelBuilder.Entity<IdentityUserClaim<Guid>>()
            .HasIndex(c => new { c.UserId, c.ClaimType })
            .HasDatabaseName("IX_UserClaims_UserId_ClaimType");

        // فهرس على ClaimType لتسريع البحث عن نوع معين من الصلاحيات
        modelBuilder.Entity<IdentityUserClaim<Guid>>()
            .HasIndex(c => c.ClaimType)
            .HasDatabaseName("IX_UserClaims_ClaimType");

        // فهرس على ClaimValue لتسريع البحث عن صلاحية معينة
        modelBuilder.Entity<IdentityUserClaim<Guid>>()
            .HasIndex(c => c.ClaimValue)
            .HasDatabaseName("IX_UserClaims_ClaimValue");

        // ==================== فهارس RoleClaim لتحسين الأداء ====================

        // فهرس مركب على RoleId و ClaimType لتسريع البحث عن صلاحيات الدور
        modelBuilder.Entity<IdentityRoleClaim<Guid>>()
            .HasIndex(c => new { c.RoleId, c.ClaimType })
            .HasDatabaseName("IX_RoleClaims_RoleId_ClaimType");

        // فهرس على ClaimType لتسريع البحث عن نوع معين من صلاحيات الأدوار
        modelBuilder.Entity<IdentityRoleClaim<Guid>>()
            .HasIndex(c => c.ClaimType)
            .HasDatabaseName("IX_RoleClaims_ClaimType");

        // فهرس على ClaimValue لتسريع البحث عن صلاحية معينة في الأدوار
        modelBuilder.Entity<IdentityRoleClaim<Guid>>()
            .HasIndex(c => c.ClaimValue)
            .HasDatabaseName("IX_RoleClaims_ClaimValue");
    }
    public DbSet<Category> Categories { get; set; }
    public DbSet<Client> Clients { get; set; }
    public DbSet<Consumed> Consumeds { get; set; }
    public DbSet<ConsumedItem> ConsumedItems { get; set; }
    public DbSet<Employee> Employees { get; set; }
    public DbSet<Expense> Expenses { get; set; }
    public DbSet<Financial> Financials { get; set; }
    public DbSet<ReturnPurchase> ReturnPurchases { get; set; }
    public DbSet<ReturnPurchaseItem> ReturnPurchaseItems { get; set; }
    public DbSet<Purchase> Purchases { get; set; }
    public DbSet<PurchaseItem> PurchaseItems { get; set; }
    public DbSet<Item> Items { get; set; }
    public DbSet<ItemNum> ItemNums { get; set; }
    public DbSet<ItemUnit> ItemUnits { get; set; }
    public DbSet<Inventory> Inventories { get; set; }
    public DbSet<InventoryItem> InventoryItems { get; set; }
    public DbSet<Receipt> Receipts { get; set; }
    public DbSet<Salary> Salaries { get; set; }
    public DbSet<Sell> Sells { get; set; }
    public DbSet<SellItem> SellItems { get; set; }
    public DbSet<ReturnSell> ReturnSells { get; set; }
    public DbSet<ReturnSellItem> ReturnSellItems { get; set; }
    public DbSet<Store> Stores { get; set; }
    public DbSet<ShopSettings> ShopSettings { get; set; }
    public DbSet<StoreItem> StoreItems { get; set; }
    public DbSet<StoreItemExp> StoreItemExps { get; set; }
    public DbSet<Transfer> Transfers { get; set; }
    public DbSet<TransferItem> TransferItems { get; set; }
    public DbSet<Treasury> Treasuries { get; set; }
    public DbSet<Unit> Units { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<UserTreasury> UserTreasuries { get; set; }


}

