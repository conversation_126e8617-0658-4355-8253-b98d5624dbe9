﻿using PosGTech.ModelsDTO.Clients;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PosGTech.ModelsDTO.Sells 
{

public class SellCMDTO
{
    public int InvoiceNo { get; set; } = 0;
    public DateOnly Date { get; set; }
    public decimal Total { get; set; }
    public decimal DiscountValue { get; set; } = decimal.Zero;
    public decimal FinalTotal { get; set; }

    public ClientCMDTO Client { get; set; }
    public List<SellItemDTO> SellItemDTOs { get; set; } 
}
}