﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Authentication;
using PosGTech.ModelsDTO.Users;
using PosGTech.ModelsDTO.Authorization;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace PosGTech.DataAccess.Repository
{
    public class UserRepository : Repository<User>, IUserRepository
    {
        IUserTreasuryRepository _userTreasuryRepository;
        private readonly RoleManager<Role> _roleManager;

        public UserRepository(ApplicationDbContext db, IUserTreasuryRepository userTreasuryRepository, RoleManager<Role> roleManager = null) : base(db)
        {
            _userTreasuryRepository = userTreasuryRepository;
            _roleManager = roleManager;
        }
        public async Task<ResponseVM> AddUser(UserDTO model, List<Guid> Treasury, UserManager<User> _userManager)
        {
            if (await _userManager.FindByNameAsync(model.UserName) is not null)
                return new ResponseVM { State = false, Message = "الاسم موجود" };

            // التحقق من وجود الدور المحدد
            if (string.IsNullOrEmpty(model.RoleName))
                return new ResponseVM { State = false, Message = "يجب اختيار دور للمستخدم" };

            var user = new User
            {
                Name = model.Name,
                UserName = model.UserName,
                Email = model.UserName + "@admin.com",
                EmailConfirmed = true,
                LockoutEnabled = model.State,
                UserTreasuries = Treasury.Select(x => new UserTreasury() { TreasuryId = x, Balance = 0 }).ToList()
            };

            var result = await _userManager.CreateAsync(user, model.Password);

            if (!result.Succeeded)
            {
                var errors = string.Empty;

                foreach (var error in result.Errors)
                    errors += $"{error.Description},";

                return new ResponseVM { State = false, Message = errors };
            }

            // إضافة الدور الواحد المحدد
            await _userManager.AddToRoleAsync(user, model.RoleName);

            // إضافة الصلاحيات الإضافية كـ UserClaims
            foreach (var permission in model.AdditionalPermissions)
            {
                if (PermissionConstants.GetAllPermissions().Contains(permission))
                {
                    await _userManager.AddClaimAsync(user, new Claim("additional_permission", permission));
                }
            }

            // إضافة الصلاحيات المحذوفة من الدور كـ UserClaims
            foreach (var permission in model.RemovedPermissions)
            {
                if (PermissionConstants.GetAllPermissions().Contains(permission))
                {
                    await _userManager.AddClaimAsync(user, new Claim("removed_permission", permission));
                }
            }

            return new ResponseVM { State = true, Message = "تم الحفظ" };
        }
        public async Task<ResponseVM> UpdateUser(Guid id, UserDTO model, UserManager<User> _userManager)
        {
            var oldUser = await GetByIdAsync(id.ToString());
            if (oldUser == null)
            {
                return new ResponseVM() { State = false, Message = "المستخدم غير موجود" };
            }

            // حماية الأدمن الرئيسي من التعطيل
            if (oldUser.UserName == "Admin" && !model.State)
            {
                return new ResponseVM() { State = false, Message = "لا يمكن تعطيل الأدمن الرئيسي للنظام" };
            }

            // التحقق من وجود الدور المحدد
            if (string.IsNullOrEmpty(model.RoleName))
                return new ResponseVM { State = false, Message = "يجب اختيار دور للمستخدم" };

            var oldRoles = await _userManager.GetRolesAsync(oldUser);

            oldUser.Name = model.Name;
            oldUser.UserName = model.UserName;
            oldUser.LockoutEnabled = model.State;
            // إذا كان المستخدم نشط، تأكد من إزالة أي lockout
            if (model.State)
            {
                oldUser.LockoutEnd = null;
            }

            await _userManager.UpdateAsync(oldUser);

            // تحديث كلمة المرور فقط إذا كانت غير فارغة
            if (!string.IsNullOrWhiteSpace(model.Password))
            {
                // إزالة كلمة المرور القديمة وإضافة الجديدة
                await _userManager.RemovePasswordAsync(oldUser);
                var passwordResult = await _userManager.AddPasswordAsync(oldUser, model.Password);

                if (!passwordResult.Succeeded)
                {
                    var passwordErrors = string.Join(", ", passwordResult.Errors.Select(e => e.Description));
                    return new ResponseVM { State = false, Message = $"خطأ في تحديث كلمة المرور: {passwordErrors}" };
                }
            }

            // تحديث الدور - إزالة جميع الأدوار القديمة وإضافة الدور الجديد الواحد
            await _userManager.RemoveFromRolesAsync(oldUser, oldRoles);
            await _userManager.AddToRoleAsync(oldUser, model.RoleName);

            // تحديث الصلاحيات الإضافية والمحذوفة
            await UpdateUserPermissions(oldUser, model, _userManager);

            return new ResponseVM() { State = true, Message = "تم الحفظ بنجاح" };
        }

        public async Task<ResponseVM> DeleteUser(Guid id, UserManager<User> _userManager, Guid? currentUserId = null)
        {
            try
            {
                var oldUser = await GetByIdAsync(id.ToString());
                if (oldUser == null)
                {
                    return new ResponseVM() { State = false, Message = "المستخدم غير موجود" };
                }

                // حماية الأدمن الرئيسي من الحذف
                if (oldUser.UserName == "Admin")
                {
                    return new ResponseVM() { State = false, Message = "لا يمكن حذف الأدمن الرئيسي للنظام" };
                }

                // حماية إضافية: منع المستخدم من حذف نفسه إذا كان أدمن رئيسي
                if (currentUserId.HasValue && currentUserId.Value == id)
                {
                    var currentUser = await GetByIdAsync(currentUserId.Value.ToString());
                    if (currentUser != null && currentUser.UserName == "Admin")
                    {
                        return new ResponseVM() { State = false, Message = "لا يمكن للأدمن الرئيسي حذف نفسه" };
                    }
                }

                // فحص ما إذا كان المستخدم قد قام بأنشطة في النظام
                var hasActivity = await HasUserActivity(id);

                if (hasActivity)
                {
                    // إذا كان المستخدم له أنشطة، نقوم بالحذف المؤقت (Soft Delete)
                    oldUser.LockoutEnabled = false;
                    oldUser.LockoutEnd = DateTimeOffset.MaxValue; // تعطيل دائم

                    var result = await _userManager.UpdateAsync(oldUser);

                    if (result.Succeeded)
                    {
                        return new ResponseVM() { State = true, Message = "تم تعطيل المستخدم بنجاح (حذف مؤقت)" };
                    }
                    else
                    {
                        var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                        return new ResponseVM() { State = false, Message = $"فشل في تعطيل المستخدم: {errors}" };
                    }
                }
                else
                {
                    // إذا لم يكن للمستخدم أنشطة، نقوم بالحذف الدائم
                    return await PermanentDeleteUser(id, _userManager);
                }
            }
            catch (Exception ex)
            {
                return new ResponseVM()
                {
                    State = false,
                    Message = $"خطأ في حذف المستخدم: {ex.Message}"
                };
            }
        }

        public async Task<IEnumerable<UserDTO>> GetAllUser(UserManager<User> _userManager)
        {
            // إظهار المستخدمين النشطين فقط (المستخدمين غير المخفيين)
            var users = await _userManager.Users
                .Where(x => x.LockoutEnabled && (x.LockoutEnd == null || x.LockoutEnd <= DateTimeOffset.UtcNow))
                .Include(x => x.UserRoles).ThenInclude(x => x.Role)
                .ToListAsync();

            var userDTOs = new List<UserDTO>();

            foreach (var user in users)
            {
                // الحصول على Claims المستخدم
                var claims = await _userManager.GetClaimsAsync(user);

                var userDTO = new UserDTO
                {
                    Id = user.Id,
                    Name = user.Name,
                    RoleName = user.UserRoles.Select(x => x.Role.Name).FirstOrDefault() ?? "",
                    State = user.LockoutEnabled && (user.LockoutEnd == null || user.LockoutEnd <= DateTimeOffset.UtcNow),
                    UserName = user.UserName,
                    AdditionalPermissions = claims.Where(c => c.Type == "additional_permission").Select(c => c.Value).ToList(),
                    RemovedPermissions = claims.Where(c => c.Type == "removed_permission").Select(c => c.Value).ToList(),
                    EffectivePermissions = await GetUserEffectivePermissions(user.Id, _userManager)
                };

                userDTOs.Add(userDTO);
            }

            return userDTOs;
        }

        public async Task<IEnumerable<UserDTO>> GetAllUserIncludingHidden(UserManager<User> _userManager)
        {
            // إظهار جميع المستخدمين بما في ذلك المخفيين
            var users = await _userManager.Users
                .Include(x => x.UserRoles).ThenInclude(x => x.Role)
                .ToListAsync();

            var userDTOs = new List<UserDTO>();

            foreach (var user in users)
            {
                // الحصول على Claims المستخدم
                var claims = await _userManager.GetClaimsAsync(user);

                var userDTO = new UserDTO
                {
                    Id = user.Id,
                    Name = user.Name,
                    RoleName = user.UserRoles.Select(x => x.Role.Name).FirstOrDefault() ?? "",
                    State = user.LockoutEnabled && (user.LockoutEnd == null || user.LockoutEnd <= DateTimeOffset.UtcNow),
                    UserName = user.UserName,
                    AdditionalPermissions = claims.Where(c => c.Type == "additional_permission").Select(c => c.Value).ToList(),
                    RemovedPermissions = claims.Where(c => c.Type == "removed_permission").Select(c => c.Value).ToList(),
                    EffectivePermissions = await GetUserEffectivePermissions(user.Id, _userManager)
                };

                userDTOs.Add(userDTO);
            }

            return userDTOs;
        }

        public async Task<ResponseVM> RestoreUser(Guid id, UserManager<User> _userManager)
        {
            try
            {
                var user = await GetByIdAsync(id.ToString());
                if (user == null)
                {
                    return new ResponseVM() { State = false, Message = "المستخدم غير موجود" };
                }

                // استعادة المستخدم المخفي
                user.LockoutEnabled = true;
                user.LockoutEnd = null;

                var result = await _userManager.UpdateAsync(user);

                if (result.Succeeded)
                {
                    return new ResponseVM() { State = true, Message = "تم استعادة المستخدم بنجاح" };
                }
                else
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return new ResponseVM() { State = false, Message = $"فشل في استعادة المستخدم: {errors}" };
                }
            }
            catch (Exception ex)
            {
                return new ResponseVM()
                {
                    State = false,
                    Message = $"خطأ في استعادة المستخدم: {ex.Message}"
                };
            }
        }

        public async Task<bool> IsMainAdmin(Guid userId, UserManager<User> _userManager)
        {
            var user = await GetByIdAsync(userId.ToString());
            return user != null && user.UserName == "Admin";
        }
        public async Task<IEnumerable<UserCMDTO>> GetAllUserCM(UserManager<User> _userManager)
        {
            // إظهار المستخدمين النشطين فقط
            return await _userManager.Users
                .Where(x => x.LockoutEnabled && (x.LockoutEnd == null || x.LockoutEnd <= DateTimeOffset.UtcNow))
                .Select(x => new UserCMDTO
            {
                Id = x.Id,
                Name = x.Name
            }).ToListAsync();
        }

        /// <summary>
        /// فحص ما إذا كان المستخدم قد قام بأي أنشطة في النظام
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>true إذا كان المستخدم قد قام بأنشطة، false إذا لم يقم بأي أنشطة</returns>
        public async Task<bool> HasUserActivity(Guid userId)
        {
            try
            {
                // فحص الجداول الرئيسية التي تتبع أنشطة المستخدمين

                // فحص فواتير المبيعات
                var hasSells = await _db.Sells.AnyAsync(x => x.UserId == userId);
                if (hasSells) return true;

                // فحص فواتير المشتريات
                var hasPurchases = await _db.Purchases.AnyAsync(x => x.UserId == userId);
                if (hasPurchases) return true;

                // فحص الواصلات المالية
                var hasReceipts = await _db.Receipts.AnyAsync(x => x.UserId == userId);
                if (hasReceipts) return true;

                // فحص فواتير الجرد
                var hasInventories = await _db.Inventories.AnyAsync(x => x.UserId == userId);
                if (hasInventories) return true;

                // فحص تحويلات المخزون
                var hasTransfers = await _db.Transfers.AnyAsync(x => x.UserId == userId);
                if (hasTransfers) return true;

                // فحص إدارة الموظفين
                var hasEmployees = await _db.Employees.AnyAsync(x => x.UserId == userId);
                if (hasEmployees) return true;

                // فحص إدارة العملاء والموردين
                var hasClients = await _db.Clients.AnyAsync(x => x.UserId == userId);
                if (hasClients) return true;

                // فحص إدارة فئات المنتجات
                var hasCategories = await _db.Categories.AnyAsync(x => x.UserId == userId);
                if (hasCategories) return true;

                // فحص إدارة المنتجات
                var hasItems = await _db.Items.AnyAsync(x => x.UserId == userId);
                if (hasItems) return true;

                // فحص إدارة المخازن
                var hasStores = await _db.Stores.AnyAsync(x => x.UserId == userId);
                if (hasStores) return true;

                // فحص الأنواع المالية
                var hasFinancials = await _db.Financials.AnyAsync(x => x.UserId == userId);
                if (hasFinancials) return true;

                // فحص أنواع المصروفات
                var hasExpenses = await _db.Expenses.AnyAsync(x => x.UserId == userId);
                if (hasExpenses) return true;

                // فحص تواريخ انتهاء المنتجات
                var hasStoreItemExps = await _db.StoreItemExps.AnyAsync(x => x.UserId == userId);
                if (hasStoreItemExps) return true;

                // فحص عناصر التحويل
                var hasTransferItems = await _db.TransferItems.AnyAsync(x => x.UserId == userId);
                if (hasTransferItems) return true;

                // إذا لم يتم العثور على أي أنشطة
                return false;
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، نعتبر أن المستخدم له أنشطة لتجنب الحذف الخاطئ
                Console.WriteLine($"خطأ في فحص أنشطة المستخدم {userId}: {ex.Message}");
                return true;
            }
        }
        public async Task<UserDTO> GetUserById(Guid id, UserManager<User> _userManager)
        {
            var user = await _userManager.FindByIdAsync(id.ToString());
            if (user == null) return null;

            var roles = await _userManager.GetRolesAsync(user);
            var claims = await _userManager.GetClaimsAsync(user);

            return new UserDTO
            {
                Id = user.Id,
                Name = user.Name,
                UserName = user.UserName,
                RoleName = roles.FirstOrDefault() ?? string.Empty, // دور واحد فقط
                AdditionalPermissions = claims.Where(c => c.Type == "additional_permission").Select(c => c.Value).ToList(),
                RemovedPermissions = claims.Where(c => c.Type == "removed_permission").Select(c => c.Value).ToList(),
                EffectivePermissions = await GetUserEffectivePermissions(id, _userManager),
                State = user.LockoutEnabled && (user.LockoutEnd == null || user.LockoutEnd <= DateTimeOffset.UtcNow)
            };
        }

        public async Task<AuthUser> LoginAsync(LoginUser model, UserManager<User> _userManager, Jwt _jwt)
        {
            var authModel = new AuthUser();

            var user = new User();

            if (model.UserName.Contains("@"))
            {
                user = await _userManager.FindByEmailAsync(model.UserName);
            }
            else
            {
                user = await _userManager.FindByNameAsync(model.UserName);
            }

            if (user is null || !await _userManager.CheckPasswordAsync(user, model.Password))
            {
                authModel.Message = "خطا في اسم المستخدم او كلمة المرور";
                return authModel;
            }
            if (!user.LockoutEnabled)
            {
                authModel.Message = "تم حظرك";
                return authModel;
            }
            var cashId = (await _userTreasuryRepository.GetFirstOrDefault(x => x.UserId == user.Id && x.Treasury.Name == "النقدية")).Id;
            var cardId = (await _userTreasuryRepository.GetFirstOrDefault(x => x.UserId == user.Id && x.Treasury.Name != "النقدية")).Id;
            var jwtSecurityToken = await CreateJwtToken(user, _userManager, _jwt, cashId, cardId);
            var rolesList = await _userManager.GetRolesAsync(user);

            authModel.isAuthenticated = true;
            authModel.Token = new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);
            authModel.Email = user.Email;
            authModel.UserName = user.UserName;
            authModel.ExpiresOn = jwtSecurityToken.ValidTo;
            authModel.Roles = rolesList.ToList();

            if (user.RefreshTokens.Any(t => t.IsActive))
            {
                var activeRefreshToken = user.RefreshTokens.FirstOrDefault(t => t.IsActive);
                authModel.RefreshToken = activeRefreshToken.Token;
                authModel.RefreshTokenExpiration = activeRefreshToken.ExpiresOn;
            }
            else
            {
                var refreshToken = GenerateRefreshToken();
                authModel.RefreshToken = refreshToken.Token;
                authModel.RefreshTokenExpiration = refreshToken.ExpiresOn;
                user.RefreshTokens.Add(refreshToken);
                await _userManager.UpdateAsync(user);
            }

            return authModel;
        }

        private async Task<JwtSecurityToken> CreateJwtToken(User user, UserManager<User> _userManager, Jwt _jwt, Guid cashId, Guid cardId)
        {
            // الحصول على أدوار المستخدم
            var userRoles = await _userManager.GetRolesAsync(user);
            var roleClaims = new List<Claim>();

            // إضافة أدوار المستخدم كـ Claims
            foreach (var role in userRoles)
                roleClaims.Add(new Claim(ClaimTypes.Role, role));

            // الحصول على الصلاحيات الفعلية للمستخدم (من الأدوار + الإضافية - المحذوفة)
            var effectivePermissions = await GetUserEffectivePermissions(user.Id, _userManager);

            // إضافة الصلاحيات كـ Claims في التوكن
            var permissionClaims = effectivePermissions.Select(permission =>
                new Claim("permission", permission)).ToList();

            // إنشاء Claims الأساسية
            var basicClaims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Name, user.UserName),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(JwtRegisteredClaimNames.Email, user.Email),
                new Claim("id", user.Id.ToString()),
                new Claim("cashId", cashId.ToString()),
                new Claim("cardId", cardId.ToString()),
            };

            // دمج جميع Claims (الأساسية + الأدوار + الصلاحيات)
            var allClaims = basicClaims
                .Union(roleClaims)
                .Union(permissionClaims);

            var symmetricSecurityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwt.Key));
            var signingCredentials = new SigningCredentials(symmetricSecurityKey, SecurityAlgorithms.HmacSha256);

            var jwtSecurityToken = new JwtSecurityToken(
                issuer: _jwt.Issuer,
                audience: _jwt.Audience,
                claims: allClaims,
                expires: DateTime.UtcNow.AddMinutes(_jwt.DurationInMinutes),
                signingCredentials: signingCredentials);

            return jwtSecurityToken;
        }

        public async Task<AuthUser> RefreshTokenAsync(string token, UserManager<User> _userManager, Jwt _jwt)
        {
            var authModel = new AuthUser();

            var user = await _userManager.Users.SingleOrDefaultAsync(u => u.RefreshTokens.Any(t => t.Token == token));

            if (user == null)
            {
                authModel.Message = "Invalid token";
                return authModel;
            }

            var refreshToken = user.RefreshTokens.Single(t => t.Token == token);

            if (!refreshToken.IsActive)
            {
                authModel.Message = "Inactive token";
                return authModel;
            }

            refreshToken.RevokedOn = DateTime.UtcNow;

            var newRefreshToken = GenerateRefreshToken();
            user.RefreshTokens.Add(newRefreshToken);
            await _userManager.UpdateAsync(user);

            var cashId = (await _userTreasuryRepository.GetFirstOrDefault(x => x.UserId == user.Id && x.Treasury.Name == "النقدية")).Id;
            var cardId = (await _userTreasuryRepository.GetFirstOrDefault(x => x.UserId == user.Id && x.Treasury.Name != "النقدية")).Id;

            var jwtToken = await CreateJwtToken(user, _userManager, _jwt, cashId, cardId);
            authModel.isAuthenticated = true;
            authModel.Token = new JwtSecurityTokenHandler().WriteToken(jwtToken);
            authModel.Email = user.Email;
            authModel.UserName = user.UserName;
            var roles = await _userManager.GetRolesAsync(user);
            authModel.Roles = roles.ToList();
            authModel.RefreshToken = newRefreshToken.Token;
            authModel.RefreshTokenExpiration = newRefreshToken.ExpiresOn;

            return authModel;
        }

        public async Task<bool> RevokeTokenAsync(string token, UserManager<User> _userManager)
        {
            var user = await _userManager.Users.SingleOrDefaultAsync(u => u.RefreshTokens.Any(t => t.Token == token));

            if (user == null)
                return false;

            var refreshToken = user.RefreshTokens.Single(t => t.Token == token);

            if (!refreshToken.IsActive)
                return false;

            refreshToken.RevokedOn = DateTime.UtcNow;
            await _userManager.UpdateAsync(user);

            return true;
        }

        private RefreshToken GenerateRefreshToken()
        {
            var randomNumber = new byte[32];

            using var generator = new System.Security.Cryptography.RNGCryptoServiceProvider();

            generator.GetBytes(randomNumber);

            return new RefreshToken
            {
                Token = Convert.ToBase64String(randomNumber),
                ExpiresOn = DateTime.UtcNow.AddDays(10),
                CreatedOn = DateTime.UtcNow
            };
        }

        // ==================== Methods جديدة لإدارة الصلاحيات ====================

        /// <summary>
        /// الحصول على الصلاحيات الفعلية للمستخدم
        /// </summary>
        public async Task<List<string>> GetUserEffectivePermissions(Guid userId, UserManager<User> _userManager)
        {
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null) return new List<string>();

            // الحصول على صلاحيات الدور
            var userRoles = await _userManager.GetRolesAsync(user);
            var rolePermissions = new List<string>();

            // جمع صلاحيات جميع الأدوار (في حالة وجود أكثر من دور - للتوافق مع النظام القديم)
            foreach (var roleName in userRoles)
            {
                var rolePermissionsFromDb = await GetRolePermissions(roleName);
                rolePermissions.AddRange(rolePermissionsFromDb);
            }

            // الحصول على Claims المستخدم
            var userClaims = await _userManager.GetClaimsAsync(user);
            var additionalPermissions = userClaims.Where(c => c.Type == "additional_permission").Select(c => c.Value).ToList();
            var removedPermissions = userClaims.Where(c => c.Type == "removed_permission").Select(c => c.Value).ToList();

            // حساب الصلاحيات الفعلية
            var effectivePermissions = rolePermissions
                .Union(additionalPermissions)
                .Except(removedPermissions)
                .Distinct()
                .ToList();

            return effectivePermissions;
        }

        /// <summary>
        /// إدارة صلاحيات المستخدم بشكل مجمع
        /// </summary>
        public async Task<ResponseVM> ManageUserPermissions(UserPermissionManagementDTO model, UserManager<User> _userManager)
        {
            var user = await _userManager.FindByIdAsync(model.UserId.ToString());
            if (user == null)
                return new ResponseVM { State = false, Message = "المستخدم غير موجود" };

            var results = new List<string>();

            try
            {
                // إضافة الصلاحيات الإضافية
                foreach (var permission in model.PermissionsToAdd)
                {
                    if (PermissionConstants.GetAllPermissions().Contains(permission))
                    {
                        var claim = new Claim("additional_permission", permission);
                        var result = await _userManager.AddClaimAsync(user, claim);
                        if (result.Succeeded)
                            results.Add($"تم إضافة الصلاحية: {PermissionDescriptions.GetDescription(permission)}");
                    }
                }

                // إزالة الصلاحيات الإضافية
                foreach (var permission in model.AdditionalPermissionsToRemove)
                {
                    var claim = new Claim("additional_permission", permission);
                    var result = await _userManager.RemoveClaimAsync(user, claim);
                    if (result.Succeeded)
                        results.Add($"تم إزالة الصلاحية الإضافية: {PermissionDescriptions.GetDescription(permission)}");
                }

                // حذف صلاحيات من الدور
                foreach (var permission in model.RolePermissionsToRemove)
                {
                    var claim = new Claim("removed_permission", permission);
                    var result = await _userManager.AddClaimAsync(user, claim);
                    if (result.Succeeded)
                        results.Add($"تم حذف الصلاحية من الدور: {PermissionDescriptions.GetDescription(permission)}");
                }

                // استعادة صلاحيات محذوفة
                foreach (var permission in model.RemovedPermissionsToRestore)
                {
                    var claim = new Claim("removed_permission", permission);
                    var result = await _userManager.RemoveClaimAsync(user, claim);
                    if (result.Succeeded)
                        results.Add($"تم استعادة الصلاحية: {PermissionDescriptions.GetDescription(permission)}");
                }

                return new ResponseVM
                {
                    State = true,
                    Message = "تم تحديث صلاحيات المستخدم بنجاح",
                    Data = string.Join(", ", results)
                };
            }
            catch (Exception ex)
            {
                return new ResponseVM { State = false, Message = $"خطأ في إدارة صلاحيات المستخدم: {ex.Message}" };
            }
        }

        /// <summary>
        /// تحديث صلاحيات المستخدم عند التعديل
        /// </summary>
        private async Task UpdateUserPermissions(User user, UserDTO model, UserManager<User> _userManager)
        {
            // الحصول على Claims الحالية
            var currentClaims = await _userManager.GetClaimsAsync(user);

            // إزالة جميع الصلاحيات الحالية
            var permissionClaims = currentClaims.Where(c =>
                c.Type == "additional_permission" || c.Type == "removed_permission").ToList();

            foreach (var claim in permissionClaims)
            {
                await _userManager.RemoveClaimAsync(user, claim);
            }

            // إضافة الصلاحيات الجديدة
            foreach (var permission in model.AdditionalPermissions)
            {
                if (PermissionConstants.GetAllPermissions().Contains(permission))
                {
                    await _userManager.AddClaimAsync(user, new Claim("additional_permission", permission));
                }
            }

            foreach (var permission in model.RemovedPermissions)
            {
                if (PermissionConstants.GetAllPermissions().Contains(permission))
                {
                    await _userManager.AddClaimAsync(user, new Claim("removed_permission", permission));
                }
            }
        }

        /// <summary>
        /// الحصول على صلاحيات دور معين
        /// </summary>
        private async Task<List<string>> GetRolePermissions(string roleName)
        {
            if (_roleManager == null) return new List<string>();

            var role = await _roleManager.FindByNameAsync(roleName);
            if (role == null) return new List<string>();

            var roleClaims = await _roleManager.GetClaimsAsync(role);
            return roleClaims.Where(c => c.Type == "permission").Select(c => c.Value).ToList();
        }

        /// <summary>
        /// حذف المستخدم نهائياً من قاعدة البيانات
        /// </summary>
        /// <param name="id">معرف المستخدم</param>
        /// <param name="_userManager">مدير المستخدمين</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<ResponseVM> PermanentDeleteUser(Guid id, UserManager<User> _userManager)
        {
            try
            {
                var user = await GetByIdAsync(id.ToString());
                if (user == null)
                {
                    return new ResponseVM() { State = false, Message = "المستخدم غير موجود" };
                }

                // حماية الأدمن الرئيسي من الحذف
                if (user.UserName == "Admin")
                {
                    return new ResponseVM() { State = false, Message = "لا يمكن حذف الأدمن الرئيسي للنظام" };
                }

                // التحقق من عدم وجود أنشطة للمستخدم
                var hasActivity = await HasUserActivity(id);
                if (hasActivity)
                {
                    return new ResponseVM() { State = false, Message = "لا يمكن حذف المستخدم نهائياً لأنه قام بأنشطة في النظام. سيتم إجراء حذف مؤقت بدلاً من ذلك." };
                }

                // حذف العلاقات المرتبطة بالمستخدم
                // حذف خزائن المستخدم
                var userTreasuries = await _db.UserTreasuries.Where(ut => ut.UserId == id).ToListAsync();
                if (userTreasuries.Any())
                {
                    _db.UserTreasuries.RemoveRange(userTreasuries);
                }

                // حذف أدوار المستخدم
                var userRoles = await _db.UserRoles.Where(ur => ur.UserId == id).ToListAsync();
                if (userRoles.Any())
                {
                    _db.UserRoles.RemoveRange(userRoles);
                }

                // حذف مطالبات المستخدم (الصلاحيات الإضافية)
                var userClaims = await _db.UserClaims.Where(uc => uc.UserId == id).ToListAsync();
                if (userClaims.Any())
                {
                    _db.UserClaims.RemoveRange(userClaims);
                }

                // حذف رموز التحديث
                // لا حاجة لحذفها منفصلة لأنها Owned Entity وستحذف تلقائياً

                // حذف المستخدم نهائياً
                var result = await _userManager.DeleteAsync(user);

                if (result.Succeeded)
                {
                    return new ResponseVM() { State = true, Message = "تم حذف المستخدم نهائياً بنجاح" };
                }
                else
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    return new ResponseVM() { State = false, Message = $"فشل في حذف المستخدم: {errors}" };
                }
            }
            catch (Exception ex)
            {
                return new ResponseVM()
                {
                    State = false,
                    Message = $"خطأ في حذف المستخدم: {ex.Message}"
                };
            }
        }

    }
}

