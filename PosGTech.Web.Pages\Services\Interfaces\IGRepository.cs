﻿using Microsoft.AspNetCore.Components.Forms;
using PosGTech.Models.ViewModels;

namespace PosGTech.Web.Services.Interfaces
{
    public interface IGRepository<T> where T : class
    {
        Task<(IEnumerable<T>? list, ResponseVM? response)> GetAll(string url);
        Task<(T? model, ResponseVM? response)> GetByIdAsync(string url, object id);
        Task<ResponseVM> Insert(string url, T data);
        Task<ResponseVM> Update(string url, T data, Guid id);
        Task<ResponseVM> Delete(string url, Guid id);
        Task<ResponseVM> UploadFile(string url, HttpContent content);
        Task<byte[]?> DownloadFile(string url);
        Task<ResponseVM> InsertWithFile(string url, T data, IBrowserFile file, string NameProp);
        Task<ResponseVM> UpdateWithFile(string url, T data, Guid id, IBrowserFile file, string NameProp);

    }
}
