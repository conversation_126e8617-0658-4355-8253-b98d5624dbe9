﻿@page "/upsertClient/{id:guid}"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<MudDialog>
    <DialogContent>
        <EditForm id="_form" Model="@client" OnValidSubmit="@Upsert">
            <DataAnnotationsValidator />
            <MudPaper Class="client-form-container" Elevation="3">
                <div class="form-header">
                    <MudText Typo="Typo.h5" Class="mb-4 primary-text">
                        @(id == Guid.Empty ? "إضافة عميل جديد" : "تعديل بيانات العميل")
                    </MudText>
                </div>

                <MudDivider Class="mb-4" />

                <MudGrid Class="pa-4">
                    <MudItem xs="12" sm="6">
                        <MudTextField T="string"
                                      @bind-Value="client.Name"
                                      Label="اسم العميل"
                                      For="@(() => client.Name)"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Person"
                                      Class="mud-input-elegant" />
                    </MudItem>

                    <MudItem xs="12" sm="6">
                        <MudNumericField T="int?"
                                         @bind-Value="client.Phone"
                                         Label="رقم الهاتف"
                                         InputMode="InputMode.tel"
                                         HideSpinButtons="true"
                                         Variant="Variant.Outlined"
                                         For="@(() => client.Phone)"
                                         Adornment="Adornment.Start"
                                         AdornmentIcon="@Icons.Material.Filled.Phone"
                                         Class="mud-input-elegant" />
                    </MudItem>

                    <MudItem xs="12" sm="6">
                        <MudTextField T="string"
                                      @bind-Value="client.Address"
                                      Label="العنوان"
                                      For="@(() => client.Address)"
                                      Variant="Variant.Outlined"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.LocationOn"
                                      Class="mud-input-elegant" />
                    </MudItem>

                    <MudItem xs="12" sm="6">
                        <MudNumericField T="decimal"
                                         @bind-Value="client.Balance"
                                         Label="الرصيد الحالي"
                                         ReadOnly="client.Id!=Guid.Empty"
                                         For="@(() => client.Balance)"
                                         Variant="Variant.Outlined"
                                         Adornment="Adornment.Start"
                                         AdornmentIcon="@Icons.Material.Filled.AccountBalance"
                                         Class="mud-input-elegant" />
                    </MudItem>

                    <MudItem xs="12">
                        <MudPaper Elevation="0" Class="client-type-container pa-3 mt-2">
                            <MudText Typo="Typo.subtitle1" Class="mb-2">نوع العميل:</MudText>
                            <MudStack Row="true" Spacing="4">
                                <MudCheckBox T="bool"
                                             @bind-Value="client.IsSupplier"
                                             Label="مورد"
                                             For="@(()=>client.IsSupplier)"
                                             Color="Color.Primary"
                                             CheckedIcon="@Icons.Material.Filled.LocalShipping"
                                             UncheckedIcon="@Icons.Material.Outlined.LocalShipping"
                                             Class="client-checkbox" />

                                <MudCheckBox T="bool"
                                             @bind-Value="client.IsCustomer"
                                             Label="زبون"
                                             For="@(()=>client.IsCustomer)"
                                             Color="Color.Secondary"
                                             CheckedIcon="@Icons.Material.Filled.ShoppingCart"
                                             UncheckedIcon="@Icons.Material.Outlined.ShoppingCart"
                                             Class="client-checkbox" />
                            </MudStack>
                        </MudPaper>
                    </MudItem>

                    @if (id != Guid.Empty)
                    {
                        <MudDivider Class="my-4" />

                        <MudItem xs="12">
                            <div class="action-buttons-container mt-2">
                                <MudText Typo="Typo.subtitle1" Class="mb-3">إجراءات إضافية:</MudText>
                                <div class="d-flex flex-wrap gap-2">
                                    <MudButton Variant="Variant.Filled"
                                               Color="Color.Primary"
                                               OnClick="@(()=>AddReceipt(FinancialId.Client))"
                                               StartIcon="@Icons.Material.Filled.Receipt"
                                               Class="receipt-button">
                                        إيصال صرف\قبض
                                    </MudButton>

                                    <MudButton Variant="Variant.Filled"
                                               Color="Color.Secondary"
                                               OnClick="@(()=>AddReceipt(FinancialId.OpeningBalanceForClient))"
                                               StartIcon="@Icons.Material.Filled.PlaylistAdd"
                                               Class="receipt-button">
                                        إيصال رصيد افتتاحي
                                    </MudButton>
                                </div>
                            </div>
                        </MudItem>
                    }
                </MudGrid>
            </MudPaper>
        </EditForm>
    </DialogContent>

    <DialogActions >
        <MudButton Variant="Variant.Outlined"
                   OnClick="@Cancel"
                   Color="Color.Error"
                   StartIcon="@Icons.Material.Filled.Cancel"
                   Class="cancel-button">
            إلغاء
        </MudButton>

        <MudButton form="_form"
                   Variant="Variant.Filled"
                   ButtonType="ButtonType.Submit"
                   Color="Color.Success"
                   StartIcon="@Icons.Material.Filled.Save"
                   Class="save-button">
            حفظ
        </MudButton>
    </DialogActions>
</MudDialog>

<style>
    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }

    .client-form-container {
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .form-header {
        padding: 16px;
      
    }

    .primary-text {
        color: var(--mud-palette-primary-darken);
        font-weight: 500;
    }

    .mud-input-elegant {
        margin-bottom: 8px;
    }

    .client-type-container {
        border-radius: 8px;
        background-color: var(--mud-palette-background-grey);
    }

    .client-checkbox {
        transition: all 0.2s ease;
    }

        .client-checkbox:hover {
            transform: scale(1.05);
        }

    .action-buttons-container {
        margin-bottom: 16px;
    }

    .receipt-button {
        transition: transform 0.2s;
        border-radius: 4px;
        font-weight: 500;
        margin-bottom: 8px;
    }

        .receipt-button:hover {
            transform: translateY(-2px);
        }


    .save-button, .cancel-button {
        border-radius: 4px;
        font-weight: 500;
        min-width: 100px;
        transition: all 0.2s ease;
    }

        .save-button:hover, .cancel-button:hover {
            transform: translateY(-2px);
        }
</style>

@*
<MudDialog>
    <DialogContent>

        <EditForm id="_form" Model="@client" OnValidSubmit="@Upsert">
            <DataAnnotationsValidator />
            <MudPaper Class="d-flex" Elevation="0">
                <MudGrid Class="pa-4">
                
                        <MudTextField T="string" @bind-Value="client.Name" Label="اسم العميل" For="@(() => client.Name)" />
                   
                        <MudNumericField T="int?" @bind-Value="client.Phone" Label="رقم الهاتف" InputMode="InputMode.tel" Class="px-2" HideSpinButtons="true" Variant="Variant.Text" For="@(() => client.Phone)" />
                 
                        <MudTextField T="string" @bind-Value="client.Address" Label="العنوان" For="@(() => client.Address)" />

                    <MudNumericField T="decimal" @bind-Value="client.Balance" Label="الرصيد الحالي" ReadOnly="client.Id!=Guid.Empty" For="@(() => client.Balance)" />
                 
                    <MudItem xs="6">
                        <MudStack Row="true">
                            <MudCheckBox Class="my-4" T="bool" @bind-Value="client.IsSupplier" Label="مورد" For="@(()=>client.IsSupplier)" Color="Color.Primary"></MudCheckBox>
                            <MudCheckBox Class="my-4" T="bool" @bind-Value="client.IsCustomer" Label="زبون" For="@(()=>client.IsCustomer)" Color="Color.Primary"></MudCheckBox>
                        </MudStack>
                    </MudItem>
                    @if (id != Guid.Empty)
                    {
                        <MudItem xs="12" Class="d-flex">
                            <MudButton Style="align-self: end;" Variant="Variant.Filled" Color="Color.Primary" OnClick="@(()=>AddReceipt(FinancialId.Client))" EndIcon="@Icons.Material.Filled.Save" Class="px-6 ma-2">
                                <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">إيصال صرف\قبض</MudText>
                            </MudButton>
                            <MudButton Style="align-self: end;" Variant="Variant.Filled" Color="Color.Primary" OnClick="@(()=>AddReceipt(FinancialId.OpeningBalanceForClient))" EndIcon="@Icons.Material.Filled.Save" Class="px-6 ma-2">
                                <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">إيصال رصيد افتتاحي</MudText>
                            </MudButton>
                        </MudItem>
                    }
                </MudGrid>
            </MudPaper>
        </EditForm>

    </DialogContent>
    <DialogActions>
        <MudButton Style="align-self: end;" Variant="Variant.Filled" OnClick="@Cancel" Color="Color.Error" EndIcon="@Icons.Material.Filled.Cancel" Class="px-6 ma-2">
            <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">الغاء</MudText>
        </MudButton>

        <MudButton Style="align-self: end;" form="_form" Variant="Variant.Filled" ButtonType="ButtonType.Submit" Color="Color.Primary" EndIcon="@Icons.Material.Filled.Save" Class="px-6 ma-2">
            <MudText style="margin:0px;-webkit-user-select: none;user-select: none;" Align="Align.Center" Typo="Typo.h6">حفظ</MudText>
        </MudButton>
    </DialogActions>
</MudDialog>


<style>

    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }
</style> *@