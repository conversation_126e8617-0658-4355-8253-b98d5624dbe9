﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Configurations>Debug;Release;DebShit</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="CustomValidationAttribute\FormDataJsonBinder.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PosGTech.Models\PosGTech.Models.csproj" />
  </ItemGroup>

</Project>
