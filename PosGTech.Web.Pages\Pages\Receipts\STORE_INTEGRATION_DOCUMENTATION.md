# تكامل بيانات المتجر في طباعة الإيصالات

## نظرة عامة
تم تحديث صفحة `UpsertReceipt.razor` لتتكامل بشكل كامل مع بيانات المتجر المدخلة في `UpsertStoreSettings.razor`. يتم عرض جميع بيانات المتجر في الإيصال المطبوع بتصميم احترافي ودعم كامل للغة العربية.

## البيانات المعروضة في الإيصال

### 1. شعار المتجر (LogoPath)
- **الموقع**: الزاوية اليمنى العلوية من الإيصال
- **الحجم**: 70px × 70px
- **المعالجة**: 
  - دعم المسارات النسبية والمطلقة
  - دعم صور base64
  - معالجة أخطاء التحميل مع عرض اسم المتجر كبديل
- **التصميم**: خلفية زرقاء مع نص أبيض في حالة عدم وجود شعار

### 2. اسم المتجر (StoreName)
- **الموقع**: وسط الهيدر أسفل الشعار
- **التصميم**: خط كبير وعريض (18px, font-weight: 700)
- **اللون**: #1e293b (رمادي داكن)

### 3. اسم الشركة (CompanyName)
- **الموقع**: أسفل اسم المتجر في قسم تفاصيل الشركة
- **التصميم**: خط صغير (11px) باللون الرمادي
- **العرض**: يظهر فقط إذا كان محدداً

### 4. رقم الهاتف (CompanyPhone)
- **الموقع**: في قسم تفاصيل الشركة
- **التنسيق**: "هاتف: [رقم الهاتف]"
- **التصميم**: خط صغير (11px) باللون الرمادي

### 5. عنوان المتجر (StoreAddress)
- **الموقع**: في قسم تفاصيل الشركة
- **التصميم**: خط صغير (11px) باللون الرمادي
- **العرض**: يظهر فقط إذا كان محدداً

## التخطيط والتصميم

### هيكل الهيدر
```
┌─────────────────────────────────────────────────────────┐
│  [شعار]           [معلومات الشركة]           [التاريخ]  │
│   70x70              (وسط)                    مربع أزرق │
│                                                         │
│                 [عنوان الإيصال]                         │
│                 [رقم الإيصال]                          │
└─────────────────────────────────────────────────────────┘
```

### معلومات الشركة (الوسط)
- اسم المتجر (خط كبير وعريض)
- اسم الشركة (إذا كان محدداً)
- رقم الهاتف (إذا كان محدداً)
- عنوان المتجر (إذا كان محدداً)

## الكود المحدث

### تحميل بيانات المتجر
```csharp
private async Task LoadDefaultShopSettings()
{
    try
    {
        // محاولة جلب الإعدادات الافتراضية أولاً
        var defaultResponse = await _shopSettings.GetAll("ShopSettings/getDefaultShopSettings");
        if (defaultResponse.response == null && defaultResponse.list != null && defaultResponse.list.Any())
        {
            _defaultShopSettings = defaultResponse.list.First();
            return;
        }

        // إذا لم توجد إعدادات افتراضية، جلب أول إعدادات متاحة
        var allResponse = await _shopSettings.GetAll("ShopSettings/getAllShopSettings");
        if (allResponse.response == null && allResponse.list != null && allResponse.list.Any())
        {
            _defaultShopSettings = allResponse.list.First();
            return;
        }

        // قيم افتراضية في حالة عدم وجود إعدادات
        _defaultShopSettings = new ShopSettingsDTO
        {
            StoreName = "اسم المتجر",
            CompanyName = "اسم الشركة المطورة",
            CompanyPhone = "0123456789",
            StoreAddress = "عنوان المتجر"
        };
    }
    catch (Exception ex)
    {
        // معالجة الأخطاء مع قيم افتراضية
    }
}
```

### إعداد البيانات للعرض
```csharp
// إعداد بيانات المتجر
var logoUrl = "";
var storeName = _defaultShopSettings?.StoreName ?? "اسم المتجر";
var companyName = _defaultShopSettings?.CompanyName ?? "اسم الشركة";
var companyPhone = _defaultShopSettings?.CompanyPhone ?? "رقم الهاتف";
var storeAddress = _defaultShopSettings?.StoreAddress ?? "عنوان المتجر";

// تحويل مسار الشعار إلى URL كامل
if (!string.IsNullOrEmpty(_defaultShopSettings?.LogoPath))
{
    logoUrl = GetFullLogoUrl(_defaultShopSettings.LogoPath);
}
```

## المزايا المضافة

### 1. التحديث التلقائي
- يتم تحميل بيانات المتجر تلقائياً عند فتح صفحة الإيصال
- التحديثات في إعدادات المتجر تظهر فوراً في الإيصالات الجديدة

### 2. معالجة الأخطاء
- دعم كامل لحالات عدم وجود إعدادات
- قيم افتراضية احترافية
- معالجة أخطاء تحميل الشعار

### 3. المرونة في المسارات
- دعم المسارات النسبية والمطلقة للشعار
- دعم صور base64
- تحويل تلقائي للمسارات

### 4. التصميم المتجاوب
- تخطيط احترافي يناسب الطباعة
- دعم كامل للغة العربية واتجاه RTL
- ألوان مكتبية هادئة ومريحة للعين

## استخدام النظام

### للمستخدم النهائي
1. انتقل إلى صفحة إعدادات المتجر
2. أدخل بيانات المتجر (الاسم، الشركة، الهاتف، العنوان)
3. ارفع شعار المتجر
4. احفظ الإعدادات
5. ستظهر البيانات تلقائياً في جميع الإيصالات المطبوعة

### للمطور
- جميع البيانات محفوظة في `ShopSettingsDTO`
- التكامل يتم عبر `IGRepository<ShopSettingsDTO>`
- يمكن توسيع النظام لإضافة حقول جديدة بسهولة

## الملفات المتأثرة
- `UpsertReceipt.razor.cs` - الكود الرئيسي
- `UpsertStoreSettings.razor` - إدخال البيانات
- `ShopSettingsDTO.cs` - نموذج البيانات
- `ShopSettingsController.cs` - API endpoints

## التحسينات المستقبلية المقترحة
- إضافة معاينة مباشرة للإيصال عند تعديل إعدادات المتجر
- دعم شعارات متعددة لأنواع مختلفة من الإيصالات
- إضافة قوالب مختلفة للإيصالات
- دعم طباعة باركود أو QR code للمتجر
