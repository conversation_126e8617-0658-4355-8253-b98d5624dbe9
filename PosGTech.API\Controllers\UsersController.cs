﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Authentication;
using PosGTech.ModelsDTO.Treasury;
using PosGTech.ModelsDTO.Users;
using PosGTech.ModelsDTO.Authorization;
using System.Security.Claims;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UsersController(IUnitOfWork unitOfWork, UserManager<User> _userManager, IOptions<Jwt> _jwt, IMapper maper) : ControllerBase
    {
        [AllowAnonymous]
        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginUser user)
        {
            var auth = await unitOfWork.User.LoginAsync(user, _userManager, _jwt.Value);
            if (auth.isAuthenticated) return Ok(auth);
            return BadRequest(auth.Message);
        }
        [HttpGet("getAllTreasuriesForUser/{id}")]
        public async Task<IActionResult> GetAllTreasuriesForUser([FromRoute] Guid id)
        {
            var user = maper.Map<IEnumerable<UserTreasury>, IEnumerable<UserTreasuryCMDTO>>(await unitOfWork.UserTreasury.GetAll(x => x.UserId == id, includeProperties: $"{nameof(Treasury)}"));
            return Ok(user);
        }
        [HttpGet("getAllUserTreasury")]
        public async Task<IActionResult> GetAllUserTreasury()
        {
            var users = maper.Map<IEnumerable<UserTreasury>, IEnumerable<UserTreasuryCMDTO>>(await unitOfWork.UserTreasury.GetAll(includeProperties: $"{nameof(Models.User)},{nameof(Models.Treasury)}"));
            return Ok(users);
        }
        [HttpGet("getAllUsersCM")]
        public async Task<IActionResult> GetAllUsersCM()
        {
            var users = await unitOfWork.User.GetAllUserCM(_userManager);
            return Ok(users);
        }
        [HttpGet("getAllUsers")]
        public async Task<IActionResult> GetAllUsers()
        {
            var users = await unitOfWork.User.GetAllUser(_userManager);
            return Ok(users);
        }


        [HttpGet("getUserById/{id:guid}")]
        public async Task<IActionResult> GetUserById([FromRoute] Guid id)
        {
            var user = await unitOfWork.User.GetUserById(id, _userManager);
            return Ok(user);
        }



        [HttpPost("insertUser")]
        public async Task<IActionResult> InsertUser([FromBody] UserDTO model)
        {
            var res = await unitOfWork.User.AddUser(model, unitOfWork.Treasury.GetAll().GetAwaiter().GetResult().Select(x => x.Id).ToList(), _userManager);
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpPut("updateUser/{id:guid}")]
        public async Task<IActionResult> UpdateUser([FromRoute] Guid id, UserDTO model)
        {
            var res = await unitOfWork.User.UpdateUser(id, model, _userManager);
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpDelete("deleteUser/{id:guid}")]
        public async Task<IActionResult> DeleteUser([FromRoute] Guid id)
        {
            // الحصول على معرف المستخدم الحالي من الـ JWT token
            var currentUserIdString = User.FindFirst("sub")?.Value ?? User.FindFirst("nameid")?.Value;
            var currentUserId = !string.IsNullOrEmpty(currentUserIdString) ? Guid.Parse(currentUserIdString) : (Guid?)null;

            var res = await unitOfWork.User.DeleteUser(id, _userManager, currentUserId);
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }

        [HttpGet("getAllUsersIncludingHidden")]
        public async Task<IActionResult> GetAllUsersIncludingHidden()
        {
            var users = await unitOfWork.User.GetAllUserIncludingHidden(_userManager);
            return Ok(users);
        }

        [HttpDelete("permanentDeleteUser/{id:guid}")]
        public async Task<IActionResult> PermanentDeleteUser([FromRoute] Guid id)
        {
            var res = await unitOfWork.User.PermanentDeleteUser(id, _userManager);
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }

        [HttpPut("restoreUser/{id:guid}")]
        public async Task<IActionResult> RestoreUser([FromRoute] Guid id)
        {
            var res = await unitOfWork.User.RestoreUser(id, _userManager);
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }

        [HttpGet("isMainAdmin/{id:guid}")]
        public async Task<IActionResult> IsMainAdmin([FromRoute] Guid id)
        {
            var isMainAdmin = await unitOfWork.User.IsMainAdmin(id, _userManager);
            return Ok(new { IsMainAdmin = isMainAdmin });
        }
        [HttpGet("getRefreshTokenUser/{token}")]
        public async Task<IActionResult> GetRefreshTokenUser([FromRoute] string token)
        {
            var auth = await unitOfWork.User.RefreshTokenAsync(token, _userManager, _jwt.Value);
            if (auth.isAuthenticated) return Ok(auth);
            return BadRequest(auth.Message);
        }

        // ==================== إدارة الصلاحيات الإضافية - UserClaim ====================

        /// <summary>
        /// الحصول على الصلاحيات الفعلية للمستخدم
        /// </summary>
        [HttpGet("getUserEffectivePermissions/{id}")]
        public async Task<IActionResult> GetUserEffectivePermissions([FromRoute] Guid id)
        {
            try
            {
                var effectivePermissions = await unitOfWork.User.GetUserEffectivePermissions(id, _userManager);
                return Ok(effectivePermissions);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في الحصول على صلاحيات المستخدم", Error = ex.Message });
            }
        }

        /// <summary>
        /// إدارة صلاحيات المستخدم (إضافة/حذف صلاحيات إضافية ومحذوفة)
        /// </summary>
        [HttpPost("manageUserPermissions")]
        public async Task<IActionResult> ManageUserPermissions([FromBody] UserPermissionManagementDTO model)
        {
            try
            {
                // الحصول على معرف المستخدم الحالي
                var currentUserIdString = User.FindFirst("id")?.Value;
                var currentUserId = !string.IsNullOrEmpty(currentUserIdString) ? Guid.Parse(currentUserIdString) : (Guid?)null;
                model.ModifiedBy = currentUserId;

                var result = await unitOfWork.User.ManageUserPermissions(model, _userManager);

                if (result.State)
                    return Ok(result);
                else
                    return BadRequest(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في إدارة صلاحيات المستخدم", Error = ex.Message });
            }
        }

        /// <summary>
        /// الحصول على جميع Claims المستخدم
        /// </summary>
        [HttpGet("getUserClaims/{id}")]
        public async Task<IActionResult> GetUserClaims([FromRoute] Guid id)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(id.ToString());
                if (user == null)
                    return NotFound(new { Message = "المستخدم غير موجود" });

                var claims = await _userManager.GetClaimsAsync(user);
                var userClaims = claims.Select(c => new UserClaimDTO
                {
                    UserId = id,
                    ClaimType = c.Type,
                    ClaimValue = c.Value,
                    PermissionDescription = c.Type == "additional_permission" || c.Type == "removed_permission"
                        ? PermissionDescriptions.GetDescription(c.Value)
                        : c.Value
                }).ToList();

                return Ok(userClaims);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في الحصول على Claims المستخدم", Error = ex.Message });
            }
        }

        /// <summary>
        /// إضافة صلاحية إضافية للمستخدم
        /// </summary>
        [HttpPost("addAdditionalPermission")]
        public async Task<IActionResult> AddAdditionalPermission([FromBody] UserClaimDTO model)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(model.UserId.ToString());
                if (user == null)
                    return NotFound(new { Message = "المستخدم غير موجود" });

                // التحقق من أن الصلاحية موجودة في النظام
                if (!PermissionConstants.GetAllPermissions().Contains(model.ClaimValue))
                    return BadRequest(new { Message = "الصلاحية غير موجودة في النظام" });

                var claim = new Claim("additional_permission", model.ClaimValue);
                var result = await _userManager.AddClaimAsync(user, claim);

                if (result.Succeeded)
                    return Ok(new { Message = "تم إضافة الصلاحية بنجاح" });
                else
                    return BadRequest(new { Message = "فشل في إضافة الصلاحية", Errors = result.Errors });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في إضافة الصلاحية", Error = ex.Message });
            }
        }

        /// <summary>
        /// حذف صلاحية من الدور الأساسي للمستخدم
        /// </summary>
        [HttpPost("removeRolePermission")]
        public async Task<IActionResult> RemoveRolePermission([FromBody] UserClaimDTO model)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(model.UserId.ToString());
                if (user == null)
                    return NotFound(new { Message = "المستخدم غير موجود" });

                // التحقق من أن الصلاحية موجودة في دور المستخدم
                var userRoles = await _userManager.GetRolesAsync(user);
                var hasPermissionInRole = false;

                foreach (var roleName in userRoles)
                {
                    var role = await unitOfWork.Role.GetRoleByName(roleName);
                    if (role != null)
                    {
                        var rolePermissions = await unitOfWork.Role.GetRolePermissions(role.Id);
                        if (rolePermissions.Contains(model.ClaimValue))
                        {
                            hasPermissionInRole = true;
                            break;
                        }
                    }
                }

                if (!hasPermissionInRole)
                    return BadRequest(new { Message = "الصلاحية غير موجودة في دور المستخدم" });

                var claim = new Claim("removed_permission", model.ClaimValue);
                var result = await _userManager.AddClaimAsync(user, claim);

                if (result.Succeeded)
                    return Ok(new { Message = "تم حذف الصلاحية من الدور بنجاح" });
                else
                    return BadRequest(new { Message = "فشل في حذف الصلاحية", Errors = result.Errors });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في حذف الصلاحية", Error = ex.Message });
            }
        }

        /// <summary>
        /// إزالة Claim من المستخدم
        /// </summary>
        [HttpDelete("removeUserClaim")]
        public async Task<IActionResult> RemoveUserClaim([FromBody] UserClaimDTO model)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(model.UserId.ToString());
                if (user == null)
                    return NotFound(new { Message = "المستخدم غير موجود" });

                var claim = new Claim(model.ClaimType, model.ClaimValue);
                var result = await _userManager.RemoveClaimAsync(user, claim);

                if (result.Succeeded)
                    return Ok(new { Message = "تم إزالة الصلاحية بنجاح" });
                else
                    return BadRequest(new { Message = "فشل في إزالة الصلاحية", Errors = result.Errors });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Message = "خطأ في إزالة الصلاحية", Error = ex.Message });
            }
        }
    }
}
