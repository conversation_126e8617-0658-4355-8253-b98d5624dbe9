﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول فواتير المشتريات
    /// </summary>
    public class Purchase : BaseEntity
    {

        public int InvoiceNo { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public DateOnly Date { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
        public decimal Total { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(10, 3, ErrorMessage = "خطا في القيمة")]
        public decimal DiscountValue { get; set; } = decimal.Zero;
        public bool IsDiscountValue { get; set; }
        [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
        public decimal FinalTotal { get; set; }
        public string? Note { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid? ClientId { get; set; }
        public Client? Client { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid? StoreId { get; set; }
        public Store? Store { get; set; }
        public ICollection<PurchaseItem>? PurchaseItems { get; set; }
        public ICollection<Receipt>? Receipts { get; set; }
    }
}
