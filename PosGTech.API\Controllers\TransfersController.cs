﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TransfersController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("getAllTransfers")]
        public async Task<IActionResult> GetAllTransfers()
        {
            return Ok();
        }


        [HttpGet("getTransferById/{id:Guid}")]
        public async Task<IActionResult> GetTransferById([FromRoute] Guid id)
        {
            return Ok();
        }

        [HttpPost("insertTransfer")]
        public async Task<IActionResult> InsertTransfer([FromBody] NullDTO model)
        {
            return Ok();
        }


        [HttpPut("updateTransfer/{id:Guid}")]
        public async Task<IActionResult> UpdateTransfer([FromRoute] Guid id, NullDTO model)
        {
            return Ok();

        }



        [HttpDelete("deleteTransfer/{id:Guid}")]
        public async Task<IActionResult> DeleteTransfer([FromRoute] Guid id)
        {
            return Ok();
        }

    }
}
