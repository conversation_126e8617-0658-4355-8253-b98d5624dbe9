﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول انواع المصروفات
    /// </summary>
    [Index("Name", IsUnique = true)]
    public class Expense : BaseEntity
    {

        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        [StringLength(100, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
        public string Name { get; set; }
        public ICollection<Receipt>? Receipts { get; set; }
    }
}
