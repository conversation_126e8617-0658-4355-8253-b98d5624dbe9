﻿using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Units;

namespace PosGTech.DataAccess.Repository
{
    public class UnitRepository : Repository<Unit>, IUnitRepository
    {
        public UnitRepository(ApplicationDbContext db) : base(db)
        {
        }

        public void UpdateUnit(Unit oldUnit, UnitDTO newUnite)
        {
            oldUnit.Name = newUnite.Name;
            oldUnit.UpdatedAt = DateTime.Now;
            Update(oldUnit);
        }
    }
}
