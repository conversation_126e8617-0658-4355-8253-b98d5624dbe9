﻿@page "/listInventories"
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<AuthorizeView Policy="@PermissionConstants.InventoryView" Context="auth">
    <Authorized>
        <MudPaper Class="d-flex flex-column justify-space-evenly desktop rounded-0" Elevation="0">


    <MudTable Items="@inventories" Height="calc(100vh - 170px)" Loading="loading" Breakpoint="Breakpoint.Sm" Filter="new Func<InventoryDTO,bool>(FilterFunc1)" @bind-SelectedItem="selectedItem" Virtualize="true" Striped="true" Dense="true" FixedHeader="true" Elevation="4">

        <ToolBarContent>
            <MudText Align="@Align.Justify" Typo="Typo.h6" Style="margin-top:24px; margin-left:48px;">فواتير الشراء</MudText>
            <MudTextField @bind-Value="Search" Placeholder="بحث" Adornment="Adornment.End" AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium" Class="mt-0"></MudTextField>
            <MudSpacer />
            <MudButton Variant="Variant.Filled" EndIcon="@Icons.Material.Filled.Add" OnClick="()=>Upsert(Guid.Empty)" Color="Color.Primary">جديد</MudButton>
        </ToolBarContent>

        <HeaderContent>
            <MudTh Class="rounded-0">ت</MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<InventoryDTO, object>(x=>x.InvoiceNo)">رقم الفاتورة</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<InventoryDTO, object>(x=>x.Date)">التاريخ</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<InventoryDTO, object>(x=>x.Total)">الاجمالي</MudTableSortLabel></MudTh>
            <MudTh Class="rounded-0"></MudTh>
        </HeaderContent>
        <RowTemplate>
            <MudTd DataLabel="ت">@(inventories.ToList().IndexOf(context) + 1)</MudTd>
            <MudTd DataLabel="رقم الفاتورة">@context.InvoiceNo</MudTd>
                <MudTd DataLabel="التاريخ">@context.Date</MudTd>
                <MudTd DataLabel="الاجمالي">@context.Total</MudTd>
                <MudTd>
                    <MudIconButton Size="Size.Small" Color="Color.Info" Icon="@Icons.Material.Filled.Edit" OnClick="()=>Upsert(context.Id)" />
                    <MudIconButton Size="Size.Small" Color="Color.Error" Icon="@Icons.Material.Filled.Delete" OnClick="()=>Delete(context)" />
                </MudTd>
            </RowTemplate>

            <PagerContent>
                <MudTablePager style="height:24px;" PageSizeOptions="new int[] {int.MaxValue }"
                               RowsPerPageString="عدد الصفوف :"
                               InfoFormat="{last_item} -- {all_items}"
                               HorizontalAlignment="HorizontalAlignment.Center"
                               HideRowsPerPage="true"
                               HidePageNumber="true"
                               HidePagination="true" />
            </PagerContent>
        </MudTable>


    </MudPaper>

    <MudMessageBox @ref="mbox" Title="تنبيه" CancelText="إلغاء">
        <MessageContent>
            هل تريد حذف <b>@NameInventoryForDelete</b> ؟
    </MessageContent>
    <YesButton>
        <MudButton Variant="Variant.Filled" Color="Color.Error" StartIcon="@Icons.Material.Filled.DeleteForever">حذف!</MudButton>
    </YesButton>
</MudMessageBox>




<style>

    .desktop {
        height: calc(100vh - 50px);
        width: 100%;
        padding: 8px;
        display: flex;
        gap: 8px;
        overflow: hidden;
    }
</style>
    </Authorized>
    <NotAuthorized>
        <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Class="ma-4">
            ليس لديك صلاحية الوصول لعرض الجرد
        </MudAlert>
    </NotAuthorized>
</AuthorizeView>