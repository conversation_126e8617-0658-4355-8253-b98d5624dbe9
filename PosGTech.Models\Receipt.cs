﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations.Schema;

namespace PosGTech.Models
{
    /// <summary>
    /// جدول الواصلات
    /// </summary> 
    public class Receipt : BaseEntity
    {
        public int ReceiptNo { get; set; } = 0;
        public string? Statement { get; set; }
        public DateTime Date { get; set; }
        /// <summary>
        /// يكون صحيح في حال الصرف وخطأ في حال القبض
        /// </summary>
        public bool? IsExchange { get; set; }
        /// <summary>
        /// القيمة
        /// </summary>
        [PosGTech.CustomValidationAttribute.Precision(18, 3, ErrorMessage = "خطا في القيمة")]
        public decimal Value { get; set; }
        /// <summary>
        /// الخزينة المصروف والمقبوض منها واليها
        /// </summary>
        public Guid? UserTreasuryId { get; set; }
        [ForeignKey("UserTreasuryId")]
        public UserTreasury? UserTreasury { get; set; }
        /// <summary>
        /// الموظف
        /// </summary>
        public Guid? EmployeeId { get; set; }
        public Employee? Employee { get; set; }
        /// <summary>
        /// العميل
        /// </summary>
        public Guid? ClientId { get; set; }
        public Client? Client { get; set; }
        /// <summary>
        /// المصروفات
        /// </summary>
        public Guid? ExpenseId { get; set; }
        public Expense? Expense { get; set; }
        /// <summary>
        /// رقم فاتورة الشراء
        /// </summary>
        public Guid? PurchaseId { get; set; }
        public Purchase? Purchase { get; set; }
        /// <summary>
        /// رقم فاتورة المبيعات
        /// </summary>
        public Guid? SellId { get; set; }
        public Sell? Sell { get; set; }
        /// <summary>
        /// نوع العملية
        /// </summary>
        public Guid? FinancialId { get; set; }
        public Financial? Financial { get; set; }
        /// <summary>
        /// الخزينة المحول اليها في حالة التحويل 
        /// </summary>
        public Guid? ToTreasuryId { get; set; }
        [ForeignKey("ToTreasuryId")]
        public UserTreasury? ToTreasury { get; set; }
    }
}
