﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Expenses;

namespace PosGTech.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ExpensesController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
    {
        [HttpGet("getAllExpenses")]
        public async Task<IActionResult> GetAllExpenses()
        {
            var Expenses = maper.Map<IEnumerable<Expense>, IEnumerable<ExpenseDTO>>(await unitOfWork.Expense.GetAll());
            return Ok(Expenses);
        }


        [HttpGet("getExpenseById/{id:Guid}")]
        public async Task<IActionResult> GetExpenseById([FromRoute] Guid id)
        {
            var Expense = maper.Map<Expense, ExpenseDTO>(await unitOfWork.Expense.GetByIdAsync(id));
            return Ok(Expense);
        }



        [HttpPost("insertExpense")]
        public async Task<IActionResult> InsertExpense([FromBody] ExpenseDTO model)
        {
            var newExpense = maper.Map<ExpenseDTO, Expense>(model);
            unitOfWork.Expense.Add(newExpense);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpPut("updateExpense/{id:Guid}")]
        public async Task<IActionResult> UpdateExpense([FromRoute] Guid id, ExpenseDTO model)
        {
            var oldExpense = await unitOfWork.Expense.GetByIdAsync(id);
            if (oldExpense == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });

            unitOfWork.Expense.UpdateExpense(oldExpense, model);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }


        [HttpDelete("deleteExpense/{id:Guid}")]
        public async Task<IActionResult> DeleteExpense([FromRoute] Guid id)
        {
            var oldExpense = await unitOfWork.Expense.GetByIdAsync(id);
            if (oldExpense == null) return BadRequest(new ResponseVM() { Message = "البيانات غير موجودة" });
            unitOfWork.Expense.Remove(oldExpense);
            var res = await unitOfWork.SaveWithTransaction();
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }
    }
}
