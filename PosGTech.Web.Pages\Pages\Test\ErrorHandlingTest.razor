@page "/test/error-handling"
@using MudBlazor
@using PosGTech.Web.Services.Interfaces
@using Microsoft.JSInterop
@inject IErrorHandlingService ErrorHandlingService
@inject IErrorLoggingService ErrorLoggingService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject IJSRuntime JSRuntime

<PageTitle>اختبار نظام معالجة الأخطاء</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudGrid>
        <!-- Header Section -->
        <MudItem xs="12">
            <MudPaper Class="pa-4" Elevation="3">
                <MudText Typo="Typo.h4" Class="mb-2" dir="rtl">
                    اختبار نظام معالجة الأخطاء
                </MudText>
                <MudText Typo="Typo.body1" Color="Color.Secondary" dir="rtl">
                    هذه الصفحة تتيح لك اختبار جميع أنواع الأخطاء ومعالجتها في النظام
                </MudText>
            </MudPaper>
        </MudItem>

        <!-- System Status Section -->
        <MudItem xs="12" md="6">
            <MudPaper Class="pa-4" Elevation="2">
                <MudText Typo="Typo.h6" Class="mb-3" dir="rtl">
                    حالة النظام
                </MudText>
                
                <div class="d-flex align-center mb-2" dir="rtl">
                    <MudIcon Icon="@(IsSystemHealthy ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Error)"
                             Color="@(IsSystemHealthy ? Color.Success : Color.Error)" Class="ml-2" />
                    <MudText>النظام @(IsSystemHealthy ? "يعمل بشكل طبيعي" : "يواجه مشاكل")</MudText>
                </div>
                
                <MudText Typo="Typo.body2" Class="mb-2" dir="rtl">
                    عدد الاختبارات المنفذة: @TestCount
                </MudText>
                
                @if (LastTestTime.HasValue)
                {
                    <MudText Typo="Typo.body2" dir="rtl">
                        آخر اختبار: @LastTestTime.Value.ToString("yyyy-MM-dd HH:mm:ss")
                    </MudText>
                }
            </MudPaper>
        </MudItem>

        <!-- Exception Testing Section -->
        <MudItem xs="12" md="6">
            <MudPaper Class="pa-4" Elevation="2">
                <MudText Typo="Typo.h6" Class="mb-3" dir="rtl">
                    اختبار الأخطاء
                </MudText>
                
                <div class="d-flex flex-column gap-2" dir="rtl">
                    <MudButton Variant="Variant.Filled" 
                               Color="Color.Error" 
                               OnClick="@(() => TestException(new ArgumentNullException("testParam", "Test null argument")))"
                               FullWidth="true">
                        Null Argument Exception
                    </MudButton>
                    
                    <MudButton Variant="Variant.Filled" 
                               Color="Color.Warning" 
                               OnClick="@(() => TestException(new InvalidOperationException("Test invalid operation")))"
                               FullWidth="true">
                        Invalid Operation Exception
                    </MudButton>
                    
                    <MudButton Variant="Variant.Filled" 
                               Color="Color.Info" 
                               OnClick="@(() => TestException(new NotImplementedException("Test not implemented feature")))"
                               FullWidth="true">
                        Not Implemented Exception
                    </MudButton>
                    
                    <MudButton Variant="Variant.Filled" 
                               Color="Color.Dark" 
                               OnClick="@(() => TestException(new OutOfMemoryException("Test critical memory error")))"
                               FullWidth="true">
                        Critical Error (Out of Memory)
                    </MudButton>
                </div>
            </MudPaper>
        </MudItem>
        
        <!-- Navigation Testing Section -->
        <MudItem xs="12" md="6">
            <MudPaper Class="pa-4" Elevation="2">
                <MudText Typo="Typo.h6" Class="mb-3" dir="rtl">
                    اختبار التنقل إلى صفحات الأخطاء
                </MudText>
                
                <div class="d-flex flex-column gap-2" dir="rtl">
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               OnClick="@(() => Navigation.NavigateTo("/error/404"))"
                               FullWidth="true">
                        صفحة 404 - غير موجود
                    </MudButton>
                    
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Secondary"
                               OnClick="@(() => Navigation.NavigateTo("/error/500"))"
                               FullWidth="true">
                        صفحة 500 - خطأ خادم
                    </MudButton>
                    
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Tertiary"
                               OnClick="@(() => Navigation.NavigateTo("/error/403"))"
                               FullWidth="true">
                        صفحة 403 - غير مصرح
                    </MudButton>
                    
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Info"
                               OnClick="@(() => Navigation.NavigateTo("/error/general"))"
                               FullWidth="true">
                        صفحة خطأ عام
                    </MudButton>
                </div>
            </MudPaper>
        </MudItem>

        <!-- Async Error Testing Section -->
        <MudItem xs="12" md="6">
            <MudPaper Class="pa-4" Elevation="2">
                <MudText Typo="Typo.h6" Class="mb-3" dir="rtl">
                    اختبار الأخطاء غير المتزامنة
                </MudText>
                
                <div class="d-flex flex-column gap-2" dir="rtl">
                    <MudButton Variant="Variant.Text"
                               Color="Color.Error"
                               OnClick="TestAsyncException"
                               FullWidth="true">
                        اختبار خطأ غير متزامن
                    </MudButton>
                    
                    <MudButton Variant="Variant.Text"
                               Color="Color.Warning"
                               OnClick="TestTaskException"
                               FullWidth="true">
                        اختبار خطأ في Task
                    </MudButton>
                    
                    <MudButton Variant="Variant.Text"
                               Color="Color.Info"
                               OnClick="TestUnobservedTaskException"
                               FullWidth="true">
                        اختبار خطأ Task غير مراقب
                    </MudButton>
                </div>
            </MudPaper>
        </MudItem>

        <!-- Recent Errors Section -->
        <MudItem xs="12">
            <MudPaper Class="pa-4" Elevation="2">
                <MudText Typo="Typo.h6" Class="mb-3" dir="rtl">
                    الأخطاء الحديثة (@RecentErrors.Count)
                </MudText>
                
                @if (RecentErrors.Any())
                {
                    <MudText Typo="Typo.subtitle1" Class="mb-2" dir="rtl">
                        الأخطاء الحديثة (@RecentErrors.Count):
                    </MudText>
                    
                    <MudList T="string">
                        @foreach (var error in RecentErrors.Take(5))
                        {
                            <MudListItem T="string">
                                <div dir="rtl">
                                    <MudText Typo="Typo.body2">
                                        <strong>@error.ExceptionType:</strong> @error.Message
                                    </MudText>
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">
                                        @error.Timestamp.ToString("yyyy-MM-dd HH:mm:ss") - @error.Context
                                    </MudText>
                                </div>
                            </MudListItem>
                            <MudDivider />
                        }
                    </MudList>
                }
                else
                {
                    <MudAlert Severity="Severity.Info" dir="rtl">
                        لا توجد أخطاء مسجلة حتى الآن
                    </MudAlert>
                }
                
                <div class="d-flex gap-2 mt-3" dir="rtl">
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               OnClick="LoadRecentErrors"
                               StartIcon="Icons.Material.Filled.Refresh">
                        تحديث
                    </MudButton>
                    
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Secondary"
                               OnClick="ClearErrorLogs"
                               StartIcon="Icons.Material.Filled.Clear">
                        مسح السجلات
                    </MudButton>
                </div>
            </MudPaper>
        </MudItem>
        
        <!-- Error Boundary Testing Section -->
        <MudItem xs="12">
            <MudPaper Class="pa-4" Elevation="2">
                <MudText Typo="Typo.h6" Class="mb-3" dir="rtl">
                    اختبار Error Boundary
                </MudText>
                
                <div class="d-flex align-center gap-2 mb-3" dir="rtl">
                    <MudSwitch T="bool" @bind-Checked="ShouldThrowException" Color="Color.Error" />
                    <MudText>تفعيل رمي الأخطاء في العرض</MudText>
                </div>
                
                @if (ShouldThrowException)
                {
                    <MudAlert Severity="Severity.Warning" dir="rtl">
                        سيتم رمي خطأ في العرض التالي: @ThrowExceptionInRender()
                    </MudAlert>
                }
                else
                {
                    <MudAlert Severity="Severity.Success" dir="rtl">
                        العرض يعمل بشكل طبيعي
                    </MudAlert>
                }
            </MudPaper>
        </MudItem>

        <!-- JavaScript Error Testing Section -->
        <MudItem xs="12" md="6">
            <MudPaper Class="pa-4" Elevation="2">
                <MudText Typo="Typo.h6" Class="mb-3" dir="rtl">
                    اختبار أخطاء JavaScript
                </MudText>

                <div class="d-flex flex-column gap-2" dir="rtl">
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Warning"
                               OnClick="TestJavaScriptError"
                               FullWidth="true">
                        اختبار خطأ JavaScript
                    </MudButton>

                    <MudButton Variant="Variant.Filled"
                               Color="Color.Warning"
                               OnClick="TestPromiseRejection"
                               FullWidth="true">
                        اختبار Promise Rejection
                    </MudButton>

                    <MudButton Variant="Variant.Filled"
                               Color="Color.Warning"
                               OnClick="TestAsyncJSError"
                               FullWidth="true">
                        اختبار Async JS Error
                    </MudButton>

                    <MudButton Variant="Variant.Filled"
                               Color="Color.Warning"
                               OnClick="TestUnhandledPromiseRejection"
                               FullWidth="true">
                        اختبار Unhandled Promise Rejection
                    </MudButton>
                </div>
            </MudPaper>
        </MudItem>

        <!-- More JavaScript Error Testing -->
        <MudItem xs="12" md="6">
            <MudPaper Class="pa-4" Elevation="2">
                <MudText Typo="Typo.h6" Class="mb-3" dir="rtl">
                    اختبار أخطاء JavaScript متقدمة
                </MudText>

                <div class="d-flex flex-column gap-2" dir="rtl">
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Warning"
                               OnClick="TestDOMError"
                               FullWidth="true">
                        اختبار DOM Error
                    </MudButton>

                    <MudButton Variant="Variant.Filled"
                               Color="Color.Warning"
                               OnClick="TestTypeError"
                               FullWidth="true">
                        اختبار Type Error
                    </MudButton>

                    <MudButton Variant="Variant.Filled"
                               Color="Color.Warning"
                               OnClick="TestNetworkError"
                               FullWidth="true">
                        اختبار Network Error
                    </MudButton>

                    <MudButton Variant="Variant.Filled"
                               Color="Color.Warning"
                               OnClick="TestSyntaxError"
                               FullWidth="true">
                        اختبار Syntax Error
                    </MudButton>
                </div>
            </MudPaper>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    private List<PosGTech.Web.Services.Interfaces.ErrorInfo> RecentErrors = new();
    private int TestCount = 0;
    private DateTime? LastTestTime;
    private bool IsSystemHealthy = true;
    private bool ShouldThrowException = false;
    
    protected override async Task OnInitializedAsync()
    {
        await LoadRecentErrors();
    }
    
    private async Task TestException(Exception exception)
    {
        try
        {
            TestCount++;
            LastTestTime = DateTime.Now;
            IsSystemHealthy = false;
            
            await ErrorHandlingService.HandleExceptionAsync(exception);
            
            Snackbar.Add($"تم اختبار الخطأ: {exception.GetType().Name}", Severity.Info);
            
            await LoadRecentErrors();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"فشل في اختبار الخطأ: {ex.Message}", Severity.Error);
        }
    }
    
    private async Task TestAsyncException()
    {
        try
        {
            await Task.Run(async () =>
            {
                await Task.Delay(100);
                throw new InvalidOperationException("Test async exception");
            });
        }
        catch (Exception ex)
        {
            await TestException(ex);
        }
    }
    
    private async Task TestTaskException()
    {
        var task = Task.Run(() =>
        {
            throw new ApplicationException("Test task exception");
        });
        
        try
        {
            await task;
        }
        catch (Exception ex)
        {
            await TestException(ex);
        }
    }
    
    private async Task TestUnobservedTaskException()
    {
        // Create a task that will throw but not be awaited
        var _ = Task.Run(async () =>
        {
            await Task.Delay(50);
            throw new InvalidOperationException("Test unobserved task exception");
        });
        
        // Force garbage collection to trigger unobserved task exception
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        
        Snackbar.Add("تم إنشاء مهمة غير مراقبة - قد يظهر الخطأ في السجلات", Severity.Warning);
    }
    
    private async Task LoadRecentErrors()
    {
        try
        {
            RecentErrors = await ErrorLoggingService.GetRecentErrorsAsync(10);
            IsSystemHealthy = !RecentErrors.Any(e => e.Timestamp > DateTime.Now.AddMinutes(-5));
        }
        catch (Exception ex)
        {
            Snackbar.Add($"فشل في تحميل الأخطاء الحديثة: {ex.Message}", Severity.Error);
        }
    }
    
    private async Task ClearErrorLogs()
    {
        try
        {
            await ErrorLoggingService.ClearOldLogsAsync();
            await LoadRecentErrors();
            Snackbar.Add("تم مسح سجلات الأخطاء", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"فشل في مسح السجلات: {ex.Message}", Severity.Error);
        }
    }
    
    private string ThrowExceptionInRender()
    {
        throw new InvalidOperationException("Test exception from render method");
    }

    // JavaScript Error Testing Methods
    private async Task TestJavaScriptError()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("errorTesting.throwJavaScriptError");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"تم اختبار خطأ JavaScript: {ex.Message}", Severity.Info);
        }
    }

    private async Task TestPromiseRejection()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("errorTesting.throwPromiseRejection");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"تم اختبار Promise Rejection: {ex.Message}", Severity.Info);
        }
    }

    private async Task TestAsyncJSError()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("errorTesting.throwAsyncError");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"تم اختبار Async JS Error: {ex.Message}", Severity.Info);
        }
    }

    private async Task TestUnhandledPromiseRejection()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("errorTesting.createUnhandledPromiseRejection");
            Snackbar.Add("تم إنشاء Unhandled Promise Rejection - تحقق من وحدة التحكم", Severity.Warning);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"خطأ في اختبار Unhandled Promise Rejection: {ex.Message}", Severity.Error);
        }
    }

    private async Task TestDOMError()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("errorTesting.throwDOMError");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"تم اختبار DOM Error: {ex.Message}", Severity.Info);
        }
    }

    private async Task TestTypeError()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("errorTesting.throwTypeError");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"تم اختبار Type Error: {ex.Message}", Severity.Info);
        }
    }

    private async Task TestNetworkError()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("errorTesting.simulateNetworkError");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"تم اختبار Network Error: {ex.Message}", Severity.Info);
        }
    }

    private async Task TestSyntaxError()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("errorTesting.throwSyntaxError");
        }
        catch (Exception ex)
        {
            Snackbar.Add($"تم اختبار Syntax Error: {ex.Message}", Severity.Info);
        }
    }
}
