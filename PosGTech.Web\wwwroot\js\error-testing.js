// JavaScript functions for testing error handling

window.errorTesting = {
    // Test JavaScript runtime error
    throwJavaScriptError: function() {
        // This will throw a ReferenceError
        nonExistentFunction();
    },

    // Test promise rejection
    throwPromiseRejection: function() {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                reject(new Error("Test promise rejection"));
            }, 100);
        });
    },

    // Test async function error
    throwAsyncError: async function() {
        throw new Error("Test async function error");
    },

    // Test syntax error (this would be caught at parse time)
    throwSyntaxError: function() {
        try {
            eval("var x = ;"); // Invalid syntax
        } catch (e) {
            throw e;
        }
    },

    // Test network error simulation
    simulateNetworkError: async function() {
        try {
            const response = await fetch('/api/nonexistent-endpoint');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            throw new Error(`Network error: ${error.message}`);
        }
    },

    // Test unhandled promise rejection
    createUnhandledPromiseRejection: function() {
        // This promise rejection will not be caught
        new Promise((resolve, reject) => {
            setTimeout(() => {
                reject(new Error("Unhandled promise rejection test"));
            }, 100);
        });
    },

    // Test DOM manipulation error
    throwDOMError: function() {
        // Try to access non-existent element
        const element = document.getElementById('non-existent-element');
        element.innerHTML = 'This will fail'; // TypeError: Cannot set property 'innerHTML' of null
    },

    // Test type error
    throwTypeError: function() {
        const obj = null;
        obj.someMethod(); // TypeError: Cannot read property 'someMethod' of null
    }
};
