﻿using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.Web.Services.Interfaces;
using System.Net;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace PosGTech.Web.Services.Repositories
{
    public class GRepository<T>(HttpClient _httpClient, ILocalStorageService _localStorage, ILogger<GRepository<T>> _logger) : IGRepository<T> where T : class
    {
        /// <summary>
        /// فحص وإعداد رمز المصادقة للطلبات
        /// </summary>
        async Task CheckToken()
        {
            try
            {
                var token = await _localStorage.GetItemAsStringAsync("authToken");
                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogWarning("Authentication token is null or empty");
                    throw new UnauthorizedAccessException("رمز المصادقة غير موجود. يرجى تسجيل الدخول مرة أخرى.");
                }

                if (!_httpClient.DefaultRequestHeaders.Contains("Bearer"))
                    _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);
            }
            catch (UnauthorizedAccessException)
            {
                throw; // إعادة رفع استثناء المصادقة
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص رمز المصادقة");
                throw new InvalidOperationException("فشل في إعداد رمز المصادقة. يرجى إعادة تسجيل الدخول.", ex);
            }
        }
        /// <summary>
        /// جلب جميع العناصر من الخادم
        /// </summary>
        /// <param name="url">رابط API</param>
        /// <returns>قائمة العناصر أو رسالة خطأ</returns>
        public async Task<(IEnumerable<T>?, ResponseVM?)> GetAll(string url)
        {
            try
            {
                await CheckToken();
                var fullUrl = _httpClient.BaseAddress + url;
                _logger.LogInformation("جاري جلب البيانات من: {Url}", fullUrl);

                var response = await _httpClient.GetAsync(fullUrl);

                if (response.IsSuccessStatusCode)
                {
                    var data = await response.Content.ReadFromJsonAsync<T[]>();
                    _logger.LogInformation("تم جلب {Count} عنصر بنجاح من {Url}", data?.Length ?? 0, url);
                    return (data, null);
                }
                else
                {
                    var errorMessage = await GetDetailedErrorMessage(response, "جلب البيانات", url);
                    _logger.LogWarning("فشل في جلب البيانات من {Url}. كود الحالة: {StatusCode}", url, response.StatusCode);
                    return (null, new ResponseVM { State = false, Message = errorMessage });
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "خطأ في المصادقة أثناء جلب البيانات من {Url}", url);
                return (null, new ResponseVM { State = false, Message = "انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى." });
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "خطأ في الشبكة أثناء جلب البيانات من {Url}", url);
                var message = ex.Message.Contains("timeout") ? "انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى." :
                             ex.Message.Contains("network") ? "خطأ في الشبكة. يرجى التحقق من اتصال الإنترنت." :
                             "خطأ في الاتصال بالخادم. يرجى المحاولة لاحقاً.";
                return (null, new ResponseVM { State = false, Message = message });
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogWarning(ex, "تم إلغاء طلب جلب البيانات من {Url}", url);
                return (null, new ResponseVM { State = false, Message = "تم إلغاء العملية أو انتهت المهلة المحددة." });
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "خطأ في تحليل البيانات المستلمة من {Url}", url);
                return (null, new ResponseVM { State = false, Message = "خطأ في تنسيق البيانات المستلمة من الخادم." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب البيانات من {Url}", url);
                return (null, new ResponseVM { State = false, Message = $"خطأ غير متوقع: {ex.Message}" });
            }
        }

        /// <summary>
        /// استخراج رسالة خطأ مفصلة من استجابة HTTP
        /// </summary>
        /// <param name="response">استجابة HTTP</param>
        /// <param name="operation">نوع العملية</param>
        /// <param name="context">السياق الإضافي</param>
        /// <returns>رسالة خطأ مفصلة</returns>
        private async Task<string> GetDetailedErrorMessage(HttpResponseMessage response, string operation, string context = "")
        {
            try
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                var statusCode = (int)response.StatusCode;

                // محاولة قراءة ResponseVM من المحتوى
                if (!string.IsNullOrEmpty(errorContent))
                {
                    try
                    {
                        var errorResponse = JsonSerializer.Deserialize<ResponseVM>(errorContent, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        if (errorResponse != null && !string.IsNullOrEmpty(errorResponse.Message))
                        {
                            return $"{errorResponse.Message} (كود الخطأ: {statusCode})";
                        }
                    }
                    catch (JsonException)
                    {
                        // إذا فشل في تحليل JSON، استخدم المحتوى كما هو
                        if (errorContent.Length > 200)
                            errorContent = errorContent.Substring(0, 200) + "...";
                        return $"خطأ في {operation}: {errorContent} (كود الخطأ: {statusCode})";
                    }
                }

                // رسائل خطأ افتراضية حسب كود الحالة
                return response.StatusCode switch
                {
                    HttpStatusCode.BadRequest => $"طلب غير صحيح في {operation}. يرجى التحقق من البيانات المدخلة. (400)",
                    HttpStatusCode.Unauthorized => $"غير مصرح بالوصول لـ {operation}. يرجى تسجيل الدخول مرة أخرى. (401)",
                    HttpStatusCode.Forbidden => $"ممنوع الوصول لـ {operation}. ليس لديك صلاحية كافية. (403)",
                    HttpStatusCode.NotFound => $"المورد المطلوب لـ {operation} غير موجود. (404)",
                    HttpStatusCode.Conflict => $"تعارض في البيانات أثناء {operation}. (409)",
                    HttpStatusCode.UnprocessableEntity => $"البيانات المرسلة لـ {operation} غير صالحة. (422)",
                    HttpStatusCode.InternalServerError => $"خطأ داخلي في الخادم أثناء {operation}. (500)",
                    HttpStatusCode.BadGateway => $"خطأ في البوابة أثناء {operation}. (502)",
                    HttpStatusCode.ServiceUnavailable => $"الخدمة غير متاحة حالياً لـ {operation}. (503)",
                    HttpStatusCode.GatewayTimeout => $"انتهت مهلة البوابة أثناء {operation}. (504)",
                    _ => $"خطأ غير معروف في {operation}. كود الخطأ: {statusCode}"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استخراج رسالة الخطأ المفصلة");
                return $"خطأ في {operation}. كود الحالة: {(int)response.StatusCode}";
            }
        }

        /// <summary>
        /// جلب عنصر واحد بواسطة المعرف
        /// </summary>
        /// <param name="url">رابط API</param>
        /// <param name="id">معرف العنصر</param>
        /// <returns>العنصر أو رسالة خطأ</returns>
        public async Task<(T?, ResponseVM?)> GetByIdAsync(string url, object id)
        {
            try
            {
                if (id == null)
                {
                    _logger.LogWarning("محاولة جلب عنصر بمعرف فارغ من {Url}", url);
                    return (null, new ResponseVM { State = false, Message = "معرف العنصر مطلوب." });
                }

                await CheckToken();
                var fullUrl = $"{_httpClient.BaseAddress}{url}/{id}";
                _logger.LogInformation("جاري جلب العنصر {Id} من: {Url}", id, fullUrl);

                var response = await _httpClient.GetAsync(fullUrl);

                if (response.IsSuccessStatusCode)
                {
                    var data = await response.Content.ReadFromJsonAsync<T>();
                    _logger.LogInformation("تم جلب العنصر {Id} بنجاح من {Url}", id, url);
                    return (data, null);
                }
                else
                {
                    var errorMessage = await GetDetailedErrorMessage(response, "جلب العنصر", $"ID: {id}");
                    _logger.LogWarning("فشل في جلب العنصر {Id} من {Url}. كود الحالة: {StatusCode}", id, url, response.StatusCode);
                    return (null, new ResponseVM { State = false, Message = errorMessage });
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "خطأ في المصادقة أثناء جلب العنصر {Id} من {Url}", id, url);
                return (null, new ResponseVM { State = false, Message = "انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى." });
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "خطأ في الشبكة أثناء جلب العنصر {Id} من {Url}", id, url);
                var message = ex.Message.Contains("timeout") ? "انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى." :
                             ex.Message.Contains("network") ? "خطأ في الشبكة. يرجى التحقق من اتصال الإنترنت." :
                             "خطأ في الاتصال بالخادم. يرجى المحاولة لاحقاً.";
                return (null, new ResponseVM { State = false, Message = message });
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogWarning(ex, "تم إلغاء طلب جلب العنصر {Id} من {Url}", id, url);
                return (null, new ResponseVM { State = false, Message = "تم إلغاء العملية أو انتهت المهلة المحددة." });
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "خطأ في تحليل بيانات العنصر {Id} من {Url}", id, url);
                return (null, new ResponseVM { State = false, Message = "خطأ في تنسيق البيانات المستلمة من الخادم." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب العنصر {Id} من {Url}", id, url);
                return (null, new ResponseVM { State = false, Message = $"خطأ غير متوقع: {ex.Message}" });
            }
        }

        /// <summary>
        /// إضافة عنصر جديد
        /// </summary>
        /// <param name="url">رابط API</param>
        /// <param name="data">البيانات المراد إضافتها</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<ResponseVM> Insert(string url, T data)
        {
            try
            {
                if (data == null)
                {
                    _logger.LogWarning("محاولة إضافة بيانات فارغة إلى {Url}", url);
                    return new ResponseVM { State = false, Message = "البيانات المراد إضافتها مطلوبة." };
                }

                await CheckToken();
                var fullUrl = _httpClient.BaseAddress + url;
                _logger.LogInformation("جاري إضافة عنصر جديد إلى: {Url}", fullUrl);

                var response = await _httpClient.PostAsJsonAsync(fullUrl, data);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogInformation("تم إضافة العنصر بنجاح إلى {Url}", url);
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة العنصر بنجاح." };
                }
                else if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    var errorResult = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogWarning("فشل في إضافة العنصر إلى {Url}. طلب غير صحيح: {Message}", url, errorResult?.Message);
                    return errorResult ?? new ResponseVM { State = false, Message = "البيانات المرسلة غير صحيحة." };
                }
                else
                {
                    var errorMessage = await GetDetailedErrorMessage(response, "إضافة العنصر", url);
                    _logger.LogError("فشل في إضافة العنصر إلى {Url}. كود الحالة: {StatusCode}", url, response.StatusCode);
                    return new ResponseVM { State = false, Message = errorMessage };
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "خطأ في المصادقة أثناء إضافة عنصر إلى {Url}", url);
                return new ResponseVM { State = false, Message = "انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى." };
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "خطأ في الشبكة أثناء إضافة عنصر إلى {Url}", url);
                var message = ex.Message.Contains("timeout") ? "انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى." :
                             ex.Message.Contains("network") ? "خطأ في الشبكة. يرجى التحقق من اتصال الإنترنت." :
                             "خطأ في الاتصال بالخادم. يرجى المحاولة لاحقاً.";
                return new ResponseVM { State = false, Message = message };
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogWarning(ex, "تم إلغاء طلب إضافة عنصر إلى {Url}", url);
                return new ResponseVM { State = false, Message = "تم إلغاء العملية أو انتهت المهلة المحددة." };
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "خطأ في تحليل استجابة إضافة العنصر من {Url}", url);
                return new ResponseVM { State = false, Message = "خطأ في تنسيق استجابة الخادم." };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء إضافة عنصر إلى {Url}", url);
                return new ResponseVM { State = false, Message = $"خطأ غير متوقع: {ex.Message}" };
            }
        }
        /// <summary>
        /// تحديث عنصر موجود
        /// </summary>
        /// <param name="url">رابط API</param>
        /// <param name="data">البيانات المحدثة</param>
        /// <param name="id">معرف العنصر</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<ResponseVM> Update(string url, T data, Guid id)
        {
            try
            {
                if (data == null)
                {
                    _logger.LogWarning("محاولة تحديث بيانات فارغة للعنصر {Id} في {Url}", id, url);
                    return new ResponseVM { State = false, Message = "البيانات المراد تحديثها مطلوبة." };
                }

                if (id == Guid.Empty)
                {
                    _logger.LogWarning("محاولة تحديث عنصر بمعرف فارغ في {Url}", url);
                    return new ResponseVM { State = false, Message = "معرف العنصر المراد تحديثه مطلوب." };
                }

                await CheckToken();
                var fullUrl = $"{_httpClient.BaseAddress}{url}/{id}";
                _logger.LogInformation("جاري تحديث العنصر {Id} في: {Url}", id, fullUrl);

                var response = await _httpClient.PutAsJsonAsync(fullUrl, data);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogInformation("تم تحديث العنصر {Id} بنجاح في {Url}", id, url);
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث العنصر بنجاح." };
                }
                else if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    var errorResult = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogWarning("فشل في تحديث العنصر {Id} في {Url}. طلب غير صحيح: {Message}", id, url, errorResult?.Message);
                    return errorResult ?? new ResponseVM { State = false, Message = "البيانات المرسلة غير صحيحة." };
                }
                else
                {
                    var errorMessage = await GetDetailedErrorMessage(response, "تحديث العنصر", $"ID: {id}");
                    _logger.LogError("فشل في تحديث العنصر {Id} في {Url}. كود الحالة: {StatusCode}", id, url, response.StatusCode);
                    return new ResponseVM { State = false, Message = errorMessage };
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "خطأ في المصادقة أثناء تحديث العنصر {Id} في {Url}", id, url);
                return new ResponseVM { State = false, Message = "انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى." };
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "خطأ في الشبكة أثناء تحديث العنصر {Id} في {Url}", id, url);
                var message = ex.Message.Contains("timeout") ? "انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى." :
                             ex.Message.Contains("network") ? "خطأ في الشبكة. يرجى التحقق من اتصال الإنترنت." :
                             "خطأ في الاتصال بالخادم. يرجى المحاولة لاحقاً.";
                return new ResponseVM { State = false, Message = message };
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogWarning(ex, "تم إلغاء طلب تحديث العنصر {Id} في {Url}", id, url);
                return new ResponseVM { State = false, Message = "تم إلغاء العملية أو انتهت المهلة المحددة." };
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "خطأ في تحليل استجابة تحديث العنصر {Id} من {Url}", id, url);
                return new ResponseVM { State = false, Message = "خطأ في تنسيق استجابة الخادم." };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء تحديث العنصر {Id} في {Url}", id, url);
                return new ResponseVM { State = false, Message = $"خطأ غير متوقع: {ex.Message}" };
            }
        }
        /// <summary>
        /// إضافة عنصر جديد مع ملف
        /// </summary>
        /// <param name="url">رابط API</param>
        /// <param name="data">البيانات المراد إضافتها</param>
        /// <param name="file">الملف المراد رفعه</param>
        /// <param name="NameProp">اسم خاصية البيانات</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<ResponseVM> InsertWithFile(string url, T data, IBrowserFile file, string NameProp)
        {
            try
            {
                if (data == null)
                {
                    _logger.LogWarning("محاولة إضافة بيانات فارغة مع ملف إلى {Url}", url);
                    return new ResponseVM { State = false, Message = "البيانات المراد إضافتها مطلوبة." };
                }

                if (file == null)
                {
                    _logger.LogWarning("محاولة إضافة عنصر بدون ملف إلى {Url}", url);
                    return new ResponseVM { State = false, Message = "الملف مطلوب." };
                }

                // التحقق من حجم الملف (مثلاً 10 ميجابايت كحد أقصى)
                const long maxFileSize = 10 * 1024 * 1024; // 10 MB
                if (file.Size > maxFileSize)
                {
                    _logger.LogWarning("محاولة رفع ملف كبير الحجم ({Size} bytes) إلى {Url}", file.Size, url);
                    return new ResponseVM { State = false, Message = $"حجم الملف كبير جداً. الحد الأقصى المسموح: {maxFileSize / (1024 * 1024)} ميجابايت." };
                }

                await CheckToken();
                var fullUrl = _httpClient.BaseAddress + url;
                _logger.LogInformation("جاري إضافة عنصر مع ملف ({FileName}, {Size} bytes) إلى: {Url}", file.Name, file.Size, fullUrl);

                using var request = new HttpRequestMessage(HttpMethod.Post, fullUrl);
                using var content = new MultipartFormDataContent
                {
                    { new StreamContent(file.OpenReadStream()), "Img", file.Name},
                    { new StringContent(JsonSerializer.Serialize(data), Encoding.UTF8, "application/json"), NameProp },
                };

                request.Content = content;
                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogInformation("تم إضافة العنصر مع الملف بنجاح إلى {Url}", url);
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة العنصر والملف بنجاح." };
                }
                else if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    var errorResult = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogWarning("فشل في إضافة العنصر مع الملف إلى {Url}. طلب غير صحيح: {Message}", url, errorResult?.Message);
                    return errorResult ?? new ResponseVM { State = false, Message = "البيانات أو الملف المرسل غير صحيح." };
                }
                else
                {
                    var errorMessage = await GetDetailedErrorMessage(response, "إضافة العنصر مع الملف", $"File: {file.Name}");
                    _logger.LogError("فشل في إضافة العنصر مع الملف إلى {Url}. كود الحالة: {StatusCode}", url, response.StatusCode);
                    return new ResponseVM { State = false, Message = errorMessage };
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "خطأ في المصادقة أثناء إضافة عنصر مع ملف إلى {Url}", url);
                return new ResponseVM { State = false, Message = "انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى." };
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "خطأ في الشبكة أثناء إضافة عنصر مع ملف إلى {Url}", url);
                var message = ex.Message.Contains("timeout") ? "انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى." :
                             ex.Message.Contains("network") ? "خطأ في الشبكة. يرجى التحقق من اتصال الإنترنت." :
                             "خطأ في الاتصال بالخادم. يرجى المحاولة لاحقاً.";
                return new ResponseVM { State = false, Message = message };
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogWarning(ex, "تم إلغاء طلب إضافة عنصر مع ملف إلى {Url}", url);
                return new ResponseVM { State = false, Message = "تم إلغاء العملية أو انتهت المهلة المحددة." };
            }
            catch (IOException ex)
            {
                _logger.LogError(ex, "خطأ في قراءة الملف أثناء إضافة عنصر إلى {Url}", url);
                return new ResponseVM { State = false, Message = "خطأ في قراءة الملف. يرجى التأكد من سلامة الملف." };
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "خطأ في تحليل البيانات أثناء إضافة عنصر مع ملف إلى {Url}", url);
                return new ResponseVM { State = false, Message = "خطأ في تنسيق البيانات أو استجابة الخادم." };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء إضافة عنصر مع ملف إلى {Url}", url);
                return new ResponseVM { State = false, Message = $"خطأ غير متوقع: {ex.Message}" };
            }
        }
        /// <summary>
        /// تحديث عنصر موجود مع ملف
        /// </summary>
        /// <param name="url">رابط API</param>
        /// <param name="data">البيانات المحدثة</param>
        /// <param name="id">معرف العنصر</param>
        /// <param name="file">الملف الجديد</param>
        /// <param name="NameProp">اسم خاصية البيانات</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<ResponseVM> UpdateWithFile(string url, T data, Guid id, IBrowserFile file, string NameProp)
        {
            try
            {
                if (data == null)
                {
                    _logger.LogWarning("محاولة تحديث بيانات فارغة مع ملف للعنصر {Id} في {Url}", id, url);
                    return new ResponseVM { State = false, Message = "البيانات المراد تحديثها مطلوبة." };
                }

                if (id == Guid.Empty)
                {
                    _logger.LogWarning("محاولة تحديث عنصر بمعرف فارغ مع ملف في {Url}", url);
                    return new ResponseVM { State = false, Message = "معرف العنصر المراد تحديثه مطلوب." };
                }

                if (file == null)
                {
                    _logger.LogWarning("محاولة تحديث عنصر {Id} بدون ملف في {Url}", id, url);
                    return new ResponseVM { State = false, Message = "الملف مطلوب." };
                }

                // التحقق من حجم الملف
                const long maxFileSize = 10 * 1024 * 1024; // 10 MB
                if (file.Size > maxFileSize)
                {
                    _logger.LogWarning("محاولة رفع ملف كبير الحجم ({Size} bytes) لتحديث العنصر {Id} في {Url}", file.Size, id, url);
                    return new ResponseVM { State = false, Message = $"حجم الملف كبير جداً. الحد الأقصى المسموح: {maxFileSize / (1024 * 1024)} ميجابايت." };
                }

                await CheckToken();
                var fullUrl = $"{_httpClient.BaseAddress}{url}/{id}";
                _logger.LogInformation("جاري تحديث العنصر {Id} مع ملف ({FileName}, {Size} bytes) في: {Url}", id, file.Name, file.Size, fullUrl);

                using var request = new HttpRequestMessage(HttpMethod.Put, fullUrl);
                using var content = new MultipartFormDataContent
                {
                    { new StreamContent(file.OpenReadStream()), "Img", file.Name},
                    { new StringContent(JsonSerializer.Serialize(data), Encoding.UTF8, "application/json"), NameProp },
                };

                request.Content = content;
                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogInformation("تم تحديث العنصر {Id} مع الملف بنجاح في {Url}", id, url);
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث العنصر والملف بنجاح." };
                }
                else if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    var errorResult = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogWarning("فشل في تحديث العنصر {Id} مع الملف في {Url}. طلب غير صحيح: {Message}", id, url, errorResult?.Message);
                    return errorResult ?? new ResponseVM { State = false, Message = "البيانات أو الملف المرسل غير صحيح." };
                }
                else
                {
                    var errorMessage = await GetDetailedErrorMessage(response, "تحديث العنصر مع الملف", $"ID: {id}, File: {file.Name}");
                    _logger.LogError("فشل في تحديث العنصر {Id} مع الملف في {Url}. كود الحالة: {StatusCode}", id, url, response.StatusCode);
                    return new ResponseVM { State = false, Message = errorMessage };
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "خطأ في المصادقة أثناء تحديث العنصر {Id} مع ملف في {Url}", id, url);
                return new ResponseVM { State = false, Message = "انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى." };
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "خطأ في الشبكة أثناء تحديث العنصر {Id} مع ملف في {Url}", id, url);
                var message = ex.Message.Contains("timeout") ? "انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى." :
                             ex.Message.Contains("network") ? "خطأ في الشبكة. يرجى التحقق من اتصال الإنترنت." :
                             "خطأ في الاتصال بالخادم. يرجى المحاولة لاحقاً.";
                return new ResponseVM { State = false, Message = message };
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogWarning(ex, "تم إلغاء طلب تحديث العنصر {Id} مع ملف في {Url}", id, url);
                return new ResponseVM { State = false, Message = "تم إلغاء العملية أو انتهت المهلة المحددة." };
            }
            catch (IOException ex)
            {
                _logger.LogError(ex, "خطأ في قراءة الملف أثناء تحديث العنصر {Id} في {Url}", id, url);
                return new ResponseVM { State = false, Message = "خطأ في قراءة الملف. يرجى التأكد من سلامة الملف." };
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "خطأ في تحليل البيانات أثناء تحديث العنصر {Id} مع ملف في {Url}", id, url);
                return new ResponseVM { State = false, Message = "خطأ في تنسيق البيانات أو استجابة الخادم." };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء تحديث العنصر {Id} مع ملف في {Url}", id, url);
                return new ResponseVM { State = false, Message = $"خطأ غير متوقع: {ex.Message}" };
            }
        }

        /// <summary>
        /// حذف عنصر
        /// </summary>
        /// <param name="url">رابط API</param>
        /// <param name="id">معرف العنصر المراد حذفه</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<ResponseVM> Delete(string url, Guid id)
        {
            try
            {
                if (id == Guid.Empty)
                {
                    _logger.LogWarning("محاولة حذف عنصر بمعرف فارغ من {Url}", url);
                    return new ResponseVM { State = false, Message = "معرف العنصر المراد حذفه مطلوب." };
                }

                await CheckToken();
                var fullUrl = $"{_httpClient.BaseAddress}{url}/{id}";
                _logger.LogInformation("جاري حذف العنصر {Id} من: {Url}", id, fullUrl);

                var response = await _httpClient.DeleteAsync(fullUrl);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogInformation("تم حذف العنصر {Id} بنجاح من {Url}", id, url);
                    return result ?? new ResponseVM { State = true, Message = "تم حذف العنصر بنجاح." };
                }
                else if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    var errorResult = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogWarning("فشل في حذف العنصر {Id} من {Url}. طلب غير صحيح: {Message}", id, url, errorResult?.Message);
                    return errorResult ?? new ResponseVM { State = false, Message = "لا يمكن حذف هذا العنصر." };
                }
                else
                {
                    var errorMessage = await GetDetailedErrorMessage(response, "حذف العنصر", $"ID: {id}");
                    _logger.LogError("فشل في حذف العنصر {Id} من {Url}. كود الحالة: {StatusCode}", id, url, response.StatusCode);
                    return new ResponseVM { State = false, Message = errorMessage };
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "خطأ في المصادقة أثناء حذف العنصر {Id} من {Url}", id, url);
                return new ResponseVM { State = false, Message = "انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى." };
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "خطأ في الشبكة أثناء حذف العنصر {Id} من {Url}", id, url);
                var message = ex.Message.Contains("timeout") ? "انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى." :
                             ex.Message.Contains("network") ? "خطأ في الشبكة. يرجى التحقق من اتصال الإنترنت." :
                             "خطأ في الاتصال بالخادم. يرجى المحاولة لاحقاً.";
                return new ResponseVM { State = false, Message = message };
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogWarning(ex, "تم إلغاء طلب حذف العنصر {Id} من {Url}", id, url);
                return new ResponseVM { State = false, Message = "تم إلغاء العملية أو انتهت المهلة المحددة." };
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "خطأ في تحليل استجابة حذف العنصر {Id} من {Url}", id, url);
                return new ResponseVM { State = false, Message = "خطأ في تنسيق استجابة الخادم." };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء حذف العنصر {Id} من {Url}", id, url);
                return new ResponseVM { State = false, Message = $"خطأ غير متوقع: {ex.Message}" };
            }
        }
        /// <summary>
        /// رفع ملف
        /// </summary>
        /// <param name="url">رابط API</param>
        /// <param name="content">محتوى الملف</param>
        /// <returns>نتيجة العملية</returns>
        public async Task<ResponseVM> UploadFile(string url, HttpContent content)
        {
            try
            {
                if (content == null)
                {
                    _logger.LogWarning("محاولة رفع محتوى فارغ إلى {Url}", url);
                    return new ResponseVM { State = false, Message = "محتوى الملف مطلوب." };
                }

                await CheckToken();
                var fullUrl = _httpClient.BaseAddress + url;
                _logger.LogInformation("جاري رفع ملف إلى: {Url}", fullUrl);

                var response = await _httpClient.PostAsync(fullUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogInformation("تم رفع الملف بنجاح إلى {Url}", url);
                    return result ?? new ResponseVM { State = true, Message = "تم رفع الملف بنجاح." };
                }
                else if (response.StatusCode == HttpStatusCode.BadRequest)
                {
                    var errorResult = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    _logger.LogWarning("فشل في رفع الملف إلى {Url}. طلب غير صحيح: {Message}", url, errorResult?.Message);
                    return errorResult ?? new ResponseVM { State = false, Message = "الملف المرسل غير صحيح." };
                }
                else
                {
                    var errorMessage = await GetDetailedErrorMessage(response, "رفع الملف", url);
                    _logger.LogError("فشل في رفع الملف إلى {Url}. كود الحالة: {StatusCode}", url, response.StatusCode);
                    return new ResponseVM { State = false, Message = errorMessage };
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "خطأ في المصادقة أثناء رفع ملف إلى {Url}", url);
                return new ResponseVM { State = false, Message = "انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى." };
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "خطأ في الشبكة أثناء رفع ملف إلى {Url}", url);
                var message = ex.Message.Contains("timeout") ? "انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى." :
                             ex.Message.Contains("network") ? "خطأ في الشبكة. يرجى التحقق من اتصال الإنترنت." :
                             "خطأ في الاتصال بالخادم. يرجى المحاولة لاحقاً.";
                return new ResponseVM { State = false, Message = message };
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogWarning(ex, "تم إلغاء طلب رفع ملف إلى {Url}", url);
                return new ResponseVM { State = false, Message = "تم إلغاء العملية أو انتهت المهلة المحددة." };
            }
            catch (IOException ex)
            {
                _logger.LogError(ex, "خطأ في قراءة الملف أثناء رفعه إلى {Url}", url);
                return new ResponseVM { State = false, Message = "خطأ في قراءة الملف. يرجى التأكد من سلامة الملف." };
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "خطأ في تحليل استجابة رفع الملف من {Url}", url);
                return new ResponseVM { State = false, Message = "خطأ في تنسيق استجابة الخادم." };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء رفع ملف إلى {Url}", url);
                return new ResponseVM { State = false, Message = $"خطأ غير متوقع: {ex.Message}" };
            }
        }
        /// <summary>
        /// تحميل ملف
        /// </summary>
        /// <param name="url">رابط الملف</param>
        /// <returns>محتوى الملف كمصفوفة بايت أو null في حالة الفشل</returns>
        public async Task<byte[]?> DownloadFile(string url)
        {
            try
            {
                if (string.IsNullOrEmpty(url))
                {
                    _logger.LogWarning("محاولة تحميل ملف برابط فارغ");
                    return null;
                }

                await CheckToken();
                var fullUrl = _httpClient.BaseAddress + url;
                _logger.LogInformation("جاري تحميل ملف من: {Url}", fullUrl);

                var response = await _httpClient.GetAsync(fullUrl);

                if (response.IsSuccessStatusCode)
                {
                    var fileContent = await response.Content.ReadAsByteArrayAsync();
                    _logger.LogInformation("تم تحميل الملف بنجاح من {Url}. حجم الملف: {Size} bytes", url, fileContent.Length);
                    return fileContent;
                }
                else
                {
                    _logger.LogWarning("فشل في تحميل الملف من {Url}. كود الحالة: {StatusCode}", url, response.StatusCode);
                    return null;
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "خطأ في المصادقة أثناء تحميل ملف من {Url}", url);
                return null;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "خطأ في الشبكة أثناء تحميل ملف من {Url}", url);
                return null;
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogWarning(ex, "تم إلغاء طلب تحميل ملف من {Url}", url);
                return null;
            }
            catch (IOException ex)
            {
                _logger.LogError(ex, "خطأ في قراءة الملف أثناء تحميله من {Url}", url);
                return null;
            }
            catch (OutOfMemoryException ex)
            {
                _logger.LogError(ex, "نفدت الذاكرة أثناء تحميل ملف من {Url}", url);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء تحميل ملف من {Url}", url);
                return null;
            }
        }
    }
}
