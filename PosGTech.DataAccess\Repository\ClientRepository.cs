﻿using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Clients;

namespace PosGTech.DataAccess.Repository
{
    public class ClientRepository : Repository<Client>, IClientRepository
    {
        public ClientRepository(ApplicationDbContext db) : base(db)
        {
        }

        public void UpdateClient(Client oldClient, ClientDTO newClient)
        {
            oldClient.Name = newClient.Name;
            oldClient.Phone = newClient.Phone;
            oldClient.Address = newClient.Address;
            oldClient.IsCustomer = newClient.IsCustomer;
            oldClient.IsSupplier = newClient.IsSupplier;
            oldClient.UpdatedAt = DateTime.Now;
            Update(oldClient);
        }

        public async Task UpdateClientBalanceDec(Guid clientId, decimal newBalance)
        {
            var client = await GetByIdAsync(clientId);
            client.Balance -= newBalance;
            client.UpdatedAt = DateTime.Now;
            Update(client);
        }

        public async Task UpdateClientBalanceInc(Guid clientId, decimal newBalance)
        {
            var client = await GetByIdAsync(clientId);

            client.Balance += newBalance;
            client.UpdatedAt = DateTime.Now;
            Update(client);
        }
    }
}
