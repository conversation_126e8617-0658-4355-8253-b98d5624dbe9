<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال صرف نقدية - نصف صفحة A4</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .receipt-container {
            width: 210mm;
            height: 148.5mm;
            margin: 0 auto;
            background: #ffffff;
            border: 1px solid #ccc;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        /* الشريط الأزرق العلوي */
        .top-blue-stripe {
            height: 8px;
            background: linear-gradient(90deg, #1e40af 0%, #3b82f6 60%, transparent 100%);
            width: 65%;
        }
        
        /* الهيدر */
        .receipt-header {
            padding: 15px 20px;
            position: relative;
            background: #ffffff;
        }
        
        /* شعار الشركة في الزاوية اليمنى */
        .company-logo {
            position: absolute;
            top: 10px;
            right: 20px;
            width: 80px;
            height: 60px;
            background: #1e40af;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            line-height: 1.2;
        }
        
        /* عنوان الإيصال في الوسط */
        .receipt-title {
            text-align: center;
            margin: 20px 100px 10px 100px;
            font-size: 22px;
            font-weight: 700;
            color: #1e40af;
        }
        
        /* رقم الإيصال */
        .receipt-number {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-bottom: 20px;
        }
        
        /* التاريخ في الزاوية اليسرى */
        .date-section {
            position: absolute;
            top: 15px;
            left: 20px;
            font-size: 13px;
            color: #333;
            text-align: left;
        }
        
        /* محتوى الإيصال */
        .receipt-body {
            padding: 0 20px 20px 20px;
        }
        
        /* الحقول */
        .field-container {
            margin-bottom: 15px;
        }
        
        .field-label {
            font-size: 13px;
            color: #333;
            font-weight: 600;
            margin-bottom: 5px;
            text-align: right;
        }
        
        .field-value {
            background: #f0f0f0;
            border: 1px solid #ccc;
            padding: 8px 12px;
            font-size: 14px;
            color: #333;
            width: 100%;
            box-sizing: border-box;
        }
        
        /* حقل مزدوج */
        .dual-field {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            direction: rtl;
        }

        .dual-field .field-half {
            flex: 1;
        }

        .dual-field .field-half .field-label {
            font-size: 13px;
            color: #333;
            font-weight: 600;
            margin-bottom: 5px;
            text-align: right;
        }

        .dual-field .field-half .field-value {
            background: #f0f0f0;
            border: 1px solid #ccc;
            padding: 8px 12px;
            font-size: 14px;
            color: #333;
            width: 100%;
            box-sizing: border-box;
        }
        
        /* حقل البيان */
        .statement-field {
            margin: 20px 0;
        }
        
        .statement-value {
            background: #f0f0f0;
            border: 1px solid #ccc;
            padding: 12px;
            min-height: 60px;
            width: 100%;
            box-sizing: border-box;
            font-size: 14px;
            color: #333;
        }
        
        /* الفوتر */
        .receipt-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: linear-gradient(45deg, #60a5fa 0%, #1e40af 100%);
        }
        
        /* أقسام الفوتر */
        .footer-sections {
            display: flex;
            position: absolute;
            bottom: 50px;
            left: 20px;
            right: 20px;
        }
        
        .footer-left {
            flex: 1;
            text-align: left;
            font-size: 13px;
            color: #333;
            font-weight: 600;
        }
        
        .footer-center {
            flex: 1;
            text-align: center;
            background: #f0f0f0;
            border: 1px solid #ccc;
            padding: 8px;
            font-size: 14px;
            color: #333;
        }
        
        .footer-right {
            flex: 1;
            text-align: right;
            font-size: 13px;
            color: #333;
            font-weight: 600;
        }
        
        /* تنسيقات الطباعة */
        @media print {
            body {
                margin: 0;
                padding: 0;
                background: white;
            }
            
            .receipt-container {
                border: 1px solid #000;
                box-shadow: none;
                width: 210mm;
                height: 148.5mm;
                margin: 0;
            }
            
            .test-controls {
                display: none;
            }
            
            @page {
                size: A4 landscape;
                margin: 5mm;
            }
        }
        
        /* للعرض على الشاشة */
        @media screen {
            .receipt-container {
                transform: scale(0.9);
                transform-origin: top center;
                margin-top: 20px;
                margin-bottom: 40px;
            }
        }
        
        .test-controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .test-btn {
            padding: 12px 25px;
            background: #1e40af;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-family: 'Cairo', Arial, sans-serif;
            font-weight: 600;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            background: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <button class="test-btn" onclick="window.print()">🖨️ طباعة الإيصال</button>
        <button class="test-btn" onclick="location.reload()">🔄 إعادة تحميل</button>
    </div>

    <div class="receipt-container">
        <!-- الشريط الأزرق العلوي -->
        <div class="top-blue-stripe"></div>
        
        <div class="receipt-header">
            <!-- شعار الشركة -->
            <div class="company-logo">
                أضف<br>شعارك
            </div>
            
            <!-- التاريخ -->
            <div class="date-section">
                التاريخ: ٢٠١١
            </div>
            
            <!-- عنوان الإيصال -->
            <div class="receipt-title">إيصال صرف نقدية</div>
            <div class="receipt-number">رقم ٤٠٠٠</div>
        </div>
        
        <!-- محتوى الإيصال -->
        <div class="receipt-body">
            <!-- لصالح الأمر -->
            <div class="field-container">
                <div class="field-label">لصالح الأمر</div>
                <div class="field-value"></div>
            </div>
            
            <!-- مبلغ وقدره / نقداً أو صك -->
            <div class="dual-field">
                <div class="field-half">
                    <div class="field-label">مبلغ وقدره</div>
                    <div class="field-value">52.00 ريال</div>
                </div>
                <div class="field-half">
                    <div class="field-label">نقداً أو صك</div>
                    <div class="field-value">نقداً</div>
                </div>
            </div>
            
            <!-- وذلك قيمة -->
            <div class="field-container">
                <div class="field-label">وذلك قيمة</div>
                <div class="field-value"></div>
            </div>
        </div>
        
        <!-- أقسام الفوتر -->
        <div class="footer-sections">
            <div class="footer-left">قدم</div>
            <div class="footer-center"></div>
            <div class="footer-right">قسم</div>
        </div>
        
        <!-- الفوتر الأزرق -->
        <div class="receipt-footer"></div>
    </div>

</body>
</html>
