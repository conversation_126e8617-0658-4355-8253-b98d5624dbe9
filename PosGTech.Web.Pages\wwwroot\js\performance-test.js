// اختبار أداء البحث في الأصناف
class SearchPerformanceTester {
    constructor() {
        this.testResults = [];
        this.isRunning = false;
    }

    // قياس زمن الاستجابة للبحث
    async measureSearchResponse(searchTerm, iterations = 10) {
        const times = [];
        
        for (let i = 0; i < iterations; i++) {
            const startTime = performance.now();
            
            // محاكاة البحث
            await this.simulateSearch(searchTerm);
            
            const endTime = performance.now();
            times.push(endTime - startTime);
            
            // تأخير قصير بين الاختبارات
            await this.delay(50);
        }
        
        return {
            searchTerm,
            times,
            average: times.reduce((a, b) => a + b, 0) / times.length,
            min: Math.min(...times),
            max: Math.max(...times),
            median: this.calculateMedian(times)
        };
    }

    // محاكاة البحث
    async simulateSearch(searchTerm) {
        const searchInput = document.querySelector('input[placeholder*="الصنف"], input[aria-label*="الصنف"]');
        if (!searchInput) {
            throw new Error('لم يتم العثور على حقل البحث');
        }

        // محاكاة الكتابة
        searchInput.value = searchTerm;
        searchInput.dispatchEvent(new Event('input', { bubbles: true }));
        
        // انتظار ظهور النتائج
        await this.waitForResults();
    }

    // انتظار ظهور النتائج
    async waitForResults(timeout = 2000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const dropdown = document.querySelector('.mud-autocomplete .mud-list');
            if (dropdown && dropdown.children.length > 0) {
                return true;
            }
            await this.delay(10);
        }
        
        return false;
    }

    // حساب الوسيط
    calculateMedian(numbers) {
        const sorted = [...numbers].sort((a, b) => a - b);
        const middle = Math.floor(sorted.length / 2);
        
        if (sorted.length % 2 === 0) {
            return (sorted[middle - 1] + sorted[middle]) / 2;
        }
        
        return sorted[middle];
    }

    // تأخير
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // اختبار شامل للأداء
    async runFullPerformanceTest() {
        if (this.isRunning) {
            console.log('الاختبار قيد التشغيل بالفعل');
            return;
        }

        this.isRunning = true;
        this.testResults = [];
        
        console.log('🚀 بدء اختبار الأداء...');
        
        const testCases = [
            'ا',      // حرف واحد
            'اس',     // حرفان
            'اسم',    // ثلاثة أحرف
            'منتج',   // كلمة كاملة
            '123',    // أرقام (باركود)
            'غير موجود' // بحث بدون نتائج
        ];

        try {
            for (const testCase of testCases) {
                console.log(`📊 اختبار البحث عن: "${testCase}"`);
                const result = await this.measureSearchResponse(testCase, 5);
                this.testResults.push(result);
                
                console.log(`⏱️ متوسط الوقت: ${result.average.toFixed(2)}ms`);
                console.log(`📈 أسرع: ${result.min.toFixed(2)}ms, أبطأ: ${result.max.toFixed(2)}ms`);
                
                // تنظيف حقل البحث
                await this.clearSearch();
                await this.delay(100);
            }
            
            this.generateReport();
            
        } catch (error) {
            console.error('❌ خطأ في الاختبار:', error);
        } finally {
            this.isRunning = false;
        }
    }

    // تنظيف حقل البحث
    async clearSearch() {
        const searchInput = document.querySelector('input[placeholder*="الصنف"], input[aria-label*="الصنف"]');
        if (searchInput) {
            searchInput.value = '';
            searchInput.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    // إنشاء تقرير الأداء
    generateReport() {
        console.log('\n📋 تقرير الأداء النهائي:');
        console.log('================================');
        
        const overallTimes = this.testResults.flatMap(r => r.times);
        const overallAverage = overallTimes.reduce((a, b) => a + b, 0) / overallTimes.length;
        
        console.log(`📊 متوسط الأداء العام: ${overallAverage.toFixed(2)}ms`);
        console.log(`🎯 أسرع استجابة: ${Math.min(...overallTimes).toFixed(2)}ms`);
        console.log(`⚠️ أبطأ استجابة: ${Math.max(...overallTimes).toFixed(2)}ms`);
        
        // تقييم الأداء
        let performance_rating;
        if (overallAverage < 100) {
            performance_rating = '🟢 ممتاز';
        } else if (overallAverage < 200) {
            performance_rating = '🟡 جيد';
        } else if (overallAverage < 500) {
            performance_rating = '🟠 مقبول';
        } else {
            performance_rating = '🔴 يحتاج تحسين';
        }
        
        console.log(`🏆 تقييم الأداء: ${performance_rating}`);
        
        // تفاصيل كل اختبار
        console.log('\n📝 تفاصيل الاختبارات:');
        this.testResults.forEach(result => {
            console.log(`"${result.searchTerm}": ${result.average.toFixed(2)}ms (${result.times.length} اختبارات)`);
        });
        
        return {
            overallAverage,
            rating: performance_rating,
            details: this.testResults
        };
    }

    // اختبار الذاكرة
    measureMemoryUsage() {
        if (performance.memory) {
            const memory = performance.memory;
            console.log('💾 استخدام الذاكرة:');
            console.log(`المستخدمة: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
            console.log(`المخصصة: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
            console.log(`الحد الأقصى: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`);
            
            return {
                used: memory.usedJSHeapSize,
                total: memory.totalJSHeapSize,
                limit: memory.jsHeapSizeLimit
            };
        } else {
            console.log('⚠️ معلومات الذاكرة غير متوفرة في هذا المتصفح');
            return null;
        }
    }

    // اختبار سريع
    async quickTest() {
        console.log('⚡ اختبار سريع للأداء...');
        const result = await this.measureSearchResponse('منتج', 3);
        console.log(`⏱️ متوسط زمن الاستجابة: ${result.average.toFixed(2)}ms`);
        return result;
    }
}

// إنشاء مثيل عام للاختبار
window.searchTester = new SearchPerformanceTester();

// دوال مساعدة للوصول السريع
window.testSearchPerformance = () => window.searchTester.runFullPerformanceTest();
window.quickSearchTest = () => window.searchTester.quickTest();
window.checkMemory = () => window.searchTester.measureMemoryUsage();

console.log('🔧 أدوات اختبار الأداء جاهزة!');
console.log('استخدم: testSearchPerformance() للاختبار الشامل');
console.log('استخدم: quickSearchTest() للاختبار السريع');
console.log('استخدم: checkMemory() لفحص الذاكرة');
