using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using PosGTech.Web.Pages;
using PosGTech.Web.Services;
using Microsoft.Extensions.DependencyInjection;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

builder.Services.AddPages();

var app = builder.Build();

// Initialize global exception handling
try
{
    var globalExceptionHandler = app.Services.GetRequiredService<GlobalExceptionHandler>();
    await globalExceptionHandler.InitializeAsync();
}
catch (Exception ex)
{
    // Log initialization error but continue
    Console.WriteLine($"Failed to initialize global exception handler: {ex.Message}");
}

await app.RunAsync();
