﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components.Web;
using MudBlazor;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Categories;
using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.Units;
using PosGTech.Web.Services.Interfaces;

namespace PosGTech.Web.Pages.Pages.Items;

public partial class UpsertItem
{
    [CascadingParameter] MudDialogInstance MudDialog { get; set; }
    [Parameter]
    public Guid id { get; set; }
    ItemDTO _itemDTO = new();
    ItemDTO _itemSelected = new();

    ItemNumDTO ItemNumSelected = new();
    ItemUnitDTO ItemUnitSelected = new();
    IEnumerable<CategoryDTO> _CategoryList { get; set; }
    IEnumerable<UnitDTO> _UnitList { get; set; }
    IEnumerable<ItemDTO> _ItemsList { get; set; } = new List<ItemDTO>();
    [Inject]
    IGRepository<ItemDTO> _item { get; set; }
    [Inject]
    IGRepository<CategoryDTO> _Category { get; set; }
    [Inject]
    IGRepository<UnitDTO> _Unit { get; set; }
    MudSelect<Guid> refCategory { get; set; }
  
    MudTextField<string> refName { get; set; }

    MudRadioGroup<bool> refHaveExp;

    MudSelect<UnitDTO> refUnit { get; set; }
    MudNumericField<decimal> refSalePrice { get; set; }
    MudNumericField<decimal> refQuantity { get; set; }

    private MudTabs tabs;
    EditForm Form { get; set; }
    short index = -1;
    bool IsEdit = false;
    public async void keydownForm(KeyboardEventArgs args)
    {
        if (args.Key == "F2") Upsert();
        if (args.Key == "F8") NewItem();
    }
    async Task GetAllItem()
    {
        var res = await _item.GetAll("Items/getAllItems");
        if (res.response == null)
        {
            _ItemsList = res.list;
            StateHasChanged();
        }
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
        }
    }
    async Task GetById(Guid id)
    {

        try
        {
            var res = await _item.GetByIdAsync("Items/getItemById", id);
            if (res.response == null)
            {
                _itemDTO = res.model;
            }
            else
            {
                _snackbar.Add("خطأ في الاتصال", Severity.Error);
                MudDialog.Cancel();
            }
        }
        catch (Exception e)
        {

            _snackbar.Add("خطأ في الاتصال", Severity.Error);
        }
    }
    protected override async Task OnInitializedAsync()
    {
        _CategoryList = new List<CategoryDTO>();
        _UnitList = new List<UnitDTO>();
        GetAllItem();
        if (id != Guid.Empty) await GetById(id);
        var Catres = await _Category.GetAll("Categories/getAllCategories");
        if (Catres.response == null)
        {
            _CategoryList = Catres.list;
        }
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            MudDialog.Cancel();
        }
        var Unitres = await _Unit.GetAll("Units/GetAllUnits");
        if (Unitres.response == null)
        {
            _UnitList = Unitres.list;
        }
        else
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
            MudDialog.Cancel();
        }
    }
    void NewItem()
    {
        id = Guid.Empty;
        _itemDTO = new();
        _itemSelected = new();
        ItemNumSelected = new();
        ItemUnitSelected = new();
    }
    async void Upsert()
    {
        if (!Form.EditContext.Validate()) return;
        if (_itemDTO.ItemUnits.Count == 0)
        {
            _snackbar.Add("الرجاء إدخال وحدة أساسية اولا ثم وحدة فرعية", Severity.Error);
            return;
        }
        if (!_itemDTO.ItemUnits.Any(x => x.IsBasicUnit))
        {
            _snackbar.Add("الرجاء إدخال وحدة أساسية اولا ثم وحدة فرعية", Severity.Error);
            return;
        }
        bool IsAdd = id == Guid.Empty;
        ResponseVM response;
        if (IsAdd)
            response = await _item.Insert("Items/insertItem", _itemDTO);
        else
            response = await _item.Update("Items/updateItem", _itemDTO, id);

        if (response.State)
        {
            IsEdit = true;
            _snackbar.Add("تم الحفظ بنجاح", Severity.Success);
            if (IsAdd) id = Guid.Parse(response.Message);
            await GetById(id);
            if (IsAdd) _ItemsList = _ItemsList.Append(_itemDTO);
            else _ItemsList = _ItemsList.Select(x => x.Id == id ? _itemDTO : x);
            ChangeItemName(_itemDTO);
        }
        else
        {
            _snackbar.Add(response.Message, Severity.Error);
        }


    }
    private async Task<IEnumerable<ItemDTO>> SearchItem(string value, CancellationToken token)
    {
        if (string.IsNullOrEmpty(value))
            return _ItemsList;
        return _ItemsList.Where(x => x.Name.Contains(value, StringComparison.InvariantCultureIgnoreCase) || x.ItemNums?.Any(x => x.Barcode == value) == true);
    }
    async void KeyDownName(KeyboardEventArgs args)
    {
        if (args.Code == "Enter" || args.Code == "Tab") await refCategory.FocusAsync();
    }

    async void KeyDownCategory(KeyboardEventArgs args)
    {
        if (args.Code == "Enter" || args.Code == "Tab")
            await refUnit.FocusAsync();
    }

    async void KeyDownUnit(KeyboardEventArgs args)
    {
        if (args.Code == "Enter" || args.Code == "Tab")
        {
            if (!ItemUnitSelected.IsBasicUnit)
                await refQuantity.FocusAsync();
            else
                await refSalePrice.FocusAsync();
        }
    }

    async void KeyDownQuantity(KeyboardEventArgs args)
    {
        if (args.Code == "Enter" || args.Code == "Tab")
            await refSalePrice.FocusAsync();
    }



    void ChangeItemName(ItemDTO item)
    {
        _itemSelected = item;
        if (item is not null)
        {
            _itemDTO.ItemNums = item.ItemNums;
            _itemDTO.ItemUnits = item.ItemUnits;
            _itemDTO.CategoryId = item.CategoryId;
            _itemDTO.CostPrice = item.CostPrice;
            _itemDTO.IsHaveExp = item.IsHaveExp;
            _itemDTO.Name = item.Name;
            _itemDTO.Id = item.Id;
            _itemDTO.Quantity = item.Quantity;
        }
        else _itemDTO = new();
        StateHasChanged();
    }
    async Task AddItemNum()
    {

        if (ItemNumSelected.Barcode is null)
        {
            _snackbar.Add("الرجاء إدخال الباركود الخاص بالصنف", Severity.Error);
            StateHasChanged();

            return;
        }
        if (_itemDTO.ItemNums.Any(x => x.Barcode == ItemNumSelected.Barcode))
        {
            _snackbar.Add("عذراالباركود المدخل موجود من قبل", Severity.Error);
            StateHasChanged();

            return;
        }
        _itemDTO.ItemNums?.Add(ItemNumSelected);


        ItemNumSelected = new();
        StateHasChanged();

    }

    async Task AddItemUnit()
    {
        try
        {


            if (ItemUnitSelected.Unit is null)
            {
                _snackbar.Add("الرجاء إدخال وحدة البيع", Severity.Error);
                StateHasChanged();

                return;
            }
            if (ItemUnitSelected.IsBasicUnit && _itemDTO.ItemUnits.Any(x => x.IsBasicUnit && index != (short)_itemDTO.ItemUnits.ToList().IndexOf(x)))
            {
                _snackbar.Add("لايمكن إضافة وحدة أساسية اخرى", Severity.Error);
                StateHasChanged();

                return;
            }
            if (_itemDTO.ItemUnits.Any(x => x.Unit == ItemUnitSelected.Unit && index != (short)_itemDTO.ItemUnits.ToList().IndexOf(x)))
            {
                _snackbar.Add("عذرا الوحدة موجودة من قبل ", Severity.Error);
                StateHasChanged();

                return;
            }
            if (ItemUnitSelected.Quantity == 0 && !ItemUnitSelected.IsBasicUnit)
            {
                _snackbar.Add("عذرا الرجاء إدخال الكمية    ", Severity.Error);
                StateHasChanged();

                return;
            }
            if (ItemUnitSelected.IsBasicUnit) ItemUnitSelected.Quantity = 0;
            if (index == -1) _itemDTO.ItemUnits?.Add(ItemUnitSelected);

            index = -1;
            ItemUnitSelected = new();
            StateHasChanged();

        }
        catch (Exception e)
        {
            _snackbar.Add("خطأ في الاتصال", Severity.Error);
        }

    }
    void DeletItemNum(ItemNumDTO Item)
    {
        _itemDTO.ItemNums?.Remove(Item);

    }
    void DeletItemUnit(ItemUnitDTO Item)
    {
        _itemDTO.ItemUnits?.Remove(Item);

    }
    void EditItemUnit(ItemUnitDTO Item)
    {
        ItemUnitSelected = Item;
        index = (short)_itemDTO.ItemUnits.ToList().IndexOf(Item);
    }
    void ChangeIsBigger(bool IsBigger)
    {
        ItemUnitSelected.IsBigger = IsBigger;
        if (ItemUnitSelected.Quantity > 0 && !ItemUnitSelected.IsBasicUnit)
        {
            ChangePrice();
        }
    }
    void ChangeIsBasicUnit(bool IsBasicUnit)
    {
        ItemUnitSelected.IsBasicUnit = IsBasicUnit;
        if (IsBasicUnit)
        {
            ItemUnitSelected.Quantity = 0;
            ItemUnitSelected.SalePrice = 0;
        }
    }
    void ChangeQte(decimal qte)
    {
        ItemUnitSelected.Quantity = qte;
        if (qte > 0 && !ItemUnitSelected.IsBasicUnit)
        {
            ChangePrice();
        }
    }
    void ChangePrice()
    {
        if (_itemDTO.ItemUnits.Any(x => x.IsBasicUnit))
        {
            var basicUnit = _itemDTO.ItemUnits.First(x => x.IsBasicUnit);
            ItemUnitSelected.SalePrice = ItemExtensions.GetPriceUnitDTO(ItemUnitSelected, basicUnit.SalePrice);
        }
    }


    void Cancel() => MudDialog.Close(IsEdit);
}
