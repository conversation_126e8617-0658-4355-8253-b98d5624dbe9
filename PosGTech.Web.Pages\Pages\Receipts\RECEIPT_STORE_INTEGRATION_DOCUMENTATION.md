# توثيق تكامل بيانات المتجر مع طباعة الإيصالات

## نظرة عامة
تم تحديث صفحة `UpsertReceipt.razor` لتتضمن جلب وعرض بيانات المتجر من `UpsertStoreSettings.razor` في الإيصالات المطبوعة، بالإضافة إلى عرض اسم المستخدم المسجل الدخول.

## التحديثات المنفذة

### 1. إضافة المراجع المطلوبة
```csharp
using Microsoft.AspNetCore.Components.Authorization;
```

### 2. إضافة المتغيرات الجديدة
```csharp
private string _currentUserName = "";

[Inject]
AuthenticationStateProvider _auth { get; set; }
```

### 3. تحديث دالة التهيئة
```csharp
protected override async Task OnInitializedAsync()
{
    await Loading<PERSON>ata();
    await LoadDefaultShopSettings();
    await LoadCurrentUserName(); // إضافة جديدة
    // باقي الكود...
}
```

### 4. إضافة دالة تحميل اسم المستخدم
```csharp
/// <summary>
/// تحميل اسم المستخدم المسجل الدخول حالياً
/// </summary>
private async Task LoadCurrentUserName()
{
    try
    {
        var auth = await _auth.GetAuthenticationStateAsync();
        if (auth.User.Identity?.IsAuthenticated == true)
        {
            // محاولة الحصول على اسم المستخدم من Claims
            var userNameClaim = auth.User.Claims.FirstOrDefault(x => x.Type == "name")?.Value;
            var userIdClaim = auth.User.Claims.FirstOrDefault(x => x.Type == "id")?.Value;
            
            if (!string.IsNullOrEmpty(userNameClaim))
            {
                _currentUserName = userNameClaim;
            }
            else if (!string.IsNullOrEmpty(userIdClaim) && Guid.TryParse(userIdClaim, out var userId))
            {
                // إذا لم يكن الاسم متوفراً في Claims، جلبه من قاعدة البيانات
                var currentUser = Users.FirstOrDefault(x => x.Id == userId);
                _currentUserName = currentUser?.Name ?? "مستخدم غير معروف";
            }
            else
            {
                _currentUserName = "مستخدم غير معروف";
            }
        }
        else
        {
            _currentUserName = "غير مسجل الدخول";
        }
    }
    catch (Exception ex)
    {
        _snackbar.Add($"خطأ في تحميل معلومات المستخدم: {ex.Message}", Severity.Warning);
        _currentUserName = "مستخدم غير معروف";
    }
}
```

### 5. تحديث CSS للإيصال
```css
/* معلومات المستخدم */
.user-info {
    position: absolute;
    top: 50px;
    left: 20px;
    background: #f8fafc;
    color: #1e293b;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    border: 1px solid #e2e8f0;
    max-width: 120px;
    text-align: center;
}
```

### 6. تحديث HTML الإيصال
```html
<!-- معلومات المستخدم -->
<div class='user-info'>
    المستخدم: {_currentUserName}
</div>

<!-- معلومات الشركة في الوسط -->
<div class='company-info'>
    <div class='store-name'>{_defaultShopSettings?.StoreName ?? "اسم المتجر"}</div>
    <div class='company-details'>
        {(!string.IsNullOrEmpty(_defaultShopSettings?.CompanyName) ? _defaultShopSettings.CompanyName + "<br/>" : "")}
        {(!string.IsNullOrEmpty(_defaultShopSettings?.CompanyPhone) ? "هاتف: " + _defaultShopSettings.CompanyPhone + "<br/>" : "")}
        {(!string.IsNullOrEmpty(_defaultShopSettings?.StoreAddress) ? _defaultShopSettings.StoreAddress : "")}
    </div>
</div>
```

## البيانات المعروضة في الإيصال

### من إعدادات المتجر (ShopSettingsDTO):
- **اسم المتجر** (`StoreName`): يظهر في وسط الهيدر
- **اسم الشركة** (`CompanyName`): يظهر تحت اسم المتجر
- **رقم الهاتف** (`CompanyPhone`): يظهر مع تسمية "هاتف:"
- **عنوان المتجر** (`StoreAddress`): يظهر في أسفل معلومات الشركة
- **شعار المتجر** (`LogoPath`): يظهر في الزاوية اليمنى العلوية

### من معلومات المستخدم:
- **اسم المستخدم المسجل الدخول**: يظهر في الزاوية اليسرى تحت التاريخ

## مواقع عرض البيانات في الإيصال

```
┌─────────────────────────────────────────────────────────────┐
│  [شعار]           [معلومات الشركة]           [التاريخ]      │
│                      اسم المتجر                              │
│                     اسم الشركة                              │
│                   هاتف: رقم الهاتف                          │
│                    عنوان المتجر                             │
│                                              [المستخدم]     │
│                                                             │
│                    عنوان الإيصال                            │
│                     رقم الإيصال                             │
├─────────────────────────────────────────────────────────────┤
│  محتوى الإيصال...                                          │
└─────────────────────────────────────────────────────────────┘
```

## الميزات المضافة

### 1. التكامل التلقائي
- تحميل إعدادات المتجر تلقائياً عند فتح الصفحة
- عرض البيانات في الإيصال المطبوع دون تدخل المستخدم

### 2. معالجة الأخطاء
- في حالة عدم وجود إعدادات متجر، يتم استخدام قيم افتراضية
- معالجة أخطاء تحميل معلومات المستخدم

### 3. التصميم المتجاوب
- عرض البيانات بتنسيق مناسب للطباعة
- دعم الاتجاه من اليمين إلى اليسار (RTL)

### 4. الأمان
- التحقق من صحة المصادقة قبل عرض معلومات المستخدم
- معالجة حالات عدم وجود بيانات

## كيفية الاستخدام

1. **إعداد بيانات المتجر**: قم بإدخال بيانات المتجر من صفحة `UpsertStoreSettings.razor`
2. **إنشاء إيصال**: اذهب إلى صفحة `UpsertReceipt.razor` وأنشئ إيصال جديد
3. **طباعة الإيصال**: انقر على زر "طباعة" لعرض الإيصال مع جميع بيانات المتجر والمستخدم

## ملاحظات تقنية

- يتم تحميل البيانات مرة واحدة عند تهيئة الصفحة
- البيانات محفوظة في متغيرات خاصة للاستخدام في الطباعة
- يدعم النظام عدة تنسيقات طباعة (A4, A5, 80mm)
- جميع النصوص تدعم اللغة العربية والاتجاه RTL

## التحديثات المستقبلية المقترحة

1. إضافة إمكانية اختيار إعدادات متجر مختلفة لكل إيصال
2. حفظ تفضيلات المستخدم للطباعة
3. إضافة المزيد من معلومات المستخدم (الدور، القسم، إلخ)
4. تحسين التصميم والألوان
