﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.StoreItem;
using PosGTech.ModelsDTO.StoreItemExp;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace PosGTech.API.Controllers;

[Route("api/[controller]")]
[ApiController]
public class StoreItemsController(IUnitOfWork unitOfWork, IMapper maper) : ControllerBase
{
    [HttpGet("StoreItemsById/{id:Guid}")]
    public async Task<IActionResult> StoreItemsById([FromRoute] Guid id)
    {
        var resuilt = await unitOfWork.StoreItem.Select(x => new StoreItemDTO() { Item = maper.Map<ItemDTO>(x.Item), StoreId = x.StoreId, StoreItemExps = maper.Map<ICollection<StoreItemExpDTO>>(x.StoreItemExps.Where(z => z.Quantity > 0)) }, x => x.StoreId == id && x.StoreItemExps.Any(z => z.Quantity > 0), includeProperties: "Item.ItemNums,Item.ItemUnits.Unit");
        return Ok(resuilt);

    }

}
