# توثيق ميزة تصفية الأصناف حسب المخزن

## نظرة عامة
تم إضافة ميزة جديدة لصفحة قائمة الأصناف (ListItems.razor) تتيح للمستخدم تصفية الأصناف حسب المخزن المحدد أو عرض جميع الأصناف من جميع المخازن.

## الميزات المضافة

### 1. حقل اختيار المخزن (MudAutocomplete)
- **الموقع**: في شريط الأدوات (ToolBar) بجانب حقل البحث
- **النوع**: MudAutocomplete مع دعم البحث
- **الخيارات**: 
  - "عرض الكل" (افتراضي) - يعرض جميع الأصناف من جميع المخازن
  - قائمة بجميع المخازن المتاحة
- **دعم RTL**: مصمم للعمل مع اللغة العربية والتخطيط من اليمين إلى اليسار

### 2. مؤشر التصفية النشطة
- **عرض Chip**: يظهر اسم المخزن المحدد عند التصفية
- **إمكانية الإلغاء**: يمكن إلغاء التصفية بالنقر على زر الإغلاق في الـ Chip

### 3. تحديث فوري للبيانات
- **التحديث التلقائي**: تتحدث قائمة الأصناف فوريًا عند تغيير اختيار المخزن
- **عرض الكميات**: تظهر الكميات المناسبة حسب المخزن المحدد

## التغييرات التقنية

### 1. API Endpoint جديد
**الملف**: `PosGTech.API/Controllers/ItemsController.cs`
```csharp
[HttpGet("getAllItemsByStore")]
public async Task<IActionResult> GetAllItemsByStore([FromQuery] Guid? storeId = null)
```

**الوظيفة**:
- إذا كان `storeId` فارغ أو `Guid.Empty`: إرجاع جميع الأصناف مع الكميات الإجمالية
- إذا كان `storeId` محدد: إرجاع الأصناف مع الكميات من المخزن المحدد فقط

### 2. تحديثات الواجهة الأمامية
**الملف**: `PosGTech.Web.Pages/Pages/Items/ListItems.razor`

**المكونات المضافة**:
- `MudAutocomplete<StoreCMDTO>` لاختيار المخزن
- `MudChip` لعرض حالة التصفية النشطة
- تحسينات CSS للتصميم المتجاوب

**الملف**: `PosGTech.Web.Pages/Pages/Items/ListItems.razor.cs`

**الخدمات المضافة**:
- `IGRepository<StoreCMDTO> _store` لجلب قائمة المخازن
- `selectedStore` لتتبع المخزن المحدد
- `SearchStores()` لتصفية المخازن في الـ Autocomplete
- `OnStoreSelectionChanged()` للتعامل مع تغيير الاختيار

## كيفية الاستخدام

### 1. عرض جميع الأصناف
- افتح صفحة الأصناف
- تأكد من عدم اختيار أي مخزن (الحالة الافتراضية)
- ستظهر جميع الأصناف مع الكميات الإجمالية من جميع المخازن

### 2. تصفية حسب مخزن محدد
- انقر على حقل "المخزن" في شريط الأدوات
- ابحث عن المخزن المطلوب أو اختر من القائمة
- ستتحدث القائمة فوريًا لتظهر الأصناف من المخزن المحدد فقط

### 3. إلغاء التصفية
- انقر على زر الإغلاق (×) في الـ Chip الذي يظهر اسم المخزن
- أو امسح اختيار المخزن من الحقل
- ستعود القائمة لعرض جميع الأصناف

## التصميم المتجاوب

### دعم الدقات المختلفة
- **1920×1080**: عرض كامل مع جميع العناصر في سطر واحد
- **1280×1024**: تخطيط مرن مع تقليل المساحات
- **الشاشات الصغيرة**: تخطيط عمودي مع عناصر بعرض كامل

### دعم اللغة العربية
- **RTL Layout**: جميع العناصر مصممة للعمل مع التخطيط من اليمين إلى اليسار
- **النصوص العربية**: دعم كامل لعرض النصوص العربية
- **الأيقونات**: موضعة بشكل صحيح مع النصوص العربية

## الاختبارات المطلوبة

### 1. اختبار الوظائف الأساسية
- [ ] تحميل قائمة المخازن بنجاح
- [ ] عرض جميع الأصناف عند عدم اختيار مخزن
- [ ] تصفية الأصناف عند اختيار مخزن محدد
- [ ] صحة عرض الكميات حسب المخزن

### 2. اختبار واجهة المستخدم
- [ ] عمل البحث في قائمة المخازن
- [ ] ظهور وإخفاء مؤشر التصفية
- [ ] التصميم المتجاوب على دقات مختلفة
- [ ] دعم RTL والنصوص العربية

### 3. اختبار الأداء
- [ ] سرعة تحديث البيانات عند تغيير المخزن
- [ ] عدم وجود تأخير في الاستجابة
- [ ] استقرار الواجهة أثناء التحديث

## ملاحظات للمطورين

### نقاط مهمة
1. **فصل المفاهيم**: تم الحفاظ على الفصل بين Store (متجر) و Warehouse (مخزن)
2. **إعادة الاستخدام**: الكود مصمم ليكون قابل لإعادة الاستخدام في صفحات أخرى
3. **الأمان**: جميع الـ API endpoints محمية بالمصادقة
4. **الأداء**: استخدام تقنيات التحميل الفعال للبيانات

### إمكانيات التطوير المستقبلية
- إضافة تصفية متقدمة (حسب الفئة + المخزن)
- حفظ اختيار المخزن في Local Storage
- إضافة إحصائيات سريعة للمخزن المحدد
- دعم تصفية متعددة المخازن
