﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Users
{
    /// <summary>
    /// نموذج بيانات المستخدم المحدث لدعم نظام الدور الواحد مع الصلاحيات الإضافية
    /// </summary>
    public class UserDTO
    {
        public Guid? Id { get; set; }

        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public string Name { get; set; }

        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public string UserName { get; set; }

        [StringLength(100, ErrorMessage = "يجب ان يحتوي على حروف ورموز وارقام", MinimumLength = 6)]
        [DataType(DataType.Password)]
        public string? Password { get; set; }

        [DataType(DataType.Password)]
        [Compare("Password", ErrorMessage = "كلمة المرور غير مطابقة")]
        public string? ConfirmPassword { get; set; }

        /// <summary>
        /// الدور الأساسي الواحد للمستخدم
        /// تم تغييره من List إلى string واحد فقط
        /// </summary>
        [Required(ErrorMessage = "يجب اختيار دور للمستخدم")]
        public string RoleName { get; set; } = string.Empty;

        /// <summary>
        /// الصلاحيات الإضافية خارج نطاق الدور الأساسي
        /// يتم حفظها كـ UserClaim مع ClaimType = "additional_permission"
        /// </summary>
        public List<string> AdditionalPermissions { get; set; } = new List<string>();

        /// <summary>
        /// الصلاحيات المحذوفة من الدور الأساسي
        /// يتم حفظها كـ UserClaim مع ClaimType = "removed_permission"
        /// </summary>
        public List<string> RemovedPermissions { get; set; } = new List<string>();

        /// <summary>
        /// الصلاحيات الفعلية المحسوبة للمستخدم (للعرض فقط)
        /// = صلاحيات الدور + الصلاحيات الإضافية - الصلاحيات المحذوفة
        /// </summary>
        public List<string> EffectivePermissions { get; set; } = new List<string>();

        public bool State { get; set; } = true;
    }
}
