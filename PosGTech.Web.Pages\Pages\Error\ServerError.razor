@page "/error/500"
@page "/server-error"
@using MudBlazor
@using PosGTech.Web.Services.Interfaces
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject IErrorLoggingService ErrorLoggingService

<PageTitle>خطأ في الخادم - 500</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium" Class="mt-8">
    <MudPaper Elevation="3" Class="pa-8">
        <div dir="rtl" class="text-center">
            <!-- Error Icon -->
            <MudIcon Icon="@Icons.Material.Filled.ErrorOutline" 
                     Color="Color.Error" 
                     Size="Size.Large" 
                     Style="font-size: 6rem;" 
                     Class="mb-4" />
            
            <!-- Error Code -->
            <MudText Typo="Typo.h2" Color="Color.Error" Class="mb-2">
                500
            </MudText>
            
            <!-- Main Error Message -->
            <MudText Typo="Typo.h4" Class="mb-4">
                خطأ في الخادم
            </MudText>
            
            <!-- Description -->
            <MudText Typo="Typo.body1" Color="Color.Secondary" Class="mb-6">
                عذراً، حدث خطأ غير متوقع في الخادم. نحن نعمل على حل هذه المشكلة.
                يرجى المحاولة مرة أخرى بعد قليل أو الاتصال بالدعم الفني إذا استمرت المشكلة.
            </MudText>
            
            <!-- Error ID Display -->
            @if (!string.IsNullOrEmpty(ErrorId))
            {
                <MudAlert Severity="Severity.Warning" Class="mb-4">
                    <strong>رقم الخطأ:</strong> @ErrorId<br/>
                    <small>يرجى ذكر هذا الرقم عند الاتصال بالدعم الفني</small>
                </MudAlert>
            }
            
            <!-- Retry Counter -->
            @if (RetryCount > 0)
            {
                <MudAlert Severity="Severity.Info" Class="mb-4">
                    عدد المحاولات: @RetryCount
                </MudAlert>
            }
            
            <!-- Action Buttons -->
            <div class="d-flex justify-center flex-wrap gap-3 mb-6">
                <MudButton Variant="Variant.Filled" 
                           Color="Color.Primary" 
                           StartIcon="@Icons.Material.Filled.Refresh"
                           OnClick="RetryOperation"
                           Size="Size.Large"
                           Disabled="@IsRetrying">
                    @if (IsRetrying)
                    {
                        <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                        <span class="ml-2">جاري إعادة المحاولة...</span>
                    }
                    else
                    {
                        <span>إعادة المحاولة</span>
                    }
                </MudButton>
                
                <MudButton Variant="Variant.Outlined" 
                           Color="Color.Secondary" 
                           StartIcon="@Icons.Material.Filled.Home"
                           OnClick="NavigateToHome"
                           Size="Size.Large">
                    الصفحة الرئيسية
                </MudButton>
                
                <MudButton Variant="Variant.Text" 
                           Color="Color.Info" 
                           StartIcon="@Icons.Material.Filled.ArrowBack"
                           OnClick="GoBack"
                           Size="Size.Large">
                    العودة للخلف
                </MudButton>
            </div>
            
            <!-- Help Section -->
            <MudDivider Class="my-6" />
            
            <MudText Typo="Typo.h6" Class="mb-3">
                إذا استمرت المشكلة
            </MudText>
            
            <div class="d-flex justify-center flex-wrap gap-2">
                <MudChip T="bool" Icon="@Icons.Material.Filled.ContactSupport"
                         Color="Color.Success" 
                         Variant="Variant.Outlined"
                         OnClick="ContactSupport">
                    الاتصال بالدعم
                </MudChip>
                
                <MudChip T="bool" Icon="@Icons.Material.Filled.BugReport"
                         Color="Color.Warning" 
                         Variant="Variant.Outlined"
                         OnClick="ReportIssue">
                    الإبلاغ عن المشكلة
                </MudChip>
                
                <MudChip T="bool" Icon="@Icons.Material.Filled.History"
                         Color="Color.Info" 
                         Variant="Variant.Outlined"
                         OnClick="ShowRecentErrors">
                    سجل الأخطاء
                </MudChip>
            </div>
            
            <!-- Status Information -->
            <MudDivider Class="my-6" />
            
            <MudText Typo="Typo.caption" Color="Color.Secondary">
                وقت الخطأ: @ErrorTime.ToString("yyyy-MM-dd HH:mm:ss")
            </MudText>
        </div>
    </MudPaper>
</MudContainer>

@code {
    [Parameter] public string? ErrorMessage { get; set; }
    [Parameter] public string? ErrorId { get; set; }
    
    private int RetryCount = 0;
    private bool IsRetrying = false;
    private DateTime ErrorTime = DateTime.Now;
    
    protected override void OnInitialized()
    {
        // Generate error ID if not provided
        if (string.IsNullOrEmpty(ErrorId))
        {
            ErrorId = Guid.NewGuid().ToString("N")[..8].ToUpper();
        }
        
        // Log the server error
        LogServerError();
    }
    
    private async void LogServerError()
    {
        try
        {
            await ErrorLoggingService.LogExceptionAsync(
                new Exception($"Server Error 500: {ErrorMessage ?? "Unknown server error"}"),
                "ServerErrorPage",
                new Dictionary<string, object>
                {
                    ["ErrorId"] = ErrorId ?? "Unknown",
                    ["UserAgent"] = "Web Browser",
                    ["Timestamp"] = ErrorTime
                }
            );
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to log server error: {ex.Message}");
        }
    }
    
    private async Task RetryOperation()
    {
        IsRetrying = true;
        RetryCount++;
        StateHasChanged();
        
        try
        {
            // Simulate retry delay
            await Task.Delay(2000);
            
            // Try to navigate back to the previous page or refresh
            Navigation.NavigateTo(Navigation.Uri, forceLoad: true);
        }
        catch (Exception ex)
        {
            Snackbar.Add("فشلت إعادة المحاولة. يرجى المحاولة لاحقاً.", Severity.Error);
            Console.WriteLine($"Retry failed: {ex.Message}");
        }
        finally
        {
            IsRetrying = false;
            StateHasChanged();
        }
    }
    
    private void NavigateToHome()
    {
        try
        {
            Navigation.NavigateTo("/", forceLoad: true);
        }
        catch (Exception ex)
        {
            Snackbar.Add("حدث خطأ أثناء الانتقال إلى الصفحة الرئيسية", Severity.Error);
        }
    }
    
    private void GoBack()
    {
        try
        {
            Navigation.NavigateTo("javascript:history.back()", forceLoad: true);
        }
        catch (Exception ex)
        {
            NavigateToHome();
        }
    }
    
    private void ContactSupport()
    {
        var message = $"يرجى الاتصال بالدعم الفني وذكر رقم الخطأ: {ErrorId}";
        Snackbar.Add(message, Severity.Info, config =>
        {
            config.VisibleStateDuration = 10000;
        });
    }
    
    private void ReportIssue()
    {
        Snackbar.Add($"تم تسجيل المشكلة برقم: {ErrorId}. شكراً لك على الإبلاغ.", Severity.Success);
    }
    
    private async void ShowRecentErrors()
    {
        try
        {
            var recentErrors = await ErrorLoggingService.GetRecentErrorsAsync(5);
            var errorCount = recentErrors.Count;
            Snackbar.Add($"تم العثور على {errorCount} أخطاء حديثة في السجل", Severity.Info);
        }
        catch (Exception ex)
        {
            Snackbar.Add("لا يمكن الوصول إلى سجل الأخطاء حالياً", Severity.Warning);
        }
    }
}
