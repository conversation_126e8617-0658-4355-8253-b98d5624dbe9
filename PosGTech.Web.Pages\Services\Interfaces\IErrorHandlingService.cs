using System;
using System.Threading.Tasks;

namespace PosGTech.Web.Services.Interfaces
{
    /// <summary>
    /// Interface for centralized error handling service
    /// </summary>
    public interface IErrorHandlingService
    {
        /// <summary>
        /// Handles exceptions and determines appropriate user response
        /// </summary>
        /// <param name="exception">The exception to handle</param>
        /// <param name="context">Additional context about where the error occurred</param>
        /// <param name="showToUser">Whether to show error message to user</param>
        /// <returns>Task representing the async operation</returns>
        Task HandleExceptionAsync(Exception exception, string context = "", bool showToUser = true);

        /// <summary>
        /// Logs error details for debugging purposes
        /// </summary>
        /// <param name="exception">The exception to log</param>
        /// <param name="context">Additional context information</param>
        /// <param name="additionalData">Any additional data to log</param>
        /// <returns>Task representing the async operation</returns>
        Task LogErrorAsync(Exception exception, string context = "", object? additionalData = null);

        /// <summary>
        /// Gets user-friendly error message based on exception type
        /// </summary>
        /// <param name="exception">The exception to get message for</param>
        /// <returns>User-friendly error message in Arabic</returns>
        string GetUserFriendlyMessage(Exception exception);

        /// <summary>
        /// Determines error severity level
        /// </summary>
        /// <param name="exception">The exception to categorize</param>
        /// <returns>Error severity level</returns>
        ErrorSeverity GetErrorSeverity(Exception exception);

        /// <summary>
        /// Navigates to appropriate error page based on exception type
        /// </summary>
        /// <param name="exception">The exception that occurred</param>
        /// <param name="returnUrl">URL to return to after error is resolved</param>
        /// <returns>Task representing the async operation</returns>
        Task NavigateToErrorPageAsync(Exception exception, string? returnUrl = null);
    }

    /// <summary>
    /// Error severity levels
    /// </summary>
    public enum ErrorSeverity
    {
        Low,        // Minor issues, user can continue
        Medium,     // Moderate issues, some functionality affected
        High,       // Major issues, significant functionality affected
        Critical    // Critical issues, application may be unusable
    }
}
