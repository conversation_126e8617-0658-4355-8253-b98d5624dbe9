﻿using AutoMapper;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.Finacnial;
using PosGTech.ModelsDTO.Items;
using PosGTech.ModelsDTO.Sells;

namespace PosGTech.DataAccess.Repository
{
    public class SellRepository(ApplicationDbContext db, ISellItemRepository _sellItemRepository
        , IClientRepository _clientRepository
        , IReceiptRepository _receiptRepository
        , IStoreItemExpRepository _itemExpRepository) : Repository<Sell>(db), ISellRepository
    {
        private readonly ApplicationDbContext _db = db;

        /// <summary>
        /// Convert sell items quantities to basic unit for inventory operations
        /// </summary>
        private async Task<List<ItemForChangeQte>> ConvertSellItemsToBasicUnit(IEnumerable<SellItem> sellItems)
        {
            var itemsForQuantityChange = new List<ItemForChangeQte>();
            foreach (var sellItem in sellItems)
            {
                // Get the item unit to perform conversion
                var itemUnit = await _db.ItemUnits.FindAsync(sellItem.ItemUnitId);
                if (itemUnit != null)
                {
                    // Convert the sell quantity to basic unit quantity for inventory operations
                    var basicUnitQuantity = ItemExtensions.GetQuantityUnit(itemUnit, sellItem.Quantity);
                    itemsForQuantityChange.Add(new ItemForChangeQte()
                    {
                        ItemId = sellItem.StoreItemExpId,
                        Quantity = basicUnitQuantity,
                        ItemUnitId = sellItem.ItemUnitId
                    });
                }
            }
            return itemsForQuantityChange;
        }

        public async Task<Guid> AddSell(Sell sell, string userId, decimal paid = 0, Guid? treasuryId = null)
        {
            // Convert quantities to basic unit and reduce inventory
            var itemsForQuantityReduction = await ConvertSellItemsToBasicUnit(sell.SellItems);
            await _itemExpRepository.UpdateReduceQuantityDirectly(itemsForQuantityReduction);

            sell.InvoiceNo = ((await GetLastOrDefault(order: x => x.CreatedAt, tracked: false))?.InvoiceNo).GetValueOrDefault(0) + 1;
            await _clientRepository.UpdateClientBalanceDec(sell.ClientId.GetValueOrDefault(), sell.FinalTotal);
            if (paid > 0) await _receiptRepository.AddReceipt(new() { Sell = sell, ClientId = sell.ClientId, UserTreasuryId = treasuryId, Date = DateTime.Now, FinancialId = FinancialId.Sale, IsExchange = false, Value = paid });
            else Add(sell);
            return sell.Id;
        }
        public async Task UpdateSell(Sell oldSell, Sell newSell)
        {
            // Add back old quantities (convert to basic unit first)
            var oldItemsForQuantityAddition = await ConvertSellItemsToBasicUnit(oldSell.SellItems.Where(x => x.ReturnQuantity == 0));
            await _itemExpRepository.AddQuantityDirectly(oldItemsForQuantityAddition);

            _sellItemRepository.RemoveRange(oldSell.SellItems.Where(x => x.ReturnQuantity == 0));

            // Reduce new quantities (convert to basic unit first)
            var newItemsForQuantityReduction = await ConvertSellItemsToBasicUnit(newSell.SellItems.Where(x => x.ReturnQuantity == 0));
            await _itemExpRepository.UpdateReduceQuantityDirectly(newItemsForQuantityReduction);

            oldSell.SellItems = newSell.SellItems;
            _sellItemRepository.AddRange(newSell.SellItems.Where(x => x.ReturnQuantity == 0).ToList());
            await _clientRepository.UpdateClientBalanceInc(oldSell.ClientId.GetValueOrDefault(), oldSell.FinalTotal);
            await _clientRepository.UpdateClientBalanceDec(newSell.ClientId.GetValueOrDefault(), newSell.FinalTotal);

            oldSell.Total = newSell.Total;
            oldSell.FinalTotal = newSell.FinalTotal;
            oldSell.StoreId = newSell.StoreId;
            oldSell.Date = newSell.Date;
            oldSell.ClientId = newSell.ClientId;
            oldSell.UpdatedAt = DateTime.Now;
            Update(oldSell);

        }
        public async Task DeleteSell(Sell sell)
        {
            // Add back quantities (convert to basic unit first)
            var itemsForQuantityAddition = await ConvertSellItemsToBasicUnit(sell.SellItems);
            await _itemExpRepository.AddQuantityDirectly(itemsForQuantityAddition);

            await _clientRepository.UpdateClientBalanceInc(sell.ClientId.GetValueOrDefault(), sell.FinalTotal);
            Remove(sell);
        }

        public async Task<SellCMDTO> ReportSell(Guid id, IMapper mapper)
        {

            return (await Select(x => new SellCMDTO()
            {
                Total = x.Total,
                Date = x.Date,
                InvoiceNo = x.InvoiceNo,
                DiscountValue = x.DiscountValue,
                FinalTotal = x.FinalTotal,
                Client = mapper.Map<Client, ClientCMDTO>(x.Client),
                SellItemDTOs = mapper.Map<List<SellItemDTO>>(x.SellItems)
            }, x => x.Id == id, includeProperties: "Client,SellItems.ItemUnit.Item,SellItems.ItemUnit.Unit")).First();


        }
    }
}
