﻿using System.ComponentModel.DataAnnotations;

namespace PosGTech.Models
{
    public class Transfer : BaseEntity
    {
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid StoreFromId { get; set; }
        public Store? StoreFrom { get; set; }
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        public Guid StoreToId { get; set; }
        public Store? StoreTo { get; set; }
        public DateTime Date { get; set; }
        public string? Reciver { get; set; }
        public ICollection<TransferItem>? TransferItems { get; set; }
    }
}
