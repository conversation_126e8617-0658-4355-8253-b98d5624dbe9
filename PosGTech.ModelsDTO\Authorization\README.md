# نظام الصلاحيات المحسن لمشروع PosGTech

## نظرة عامة

تم تطوير نظام صلاحيات محسن يتبع أفضل الممارسات في إدارة الصلاحيات والأدوار. النظام يتكون من:

- **الصلاحيات (Permissions)**: ثابتة ومحددة مسبقاً في الكود
- **الأدوار (Roles)**: متغيرة وقابلة للإدارة من واجهة النظام
- **الربط**: الأدوار ترتبط بالصلاحيات عبر Claims

## مكونات النظام

### 1. PermissionConstants.cs
فئة ثابتة تحتوي على جميع الصلاحيات المطلوبة في النظام (68 صلاحية):

```csharp
public static class PermissionConstants
{
    // Items Management
    public const string ItemsView = "Items.View";
    public const string ItemsAdd = "Items.Add";
    public const string ItemsEdit = "Items.Edit";
    public const string ItemsDelete = "Items.Delete";
    
    // ... باقي الصلاحيات
}
```

### 2. PermissionDescriptions.cs
يحتوي على الأوصاف العربية لجميع الصلاحيات:

```csharp
public static readonly Dictionary<string, string> Descriptions = new()
{
    { PermissionConstants.ItemsView, "عرض الأصناف" },
    { PermissionConstants.ItemsAdd, "إضافة صنف جديد" },
    // ... باقي الأوصاف
};
```

### 3. PermissionGroups.cs
تجميع الصلاحيات حسب الوحدات الوظيفية لسهولة الإدارة:

```csharp
public static readonly Dictionary<string, List<string>> Groups = new()
{
    {
        "إدارة الأصناف والتصنيفات",
        new List<string> { /* صلاحيات الأصناف */ }
    },
    // ... باقي المجموعات
};
```

### 4. SeedData.GetDefaultRoles()
دالة داخل SeedData.cs تحتوي على الأدوار الافتراضية مع توزيع الصلاحيات:

```csharp
private static List<(string Name, string Description, List<string> Permissions)> GetDefaultRoles()
{
    return new List<(string Name, string Description, List<string> Permissions)>
    {
        ("SystemAdmin", "مدير النظام", PermissionConstants.GetAllPermissions()),
        ("SalesEmployee", "موظف المبيعات", /* صلاحيات المبيعات */),
        // ... باقي الأدوار
    };
}
```

## الأدوار الافتراضية

### 1. مدير النظام (SystemAdmin)
- **الصلاحيات**: جميع الصلاحيات (68 صلاحية)
- **الوصف**: صلاحيات كاملة على جميع وحدات النظام

### 2. موظف المبيعات (SalesEmployee)
- **الصلاحيات**: 15 صلاحية
- **الوحدات**: المبيعات، العملاء، التقارير، عرض الأصناف

### 3. مسؤول المشتريات (PurchaseManager)
- **الصلاحيات**: 25 صلاحية
- **الوحدات**: المشتريات، المخزون، الأصناف، الموردين

### 4. محاسب (Accountant)
- **الصلاحيات**: 18 صلاحية
- **الوحدات**: التقارير المالية، الخزائن، المصروفات

### 5. مدير المخزون (InventoryManager)
- **الصلاحيات**: 22 صلاحية
- **الوحدات**: المخزون، الأصناف، المخازن، الجرد

## الصلاحيات حسب الوحدات

| الوحدة | عدد الصلاحيات | الصلاحيات |
|--------|---------------|-----------|
| **الأصناف** | 4 | View, Add, Edit, Delete |
| **التصنيفات** | 4 | View, Add, Edit, Delete |
| **الوحدات** | 4 | View, Add, Edit, Delete |
| **المشتريات** | 4 | View, Add, Edit, Delete |
| **المبيعات** | 4 | View, Add, Edit, Delete |
| **العملاء** | 4 | View, Add, Edit, Delete |
| **الموظفين** | 4 | View, Add, Edit, Delete |
| **المخازن** | 4 | View, Add, Edit, Delete |
| **الخزائن** | 4 | View, Add, Edit, Delete |
| **الجرد** | 4 | View, Add, Edit, Delete |
| **السندات** | 4 | View, Add, Edit, Delete |
| **المصروفات** | 4 | View, Add, Edit, Delete |
| **التقارير** | 4 | View, Generate, Export, Advanced |
| **المستخدمين** | 4 | View, Add, Edit, Delete |
| **الأدوار** | 4 | View, Add, Edit, Delete |
| **النظام** | 4 | Settings, Backup, Restore, Logs |
| **المجموع** | **68** | صلاحية |

## كيفية الاستخدام

### 1. في Controllers
```csharp
[Authorize(Policy = "permission")]
[HttpGet]
public async Task<IActionResult> GetItems()
{
    // التحقق من الصلاحية
    if (!User.HasClaim("permission", PermissionConstants.ItemsView))
        return Forbid();
    
    // منطق العمل
}
```

### 2. في Blazor Components
```razor
<AuthorizeView Policy="@PermissionConstants.ItemsAdd">
    <Authorized>
        <MudButton>إضافة صنف</MudButton>
    </Authorized>
</AuthorizeView>
```

### 3. في Services
```csharp
public class ItemService
{
    public async Task<bool> CanUserViewItems(ClaimsPrincipal user)
    {
        return user.HasClaim("permission", PermissionConstants.ItemsView);
    }
}
```

## المستخدم الافتراضي

- **اسم المستخدم**: Admin
- **كلمة المرور**: Admin123*
- **البريد الإلكتروني**: <EMAIL>
- **الدور**: SystemAdmin (جميع الصلاحيات)

## ملاحظات مهمة

1. **الصلاحيات ثابتة**: لا تتغير إلا بتحديث الكود
2. **الأدوار متغيرة**: يمكن إدارتها من واجهة النظام
3. **Claims-based**: النظام يستخدم Claims للتحقق من الصلاحيات
4. **قابل للتوسع**: يمكن إضافة صلاحيات وأدوار جديدة بسهولة
5. **متوافق**: مع ASP.NET Identity Framework
6. **موقع الملفات**: تم نقل ملفات الصلاحيات إلى `PosGTech.ModelsDTO.Authorization`
7. **الأدوار الافتراضية**: تم تحويلها إلى دالة `GetDefaultRoles()` داخل `SeedData.cs`

## التحديثات المستقبلية

لإضافة صلاحيات جديدة:
1. أضف الصلاحية في `PermissionConstants.cs`
2. أضف الوصف في `PermissionDescriptions.cs`
3. أضف الصلاحية للمجموعة المناسبة في `PermissionGroups.cs`
4. حدث الأدوار في دالة `GetDefaultRoles()` في `SeedData.cs` حسب الحاجة

## التغييرات الأخيرة

- ✅ **نقل الملفات**: تم نقل جميع ملفات Authorization من `PosGTech.Models` إلى `PosGTech.ModelsDTO`
- ✅ **تحديث Namespace**: تم تحديث namespace من `PosGTech.Models.Authorization` إلى `PosGTech.ModelsDTO.Authorization`
- ✅ **حذف DefaultRoles.cs**: تم حذف الملف وتحويل محتواه إلى دالة `GetDefaultRoles()` داخل `SeedData.cs`
- ✅ **تحديث المراجع**: تم تحديث using statements في `SeedData.cs`
