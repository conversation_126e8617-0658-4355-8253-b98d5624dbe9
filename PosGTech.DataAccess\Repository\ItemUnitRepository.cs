﻿using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.Items;

namespace PosGTech.DataAccess.Repository
{
    public class ItemUnitRepository : Repository<ItemUnit>, IItemUnitRepository
    {
        public ItemUnitRepository(ApplicationDbContext db) : base(db)
        {
        }
        /// <summary>
        /// الدلة تغير كمية unit الفرعية الى الاساسية
        /// </summary>
        /// <param name="itemForChangeQte"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ItemForChangeQte>> GetQteForBasicUnit(IEnumerable<ItemForChangeQte> itemForChangeQte)
        {
            foreach (var item in itemForChangeQte)
            {
                var unit = await GetFirstOrDefault(x => x.Id == item.ItemUnitId);
                // Use the correct cost price conversion method for purchase processing
                item.CostPrice = ItemExtensions.GetCostPriceForBasicUnit(unit, item.CostPrice);
                item.Quantity = ItemExtensions.GetQuantityUnit(unit, item.Quantity);
            }
            return itemForChangeQte;
        }
        /// <summary>
        /// الدلة تغير سعر unit الفرعية الى الاساسية 
        /// </summary>
        /// <param name="itemForChangeQte"></param>
        /// <returns></returns>
        public async Task ChangePriceForBasicUnit(IEnumerable<ItemForChangeQte> itemForChangeQte)
        {
            foreach (var item in itemForChangeQte)
            {
                var unit = await GetFirstOrDefault(x => x.Id == item.ItemUnitId);

                unit.SalePrice = item.SellPrice;
                Update(unit);
                /*Add(unit);*/ // هذا هو سبب المشكلة
            }

        }

    }
}
